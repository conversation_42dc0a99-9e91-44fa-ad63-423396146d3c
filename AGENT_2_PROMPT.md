# Agent 2: Core Components Refactoring

## Mission
Refactor core UI components to use the theme system while maintaining exact visual appearance in light mode and adding dark mode support. Follow the component separation pattern for better maintainability.

## Prerequisites ✅
- Phase 1 infrastructure is complete
- Theme system is available at `app/theme`
- System theme detection implemented (Light/Dark/System modes)
- Component separation pattern documented

## Files to Modify
**Your exclusive files (no conflicts with other agents):**
- `app/components/elements/Button.tsx`
- `app/components/elements/Text.tsx`
- `app/components/Button.tsx`
- `app/assets/styles/CommonStyles.tsx`
- `app/components/elements/FollowButton.tsx`

## Critical Requirements

### 🚨 ZERO VISUAL CHANGES IN LIGHT MODE
- Light mode must look **exactly** like current state
- No pixel differences allowed
- Preserve all existing colors, spacing, fonts
- Maintain all existing functionality and props

### 🎯 Component Separation Pattern
Follow this pattern for all components:

```typescript
// Container Component (handles logic, state, theme)
const ButtonContainer: React.FC<ButtonProps> = (props) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  
  // Logic here...
  
  return (
    <ButtonPresentation
      theme={theme}
      styles={styles}
      {...props}
      onPress={handlePress}
    />
  );
};

// Presentational Component (pure UI)
interface ButtonPresentationProps extends ButtonProps {
  theme: Theme;
  styles: any;
}

const ButtonPresentation: React.FC<ButtonPresentationProps> = ({
  theme,
  styles,
  ...props
}) => {
  return (
    <TouchableOpacity style={styles.button}>
      <Text style={styles.text}>{props.label}</Text>
    </TouchableOpacity>
  );
};

// Export container as default
export default ButtonContainer;
```

## Theme System Usage

### Import Theme Utilities
```typescript
import { useTheme, createThemedStyles } from 'app/theme';
import { responsiveValue as rv } from 'app/providers/responsive-value';
```

### Create Themed Styles
```typescript
const createStyles = createThemedStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
  },
  text: {
    color: theme.colors.text,
    fontSize: rv(16),
    fontFamily: 'medium' as const,
  },
}));
```

### Color Mapping Reference
**Light Mode (Current) → Theme Colors:**
- `#163E23` → `theme.colors.primary`
- `#ECFFD4` → `theme.colors.secondary`
- `#FFFFFF` → `theme.colors.background`
- `#000000` → `theme.colors.text`
- `#696969` → `theme.colors.textSecondary`
- `#F5F5F5` → `theme.colors.border`
- `#F2875D` → `theme.colors.buttonQuaternary`

## Specific Component Tasks

### 1. `app/components/elements/Button.tsx`
**Current Issues to Fix:**
- Hardcoded colors: `#163E23`, `#ECFFD4`, `#F2875D`
- Inline color logic in render method
- Mixed styling approaches

**Required Changes:**
- Convert all hardcoded colors to theme colors
- Separate container/presentation components
- Use `createThemedStyles` utility
- Preserve all button themes (primary, secondary, tertiary, quaternary, quinary)
- Maintain loading states and disabled states

### 2. `app/components/elements/Text.tsx`
**Current Issues:**
- Font family hardcoding
- No theme-aware text colors

**Required Changes:**
- Add theme-aware text colors
- Preserve all text types (regular, medium, bold, etc.)
- Maintain font family system
- Add dark mode text color support

### 3. `app/components/Button.tsx`
**Current Issues:**
- Hardcoded background `#1c1c1c`
- Fixed text color `#fff`

**Required Changes:**
- Convert to theme-based colors
- Separate container/presentation
- Maintain current appearance in light mode

### 4. `app/assets/styles/CommonStyles.tsx`
**Current Issues:**
- Hardcoded colors throughout
- No theme support

**Required Changes:**
- Convert to theme-based function
- Export themed style creators
- Maintain exact current appearance

### 5. `app/components/elements/FollowButton.tsx`
**Current Issues:**
- Hardcoded border color `#FED830`
- Fixed background colors

**Required Changes:**
- Use theme colors
- Separate container/presentation
- Preserve button functionality

## Implementation Steps

### Step 1: Setup Theme Imports
Add theme imports to each file:
```typescript
import { useTheme, createThemedStyles } from 'app/theme';
```

### Step 2: Identify Hardcoded Values
Find all hardcoded colors, spacing, and styling values in each component.

### Step 3: Create Theme Mappings
Map each hardcoded value to appropriate theme color:
```typescript
// Before
backgroundColor: '#163E23'

// After  
backgroundColor: theme.colors.primary
```

### Step 4: Separate Components
Split each component into container and presentational parts following the established pattern.

### Step 5: Create Themed Styles
Use `createThemedStyles` utility for all styling:
```typescript
const createStyles = createThemedStyles((theme) => ({
  // styles using theme values
}));
```

### Step 6: Test Both Themes
- Verify light mode looks identical to current state
- Verify dark mode renders correctly
- Test all component variants and states

## Testing Requirements

### Visual Regression Testing
```typescript
// Add to any screen temporarily for testing
import { ThemeToggleButton } from 'app/theme';

// In render:
<ThemeToggleButton />
```

### Component State Testing
Test all component states:
- Default state
- Loading state
- Disabled state
- Pressed state
- All button themes (primary, secondary, etc.)

## Quality Checklist

### ✅ Before Completion
- [ ] All hardcoded colors replaced with theme colors
- [ ] Light mode appears identical to current state
- [ ] Dark mode renders correctly
- [ ] All component variants work (primary, secondary, etc.)
- [ ] Loading and disabled states work
- [ ] TypeScript compiles without errors
- [ ] Component separation pattern followed
- [ ] Existing props and APIs preserved
- [ ] No breaking changes to component usage

### ✅ Code Quality
- [ ] Used `createThemedStyles` utility
- [ ] Proper TypeScript types
- [ ] Container/presentation separation
- [ ] Theme imports from `app/theme`
- [ ] Responsive values preserved (`rv()`)
- [ ] Font families preserved

## Common Pitfalls to Avoid

1. **Don't change component APIs** - Preserve all existing props
2. **Don't modify spacing** - Keep exact current spacing in light mode
3. **Don't change font sizes** - Preserve all current font sizes
4. **Don't alter animations** - Keep existing loading animations
5. **Don't break existing usage** - Components should work exactly as before

## Success Criteria

1. **Zero Visual Changes**: Light mode looks identical to current state
2. **Full Dark Mode**: All components render correctly in dark mode
3. **Component Separation**: Clean container/presentation split
4. **Type Safety**: Full TypeScript support maintained
5. **Performance**: No performance regressions
6. **Maintainability**: Improved code organization

## Next Phase Dependencies

Your work enables:
- **Phase 3-9**: Screen refactoring (can run in parallel after your completion)
- **Phase 10**: Navigation theming (depends on your themed components)

## Support Resources

- **Documentation**: `app/docs/THEME_SYSTEM.md`
- **Examples**: `app/components/examples/ThemedComponentExample.tsx`
- **Theme Reference**: `app/theme/index.ts`
- **Testing**: `app/components/ThemeToggleButton.tsx`

Complete this phase to enable parallel execution of all screen refactoring phases!
