# Phase 6: Groups and Social Features - Testing Guide

## 🧪 Testing Implementation

This guide provides comprehensive testing instructions for the Phase 6 refactored components to ensure they work correctly in both light and dark modes.

## 📋 Pre-Testing Checklist

### ✅ Prerequisites
- [ ] Phase 1 (Theme Infrastructure) is complete
- [ ] Phase 2 (Core Components) is complete
- [ ] Theme system is available at `app/theme`
- [ ] All Phase 6 files have been refactored

### ✅ Files to Test
- [ ] `app/screens/bottom/groups/index.tsx`
- [ ] `app/screens/bottom/groups/my-groups.tsx`
- [ ] `app/screens/bottom/groups/recommended-groups.tsx`
- [ ] `app/screens/bottom/convo.tsx`
- [ ] `app/screens/bottom/convo-notification.tsx`

## 🎯 Testing Scenarios

### 1. Groups Main Screen (`index.tsx`)
**Test Cases:**
- [ ] **Light Mode Verification**
  - Screen background is white
  - Tab text is black
  - Active tab has yellow accent border (`#FFCB05`)
  - Floating action button displays correctly
  
- [ ] **Dark Mode Verification**
  - Screen background is dark (`#121212`)
  - Tab text is white
  - Active tab has yellow accent border (adjusted for dark mode)
  - Floating action button displays correctly

- [ ] **Functionality Tests**
  - [ ] Tab switching between "My Groups" and "Join Groups" works
  - [ ] Floating action button navigates to CreateGroup screen
  - [ ] Theme switching updates colors in real-time

### 2. My Groups Screen (`my-groups.tsx`)
**Test Cases:**
- [ ] **Light Mode Verification**
  - Background is white
  - Loading spinner displays correctly
  - Empty state SVGs display correctly
  
- [ ] **Dark Mode Verification**
  - Background is dark
  - Loading spinner displays correctly
  - Empty state SVGs display correctly

- [ ] **Functionality Tests**
  - [ ] Groups list loads and displays correctly
  - [ ] Pull-to-refresh functionality works
  - [ ] Group cards are clickable and navigate correctly
  - [ ] Empty state displays when no groups exist

### 3. Recommended Groups Screen (`recommended-groups.tsx`)
**Test Cases:**
- [ ] **Light Mode Verification**
  - Background is white
  - Loading states display correctly
  - Empty state displays correctly
  
- [ ] **Dark Mode Verification**
  - Background is dark
  - Loading states display correctly
  - Empty state displays correctly

- [ ] **Functionality Tests**
  - [ ] Recommended groups load correctly
  - [ ] Pull-to-refresh functionality works
  - [ ] Group cards display with "recommended" flag
  - [ ] Focus effect triggers data refresh

### 4. Conversations Screen (`convo.tsx`)
**Test Cases:**
- [ ] **Light Mode Verification**
  - SafeAreaView background is white
  - Status bar style is 'dark-content'
  - Header text is black
  - Back button icon is black
  
- [ ] **Dark Mode Verification**
  - SafeAreaView background is dark
  - Status bar style is 'light-content'
  - Header text is white
  - Back button icon is white

- [ ] **Functionality Tests**
  - [ ] Conversations list loads correctly
  - [ ] Back button navigation works
  - [ ] Pull-to-refresh functionality works
  - [ ] TopicCard components display correctly
  - [ ] Unread notifications are cleared properly

### 5. Conversation Notification (`convo-notification.tsx`)
**Test Cases:**
- [ ] **Light Mode Verification**
  - Container background matches theme
  - Notification icon displays correctly
  
- [ ] **Dark Mode Verification**
  - Container background matches theme
  - Notification icon displays correctly

- [ ] **Functionality Tests**
  - [ ] Notification count displays correctly
  - [ ] Tap navigation to convos screen works
  - [ ] Count parameter is passed correctly

## 🔧 Testing Tools

### Theme Toggle Button
Add this to any screen for testing:
```typescript
import { ThemeToggleButton } from 'app/theme';

// In your render method:
<ThemeToggleButton />
```

### Manual Theme Testing
```typescript
import { useTheme } from 'app/theme';

const TestComponent = () => {
  const { toggleTheme, themeMode } = useTheme();
  
  return (
    <TouchableOpacity onPress={toggleTheme}>
      <Text>Current Theme: {themeMode}</Text>
    </TouchableOpacity>
  );
};
```

## 🐛 Common Issues to Check

### Visual Issues
- [ ] **Color Inconsistencies**: Ensure all hardcoded colors are replaced
- [ ] **Text Readability**: Verify text contrast in both themes
- [ ] **Icon Colors**: Check that icons adapt to theme colors
- [ ] **Border Colors**: Ensure borders are visible in both themes

### Functional Issues
- [ ] **Navigation**: All navigation flows work correctly
- [ ] **State Management**: Loading and error states display properly
- [ ] **Data Fetching**: API calls and data display work correctly
- [ ] **Refresh Logic**: Pull-to-refresh functionality works

### Performance Issues
- [ ] **Theme Switching**: No lag when switching themes
- [ ] **Memory Leaks**: No memory issues with theme changes
- [ ] **Render Performance**: No unnecessary re-renders

## 📊 Testing Checklist

### Light Mode Testing ✅
- [ ] All screens display with white backgrounds
- [ ] Text is black and readable
- [ ] Icons are properly colored
- [ ] Borders and dividers are visible
- [ ] Loading states work correctly
- [ ] Empty states display properly

### Dark Mode Testing ✅
- [ ] All screens display with dark backgrounds
- [ ] Text is white and readable
- [ ] Icons adapt to dark theme
- [ ] Borders and dividers are visible
- [ ] Loading states work correctly
- [ ] Empty states display properly

### Functionality Testing ✅
- [ ] All navigation works
- [ ] Data loading works
- [ ] Refresh functionality works
- [ ] Error handling works
- [ ] State management works

### Integration Testing ✅
- [ ] Group cards display correctly
- [ ] Topic cards display correctly
- [ ] Notification system works
- [ ] Redux integration works
- [ ] Translation system works

## 🚀 Performance Testing

### Theme Switching Performance
1. Open groups screen
2. Toggle theme multiple times rapidly
3. Verify no lag or visual glitches
4. Check memory usage remains stable

### Data Loading Performance
1. Test with large groups lists
2. Verify smooth scrolling
3. Check loading states display quickly
4. Ensure refresh doesn't cause UI freezing

## ✅ Success Criteria

### Visual Criteria
- [ ] Light mode looks identical to pre-refactor state
- [ ] Dark mode provides good contrast and readability
- [ ] All UI elements are properly themed
- [ ] No visual regressions or glitches

### Functional Criteria
- [ ] All existing functionality preserved
- [ ] Navigation flows work correctly
- [ ] Data loading and refresh work
- [ ] Error handling works properly

### Performance Criteria
- [ ] No performance regressions
- [ ] Theme switching is smooth
- [ ] Memory usage is stable
- [ ] App remains responsive

## 📝 Test Results Documentation

Create a test results document with:
- Screenshots of light/dark mode for each screen
- Performance metrics before/after
- Any issues found and their resolutions
- Confirmation that all success criteria are met

## 🎉 Completion Verification

Phase 6 is complete when:
- [ ] All test cases pass
- [ ] Visual regression testing shows no changes in light mode
- [ ] Dark mode renders correctly on all screens
- [ ] Performance testing shows no regressions
- [ ] All functionality works as expected
- [ ] Code review confirms proper implementation

Ready for Phase 7 when all criteria are met! 🚀
