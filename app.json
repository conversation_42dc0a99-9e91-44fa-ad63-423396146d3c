{"expo": {"name": "Connectify", "slug": "connectify", "version": "1.0.0", "updates": {"url": "https://u.expo.dev/fb6b83ee-9c0d-4d37-9344-e65518da0a25", "enabled": true, "fallbackToCacheTimeout": 0}, "plugins": [["expo-camera", {"cameraPermission": "Connectify needs camera access to let you take photos for your profile, share images in chats, and scan QR codes", "microphonePermission": "Connectify needs microphone access to let you record voice messages and make voice calls in chats", "recordAudioAndroid": true}], ["expo-image-picker", {"photosPermission": "Connectify needs access to your photos to let you share images in chats, set profile pictures, and save shared media", "cameraPermission": "Connectify needs camera access to let you take photos for sharing in chats and updating your profile"}], ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], ["expo-contacts", {"contactsPermission": "Connectify needs contacts access to help you find and connect with your friends who are also using the app"}], ["expo-notifications", {"icon": "app/assets/connectify-thumbnail.png", "color": "#ffffff"}], ["expo-media-library", {"photosPermission": "Connectify needs access to your photos to let you share images and save media from chats", "savePhotosPermission": "Connectify needs permission to save photos to your gallery when you download images from chats", "isAccessMediaLocationEnabled": true}]], "orientation": "portrait", "icon": "app/assets/connectify-thumbnail.png", "userInterfaceStyle": "automatic", "backgroundColor": "#000000", "splash": {"image": "app/assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSContactsUsageDescription": "Connectify needs contacts access to help you find and connect with your friends who are also using the app", "NSPhotoLibraryAddUsageDescription": "Connectify needs permission to save photos to your gallery when you download images from chats", "ITSAppUsesNonExemptEncryption": false}, "bundleIdentifier": "com.connectify.app", "buildNumber": "1.0.0"}, "android": {"adaptiveIcon": {"foregroundImage": "app/assets/connectify-thumbnail.png", "backgroundColor": "#ffffff"}, "googleServicesFile": "google-services.json", "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_CONTACTS", "android.permission.WRITE_CONTACTS", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION"], "package": "com.connectify.app", "versionCode": 38}, "notification": {"icon": "app/assets/connectify-thumbnail.png"}, "web": {"favicon": "app/assets/favicon.png"}, "scheme": "connectify", "owner": "ibkay998", "runtimeVersion": {"policy": "appVersion"}, "extra": {"eas": {"projectId": "fb6b83ee-9c0d-4d37-9344-e65518da0a25"}}}}