import { registerRootComponent } from 'expo';
import 'react-native-gesture-handler';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AppStack from './navigation';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from 'app/redux';
import { MenuProvider } from 'react-native-popup-menu';
import { Provider } from 'react-redux';
import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { persistQueryClient } from '@tanstack/react-query-persist-client';
import { createAsyncStoragePersister } from '@tanstack/query-async-storage-persister';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { GlobalProvider } from './GlobalProvider';
import Entypo from '@expo/vector-icons/Entypo';
import { queryClient } from 'app/redux/user/hooks';
import * as Font from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import useFonts from './hooks/useFont';
import { useState, useRef, useEffect, useCallback } from 'react';
import Constants from 'expo-constants';
import * as Notifications from 'expo-notifications';

import { RootSiblingParent } from 'react-native-root-siblings';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { useTranslation } from 'react-i18next';
import enTranslations from 'app/translation/en.json';
import frTranslations from 'app/translation/fr.json';
import arTranslations from 'app/translation/ar.json';
import esTranslations from 'app/translation/es.json';
import ptTranslations from 'app/translation/pt.json';
import { PROJECT_ID } from './constants/expo';
import { registerDeviceToken } from 'app/helpers/queries';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

import { setCountry } from 'app/redux/main/reducer';
import { ActivityProvider } from './providers/ActivityContext';
import { ThemeProvider } from './providers/ThemeProvider';
import { FontSizeProvider } from './providers/FontSizeProvider';
import Orientation from 'react-native-orientation-locker';
import { OptimizedFontSizeProvider } from './providers/OptimizedFontSizeProvider';
import { warmupCriticalData } from './services/cacheWarmingService';

// **Import for error handling**

import { logUserError, logCrashReport } from 'app/api/user';
import { prepareErrorPayload, prepareCrashPayload } from 'app/utils/errorUtils';
export const initializeCountry = async (dispatch: any) => {
  try {
    const storedCountry = await AsyncStorage.getItem('selectedCountry');
    if (storedCountry === null) {
      await AsyncStorage.setItem('selectedCountry', 'GLO');
      dispatch(setCountry('GLO'));
    } else {
      dispatch(setCountry(storedCountry));
    }
  } catch (error) {
    console.error('Failed to initialize country:', error);
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: enTranslations },
      ar: { translation: arTranslations },
      es: { translation: esTranslations },
      fr: { translation: frTranslations },
      pt: { translation: ptTranslations },
      // Add other languages and their translations here
    },
    lng: 'en', // Set the default language
    fallbackLng: 'en', // Fallback to English if a translation is missing
    interpolation: {
      escapeValue: false, // React already escapes text
    },
  });

export { i18n };

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});
SplashScreen.preventAutoHideAsync();



// **Global error handler function**
function globalErrorHandler(error: any, isFatal?: boolean) {
  const payload = prepareErrorPayload(error);

  // Send error log to backend
  logUserError(payload).catch((err: any) => {
    console.error('Failed to send error log:', err);
  });

  if (isFatal) {
    const crashPayload = prepareCrashPayload(
      error,
      Platform.OS === 'ios' ? 'iOS' : 'Android',
      '1.0.0', // Replace with your app version or import from your app config
      Device.modelName || 'Unknown Device',
      Platform.Version.toString()
    );

    // Send crash report to backend
    logCrashReport(crashPayload).catch((err: any) => {
      console.error('Failed to send crash report:', err);
    });

    // Optionally, you can display a custom error screen or restart the app
  }
}

// **Set the global error handler**
ErrorUtils.setGlobalHandler(globalErrorHandler);

function App() {
  const [appIsReady, setAppIsReady] = useState(false);
  const [expoPushToken, setExpoPushToken] = useState('');
  const dispatch = store.dispatch;

  // Lock app to portrait orientation globally
  useEffect(() => {
    Orientation.lockToPortrait();
  }, []);

  useEffect(() => {
    initializeCountry(dispatch);
  }, [dispatch]);

  useEffect(() => {
    async function prepare() {
      try {
        // Pre-load fonts and other assets
        await useFonts();

        // Additional loading tasks can be done here
        await new Promise((resolve) => setTimeout(resolve, 2000));
      } catch (e) {
        console.warn(e);
      } finally {
        setAppIsReady(true);

        // Start cache warming after app is ready
        setTimeout(() => {
          warmupCriticalData();
        }, 1000); // Wait 1 second after app is ready
      }
    }

    prepare();
  }, []);

  const onLayoutRootView = useCallback(async () => {
    if (appIsReady) {
      await SplashScreen.hideAsync(); // Hide the splash screen immediately after layout
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null; // Render nothing until the app is ready
  }

  const persister = createAsyncStoragePersister({
    storage: AsyncStorage,
  });

  persistQueryClient({
    queryClient,
    persister,
  });

  const menuProviderStyles = {
    menuProviderWrapper: {
      flexDirection: 'column' as const,
    },
    backdrop: {
      backgroundColor: 'black',
      opacity: 0.2,
    },
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <PersistQueryClientProvider
        client={queryClient}
        persistOptions={{ persister }}
        onSuccess={() => {
          console.log('rehydrated');
        }}
      >
        <Provider store={store}>
          <PersistGate loading={<Text>Loading...</Text>} persistor={persistor}>
            <ThemeProvider>
              <FontSizeProvider>
                <OptimizedFontSizeProvider>
                  <GlobalProvider>
                    <ActivityProvider>
                    <MenuProvider customStyles={menuProviderStyles}>
                      <RootSiblingParent>
                        <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
                          <AppStack />
                        </View>
                      </RootSiblingParent>
                      <Toast />
                    </MenuProvider>
                    </ActivityProvider>
                  </GlobalProvider>
                </OptimizedFontSizeProvider>
              </FontSizeProvider>
            </ThemeProvider>
          </PersistGate>
        </Provider>
      </PersistQueryClientProvider>
    </SafeAreaView>
  );
}

export default registerRootComponent(App);
