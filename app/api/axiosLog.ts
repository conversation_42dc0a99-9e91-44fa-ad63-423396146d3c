import axios from 'axios';
import { store } from '../redux';

const MAIN_URL: string =
  'https://pennytot-backend-development.up.railway.app';

// Create a separate Axios instance for logging
const AxiosLogger = axios.create({
  baseURL: MAIN_URL,
  timeout: 40000,
  headers: {},
});

// Add authorization interceptor to AxiosLogger
AxiosLogger.interceptors.request.use(function (config: any) {
  const token = store.getState().user?.token;
  config.headers.Authorization = token ? `Bearer ${token}` : '';
  return config;
});

export const logUserError = async (payload: any) => {
  const { data } = await AxiosLogger.post('/log/error-log/', payload);
  return data;
};

// Export other API functions as needed
