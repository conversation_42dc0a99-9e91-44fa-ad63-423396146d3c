import { Axios } from './axios';

const getCredit = async () => {
  const { data } = await Axios.get('/credit/get-credit');
  return data.credit;
};

const getSubscription = async () => {
  const { data } = await Axios.get('/subscription/get-subscription');
  return data;
};

const getFreeCredit = async () => {
  const { data } = await Axios.get('/credit/get-free-credit');
  return data;
};

const getFreeSubscription = async () => {
  const { data } = await Axios.get('/subscription/get-free-subscription');
  return data;
};

const buyCredit = async (data: any) => {
  return await Axios.post('/credit/buy', data);
};

const buySubscription = async (data: any) => {
  return await Axios.post('/subscription/buy', data);
};

const restoreSubscription = async (data: any) => {
  const { data: responseData } = await Axios.post('/subscription/restore-purchases', data);
  return responseData;
};

const getTransactionHistory = async () => {
  const { data } = await Axios.get('/credit/transaction-history');
  return data.transactions.docs;
};

export const credit = {
  buyCredit,
  getCredit,
  getFreeCredit,
  getTransactionHistory,
  getSubscription,
  buySubscription,
  getFreeSubscription,
  restoreSubscription
};
