import { Axios } from './axios';


const getTags = () => {
  return Axios.get('/tags/list');
};

const getGroup = async (groupId: string) => {
  const { data } = await Axios.get('/group/view/' + groupId);

  return data;
};

const getMyGroups = async () => {
  const { data } = await Axios.get('/group/my-groups');
  return data.groups.docs;
};


const fetchUserGroups = async (userDetails: any) => {
  const response = await Axios({
    method: 'GET',
    url: `/group/user-groups/${userDetails._id}?page=1&limit=50`,
  });
  return response.data.groups.docs;
};
const getSuggestedGroups = async () => {
  const { data } = await Axios.get('/group/suggested-groups');
  return data.groups.docs;
};


const updateGroup = async (payload: any) => {
  const { data } = await Axios.patch(
    '/group/update/' + payload.id,
    payload.data,
  );

  return data;
};

const updateGroupPicture = async (payload: any) => {
  const { data } = await Axios.patch(
    '/group/update-group-picture/' + payload.id,
    payload.data,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
    }
  );

  return data;
};

const reportGroup = async (groupId: string,description: string) =>{
  const { data } = await Axios.post('/group/flag/' + groupId, {description});
  return data;
}

const createGroup = async (payload: any) =>{
  '/group/create/' 
    payload.data,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
    }
  }


export const group = {
  getGroup,
  getMyGroups,
  getSuggestedGroups,
  updateGroup,
  updateGroupPicture,
  reportGroup,
  createGroup, 
  fetchUserGroups,
  getTags
};
