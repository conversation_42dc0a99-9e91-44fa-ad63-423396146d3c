import { Axios } from 'app/api/axios';

export interface NewsletterSettings {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'bi-weekly' | 'monthly';
  dayOfWeek?: number;
  timeOfDay?: number;
  timezone?: string;
  contentPreferences: {
    includeFollowingPosts: boolean;
    includePopularPosts: boolean;
    includeGroupActivity: boolean;
    includeTrendingTopics: boolean;
    maxPostsPerSection?: number;
  };
  lastSentAt?: Date;
  nextScheduledAt?: Date;
}

export interface UpdateNewsletterSettingsPayload extends Partial<NewsletterSettings> {}

const getNewsletterSettings = async () => {
  const { data } = await Axios.get('/email-digest/settings');
  return data;
};

const updateNewsletterSettings = async (payload: UpdateNewsletterSettingsPayload) => {
  const { data } = await Axios.put('/email-digest/settings', payload);
  return data;
};

const subscribeToNewsletter = async () => {
  const { data } = await Axios.post('/email-digest/subscribe');
  return data;
};

const unsubscribeFromNewsletter = async () => {
  const { data } = await Axios.post('/email-digest/unsubscribe');
  return data;
};

const previewNewsletter = async () => {
  const { data } = await Axios.get('/email-digest/preview');
  return data;
};

const sendNewsletterNow = async () => {
  const { data } = await Axios.post('/email-digest/send-now');
  return data;
};

export const newsletter = {
  getNewsletterSettings,
  updateNewsletterSettings,
  subscribeToNewsletter,
  unsubscribeFromNewsletter,
  previewNewsletter,
  sendNewsletterNow,
};
