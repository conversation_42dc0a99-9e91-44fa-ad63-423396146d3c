import { IncreaseOrReduce} from 'app/redux/quiz/types';
import { Axios } from './axios';
import { REDUCE_PENNYTOTS } from 'app/Data/constants';
import { INCREASE_PENNYTOTS } from 'app/Data/constants';





const getQuestionsOld = async () => {
  const { data } = await Axios.get('/quiz/random');
  return data;
};

const getQuestions = async (country:any) => {
  const { data } = await Axios.get(`/quiz/random/${country}`);
  console.log(country,"datasstr")
  return data;
};
//deduct Pennytot
const changeUserPennyTots = async (type:"reduce" | "increase") => {
  if (type === 'reduce'){
    const { data } = await Axios.get(`/quiz/reduce/${REDUCE_PENNYTOTS}`);
    return data;
  }
  if (type === 'increase'){
    const { data } = await Axios.get(`/quiz/increase/${INCREASE_PENNYTOTS}`);
    return data;
  }
};

const stakeUserPennyTots = async (type:"reduce" | "increase",amount:number) => {
  if (type === 'reduce'){
    const { data } = await Axios.get(`/quiz/reduce/${amount}`);
    return data;
  }
  if (type === 'increase'){
    const newAmount: number = amount;
    const { data } = await Axios.get(`/quiz/increase/${newAmount}`);
    return data;
  }
};

export const quiz = {
  getQuestions,
  getQuestionsOld,
  changeUserPennyTots,
  stakeUserPennyTots
};
