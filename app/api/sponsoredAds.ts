import { Axios } from './axios';

const getSponsoredAd = async () => {
  console.log("I started ibkzz")
  const { data } = await Axios.get('/sponsored/ads/display');
  console.log(data,"ibkz")
  // The response structure should be { ad: {...} } based on your backend.
  // You can return data directly or data.ad depending on how you want to handle it in the hook.
  return data; 
};

export const sponsoredAd = {
  getSponsoredAd,
};
