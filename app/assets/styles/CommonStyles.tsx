import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

/**
 * Common styles using the theme system
 * This hook provides themed styles that automatically adapt to light/dark mode
 */
// export const useCommonStyles = () => {
//     const { theme } = useTheme();
//   const { fs } = useOptimizedFontSize();
//   const styles = createStyles(theme, fs);

//   return createThemedStyles((theme) => ({
//     inputField: {
//       height: rv(50),
//       paddingHorizontal: 20,
//       backgroundColor: theme.colors.inputBackground,
//       marginBottom: 15,
//       borderStyle: 'solid' as const,
//       marginTop: 10,
//       fontSize: rv(13),
//       fontFamily: 'medium',
//       borderWidth: 2,
//       borderColor: theme.colors.inputBorder,
//       color: theme.colors.text, // Fixed: use theme.colors.text instead of textPlaceholder for better readability
//     },
//     editInputField: {
//       borderBottomColor: theme.colors.primary,
//       borderBottomWidth: 1,
//       padding: 5,
//       backgroundColor: theme.colors.background,
//       marginBottom: 15,
//     },
//     editInputColor: {
//       borderBottomColor: theme.colors.borderLight,
//       color: theme.colors.textSecondary,
//       fontFamily: 'regular'
//     },
//     topicStyle: {
//       flex: 1,
//       flexDirection: 'row',
//       justifyContent: 'flex-start',
//       alignItems: 'flex-start',
//       backgroundColor: theme.colors.background,
//       paddingTop: 5,
//       padding: 20,
//     },
//   }))(theme);
// };

// // Alternative: Direct themed styles function (for use without hook)
// export const createCommonStyles = createThemedStyles((theme) => ({
//   inputField: {
//     height: rv(50),
//     paddingHorizontal: 20,
//     backgroundColor: theme.colors.inputBackground,
//     marginBottom: 15,
//     borderStyle: 'solid' as const,
//     marginTop: 10,
//     fontSize: rv(13),
//     fontFamily: 'medium',
//     borderWidth: 2,
//     borderColor: theme.colors.inputBorder,
//     color: theme.colors.text, // Fixed: use theme.colors.text instead of textPlaceholder
//   },
//   editInputField: {
//     borderBottomColor: theme.colors.primary,
//     borderBottomWidth: 1,
//     padding: 5,
//     backgroundColor: theme.colors.background,
//     marginBottom: 15,
//   },
//   editInputColor: {
//     borderBottomColor: theme.colors.borderLight,
//     color: theme.colors.textSecondary,
//     fontFamily: 'regular'
//   },
//   topicStyle: {
//     flex: 1,
//     flexDirection: 'row',
//     justifyContent: 'flex-start',
//     alignItems: 'flex-start',
//     backgroundColor: theme.colors.background,
//     paddingTop: 5,
//     padding: 20,
//   },
// }));

// // Export default as the hook for easier usage
// export default useCommonStyles;
export const useCommonStyles = () => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();

  const styles = createCommonStyles(theme, fs);
  return styles;
};


export const createCommonStyles = createOptimizedThemedStyles((theme, fs) => ({
  inputField: {
    height: rv(50),
    paddingHorizontal: 20,
    backgroundColor: theme.colors.inputBackground,
    marginBottom: 15,
    borderStyle: 'solid' as const,
    marginTop: 10,
    fontSize: fs('MD'), // Replacing rv(13)
    fontFamily: 'medium',
    borderWidth: 2,
    borderColor: theme.colors.inputBorder,
    color: theme.colors.text,
  },
  editInputField: {
    borderBottomColor: theme.colors.primary,
    borderBottomWidth: 1,
    padding: 5,
    backgroundColor: theme.colors.background,
    marginBottom: 15,
  },
  editInputColor: {
    borderBottomColor: theme.colors.borderLight,
    color: theme.colors.textSecondary,
    fontFamily: 'regular',
  },
  topicStyle: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.background,
    paddingTop: 5,
    padding: 20,
  },
}));


export default useCommonStyles;
