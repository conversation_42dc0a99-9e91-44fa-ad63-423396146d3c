import { ThemeColors } from 'app/types/theme';

// Light mode colors - maintaining exact current appearance
const lightColors: ThemeColors = {
  // Primary brand colors (current green theme)
  primary: '#163E23',
  primaryLight: '#4CAF50',
  primaryDark: '#0D2818',

  // Secondary colors
  secondary: '#ECFFD4',
  secondaryLight: '#F5FFE8',

  // Background colors
  background: '#FFFFFF',
  surface: '#FFFFFF',
  surfaceVariant: '#F5F5F5',

  // Text colors
  text: '#000000',
  textSecondary: '#696969',
  textDisabled: '#B0B0B0',
  textPlaceholder: '#5A5E5C',

  // Interactive colors
  accent: '#FED830',
  error: '#F44336',
  warning: '#FF9800',
  success: '#4CAF50',
  info: '#797978',

  // Border and divider colors
  border: '#F5F5F5',
  borderLight: '#E8E8E8',
  divider: '#E0E0E0',

  // Component-specific colors
  cardBackground: '#FFFFFF',
  inputBackground: 'transparent',
  inputBorder: '#F5F5F5',
  buttonPrimary: '#163E23',
  buttonSecondary: '#ECFFD4',
  buttonTertiary: '#FFFFFF',
  buttonQuaternary: '#F2875D',
  buttonQuinary: '#163E23',

  // Navigation colors
  tabBarBackground: '#FFFFFF',
  tabBarActive: '#163E23',
  tabBarInactive: '#696969',
  headerBackground: '#FFFFFF',
  headerText: '#000000',

  // Status and feedback colors
  statusBarStyle: 'dark-content' as const,
  overlayBackground: 'rgba(0, 0, 0, 0.2)',
  shadowColor: '#000000',

  // Game and interactive elements
  gameBackground: '#FFFFFF',
  wheelColors: ['#FED830', '#1B1B1B', '#FED830', '#1B1B1B', '#FED830', '#1B1B1B', '#FED830', '#1B1B1B'],
  spinWheelPrimary: '#FED830',
  spinWheelSecondary: '#1B1B1B',

  // Chat bubble colors
  chatBubbleMy: '#E5E5E5',
  chatBubbleOther: '#4F4F4F',
  chatTextMy: '#000000',
  chatTextOther: '#FFFFFF',
};

// Dark mode colors
const darkColors: ThemeColors = {
  // Primary brand colors (adjusted for dark theme)
  primary: '#4CAF50',
  primaryLight: '#81C784',
  primaryDark: '#2E7D32',

  // Secondary colors
  secondary: '#2E7D32',
  secondaryLight: '#4CAF50',

  // Background colors
  background: '#121212',
  surface: '#1E1E1E',
  surfaceVariant: '#2C2C2C',

  // Text colors
  text: '#FFFFFF',
  textSecondary: '#B0B0B0',
  textDisabled: '#666666',
  textPlaceholder: '#888888',

  // Interactive colors
  accent: '#FFD54F',
  error: '#F48FB1',
  warning: '#FFB74D',
  success: '#81C784',
  info: '#90A4AE',

  // Border and divider colors
  border: '#333333',
  borderLight: '#444444',
  divider: '#2C2C2C',

  // Component-specific colors
  cardBackground: '#1E1E1E',
  inputBackground: 'transparent',
  inputBorder: '#333333',
  buttonPrimary: '#4CAF50',
  buttonSecondary: '#2E7D32',
  buttonTertiary: '#1E1E1E',
  buttonQuaternary: '#FF8A65',
  buttonQuinary: '#4CAF50',

  // Navigation colors
  tabBarBackground: '#1E1E1E',
  tabBarActive: '#4CAF50',
  tabBarInactive: '#B0B0B0',
  headerBackground: '#1E1E1E',
  headerText: '#FFFFFF',

  // Status and feedback colors
  statusBarStyle: 'light-content' as const,
  overlayBackground: 'rgba(0, 0, 0, 0.6)',
  shadowColor: '#000000',

  // Game and interactive elements
  gameBackground: '#1E1E1E',
  wheelColors: ['#FFD54F', '#333333', '#FFD54F', '#333333', '#FFD54F', '#333333', '#FFD54F', '#333333'],
  spinWheelPrimary: '#FFD54F',
  spinWheelSecondary: '#333333',

  // Chat bubble colors
  chatBubbleMy: '#4CAF50',
  chatBubbleOther: '#333333',
  chatTextMy: '#FFFFFF',
  chatTextOther: '#FFFFFF',
};

// Common spacing values
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Common border radius values
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  round: 50,
};

// Shadow configurations
export const shadows = {
  small: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};

const colors = {
  light: lightColors,
  dark: darkColors,
};

export default colors;
