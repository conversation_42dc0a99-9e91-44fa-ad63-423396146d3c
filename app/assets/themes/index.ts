import { Theme } from 'app/types/theme';
import colors, { spacing, borderRadius, shadows } from './colors';

// Create complete theme objects
export const lightTheme: Theme = {
  mode: 'light',
  colors: colors.light,
  spacing,
  borderRadius,
  shadows,
};

export const darkTheme: Theme = {
  mode: 'dark',
  colors: colors.dark,
  spacing,
  borderRadius,
  shadows,
};

// Theme getter function
export const getTheme = (mode: 'light' | 'dark'): Theme => {
  return mode === 'dark' ? darkTheme : lightTheme;
};

// Export individual theme parts for direct access
export { colors, spacing, borderRadius, shadows };

// Default export
export default {
  light: lightTheme,
  dark: darkTheme,
  getTheme,
};
