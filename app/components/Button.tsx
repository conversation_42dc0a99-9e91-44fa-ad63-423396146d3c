import React from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import TrackedTouchableOpacity from './TrackTouchable';
import { useTheme, createThemedStyles, createOptimizedThemedStyles } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

type ButtonProps = {
  onPress: () => void;
  label: string;
};

// Presentation component props
interface ButtonPresentationProps extends ButtonProps {
  theme: any;
  styles: any;
}

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    backgroundColor: theme.colors.surface,
    paddingVertical: rv(16),
    paddingHorizontal: 32,
    alignSelf: 'center',
    borderRadius: 32,
  },
  text: {
    textAlign: 'center',
    fontSize: fs("MD"),
    color: theme.colors.text,
    fontFamily: 'bold',
    letterSpacing: 1,
  },
}));

// Container Component (handles logic, state, theme)
const ButtonContainer = (props: ButtonProps) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  return (
    <ButtonPresentation
      theme={theme}
      styles={styles}
      {...props}
    />
  );
};

// Presentational Component (pure UI)
const ButtonPresentation = ({ theme, styles, onPress, label }: ButtonPresentationProps) => {
  return (
    <TrackedTouchableOpacity onPress={onPress} style={styles.container}>
      <Text style={styles.text}>{label}</Text>
    </TrackedTouchableOpacity>
  );
};

// Export container as default
export const Button = ButtonContainer;


