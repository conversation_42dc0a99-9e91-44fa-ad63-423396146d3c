import React, { FunctionComponent } from 'react';
import { View, ListRenderItem } from 'react-native';
import { CustomText } from '../elements';
import Avatar from '../elements/Avater';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme } from 'app/hooks/useTheme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
import { createOptimizedThemedStyles } from 'app/theme';

type GroupParticipantItemProps = {
  item: AccountProfileProp;
  navigation: any;
};

const GroupParticipantItem: FunctionComponent<GroupParticipantItemProps> = ({
  item,
  navigation,
}) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  return (
    <View
      style={{
        flex: 1,
        flexDirection: 'row',
        minHeight: 60,
        alignItems: 'flex-start'
        // marginTop: 5,
       
      }}
    >
      <View
        style={{
          width: '14%',
          marginRight: 10
        }}
      >
        <Avatar size={45} source={item.profile_picture} />
      </View>
      <View
        style={{
          width: '80%',
          flexDirection: 'column',
          justifyContent: 'center',
        }}
      >
        <CustomText
          style={[
            styles.font_thirteen,{
         
            fontFamily: 'bold'

            // marginBottom: 10
          }]}
          // textType='bold'
        >
          {item.first_name} {item.last_name}
        </CustomText>
        <View style={{ width: '100%' }}>
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={2}
            style={[
              styles.font_eleven,{
              flexDirection: 'row',

              width: '80%',
              color: '#9D9D9D',
              fontFamily: 'medium'

            }]}
          >
            {item.company_position && item.company
                                    ? `${
                                        item?.company_position?.replace(
                                          /(\r\n|\n|\r)/gm,
                                          ''
                                        ) || 'position'
                                      } at ${item.company}`
                                    : 'Position at company'}
          </CustomText>
        </View>

        {/* <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <FastImage
            source={require('../../assets/single_user.png')}
            style={{
              width: 18,
              height: 18,
            }}
            resizeMode={FastImage.resizeMode.stretch}
          />

          {/* <CustomText
            style={{
              marginTop: 2,
              marginLeft: 4,
              color: '#696969',
            }}
          >
            About me: {item.bio}
          </CustomText> 
      </View> */}
      </View>
    </View>
  );
};

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
font_thirteen:{
  fontSize: fs("MD"),
},
font_eleven:{
  fontSize: fs("SM"),
}
}))

export default GroupParticipantItem;
