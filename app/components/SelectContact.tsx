import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Modal,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import ContactHelper from 'app/helpers/ContactHelper';
import { responsiveValue } from 'app/providers/responsive-value';

const Loading = require('app/assets/loading.gif');

export default function SelectContact(props: any) {
  const [contacts, setContacts] = useState([]);
 
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    async function loadContacts() {
       setIsLoading(true);
      if (props.show) {
      
        try {
          let getContacts: any = await ContactHelper.getAllContacts();
          setContacts(getContacts);
        } catch (error) {
          console.error('Error loading contacts:', error);
        } finally {
          setIsLoading(false);
        }
      }
    }

    loadContacts();
  }, [props.show]);

  function SelectContacts(contact: any) {
    console.log(contact,"contactss")
    props.setContact(JSON.stringify(contact));
    props.setShow(false);
  }

  return (
    <Modal
      transparent={true}
      animationType={'none'}
      visible={props.show}
      onRequestClose={() => {
        props.setShow(false);
      }}
    >
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>
          <Text
            style={{
              fontSize: responsiveValue(12),
              marginBottom: 20,
              fontFamily: 'bold'
            }}
          >
            Select Contact
          </Text>

          <View style={{ flex: 1 }}>
          {isLoading ? (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignContent: 'center',
          }}
        >
          <Image source={Loading} style={{ width: responsiveValue(50), height: responsiveValue(50) }} />
        </View>
      ): (
              <FlatList
                data={contacts}
                keyExtractor={(item, index) => item.id}
                renderItem={({ item }) => {
                  return (
                    <TouchableOpacity
                      style={{
                        marginVertical: 7,
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}
                      onPress={() => {
                        SelectContacts(item);
                      }}
                    >
                      <Image
                        source={require('app/assets/user.jpg')}
                        style={{
                          width: responsiveValue(30),
                          height: responsiveValue(30),
                          borderRadius: wp('7%'),
                          borderColor: 'white',
                          borderWidth: wp('0.3%'),
                        }}
                      />
                      <View
                        style={{ flexDirection: 'column', marginHorizontal: 8 }}
                      >
                        <Text
                          style={{
                            marginVertical: 4,
                            fontSize: responsiveValue(13),
                            color: 'black',
                            fontFamily: 'bold'
                          }}
                        >
                          {item.name}
                        </Text>
                        <Text style={{ color: 'rgba(0, 0, 0, 0.31)', fontFamily: 'medium'}}>
                          {item.phoneNumbers && item.phoneNumbers.length > 0
                            ? item.phoneNumbers[0].number
                            : ''}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  );
                }}
              />
            )}
          </View>

          <View
            style={{
              marginTop: 'auto',
              alignItems: 'flex-end',
            }}
          >
            <TouchableOpacity onPress={() => props.setShow(false)}>
              <Text
                style={{
                  fontSize: responsiveValue(13),
                  fontFamily: 'bold'
                }}
              >
                CANCEL
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'space-around',
    backgroundColor: '#00000040',
  },
  activityIndicatorWrapper: {
    backgroundColor: '#FFFFFF',
    height: wp('100%'),
    width: '90%',
    borderRadius: 10,
    display: 'flex',
    padding: '7%',
    flexDirection: 'column',
  },
});
