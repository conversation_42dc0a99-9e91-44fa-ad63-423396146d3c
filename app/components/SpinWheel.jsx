import React, { Component } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  Button,
  TouchableOpacity,
} from 'react-native';

import WheelOfFortune from 'app/components/WheelOfFortune';
import CustomModal from 'app/components/elements/Modals';
import { CustomText } from './elements';
import {
  useChangeUserPennytots,
  useStakeUserPennytots,
} from 'app/redux/quiz/hook';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { SafeAreaView } from 'react-native-safe-area-context';
import { t } from 'i18next';

const participants = ['0', '100', '500', '300', '0', '200', '400', '50'];

const colors = [
  '#FED830',
  '#1B1B1B',
  '#FED830',
  '#1B1B1B',
  '#FED830',
  '#1B1B1B',
  '#FED830',
  '#1B1B1B',
];
class SpinWheel extends Component {
  constructor(props) {
    super(props);

    this.state = {
      winnerValue: null,
      winnerIndex: null,
      started: false,
    };
    this.child = null;
  }

  buttonPress = () => {
    this.setState({
      started: true,
    });
    this.child._onPress();
  };

  getWinner = (value, index) => {
    const numericValue = parseFloat(value);
    this.props.onSpinResult(numericValue); // Notify the parent component of the spin result
    if (numericValue === 0) {
      this.setState({
        winnerValue: numericValue,
        winnerIndex: index,
      });
    } else {
      this.setState((prevState) => ({
        winnerValue: numericValue,
        winnerIndex: index,
      }));
    }
  };

  render() {
    const wheelOptions = {
      rewards: participants,
      colors: colors,
      knobSize: 25,
      borderWidth: 5,
      borderColor: '#fff',
      innerRadius: 30,
      duration: 4000,

      backgroundColor: 'transparent',
      textAngle: 'horizontal',
      knobSource: require('app/assets/Union.png'),

      onRef: (ref) => (this.child = ref),
    };

    return (
      <SafeAreaView style={styles.container}>
          <WheelOfFortune options={wheelOptions} getWinner={this.getWinner} />
        <View
          style={{ display: 'flex', alignItems: 'center', marginTop: rv(70) }}
        >
          <Text style={{ fontSize: rv(23), fontFamily: 'regular' }}>
            {this.props.totalAmountWon}
          </Text>
          <View style={styles.underline} />
        </View>
        {!this.state.started && (
          <View style={styles.bottomColor}>
            <View style={styles.startButtonView}>
              <TouchableOpacity
                onPress={() => this.buttonPress()}
                style={styles.startButton}
              >
                <CustomText style={styles.startButtonText}>{t("spin")}</CustomText>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {this.state.winnerIndex != null && (
          <View style={styles.bottomColor}>
            <View style={styles.startButtonView}>
              <TouchableOpacity
                onPress={() => {
                  this.setState({ winnerIndex: null });
                  this.child._tryAgain();
                }}
                style={styles.startButton}
              >
                <CustomText style={styles.startButtonText}>{t("spin")}</CustomText>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </SafeAreaView>
    );
  }
}
export default SpinWheel;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop:rv(10),
    backgroundColor: '#fff',
    alignItems: 'center',
    // justifyContent
  },
  underline: {
    width: rv(50), // Adjust width as needed to match the text width
    height: 4, // Thickness of the underline
    borderRadius: 2, // Adjust the border radius as needed
    backgroundColor: 'yellow', // Set the color to yellow
    marginTop: 2, // Adjust the space between text and underline
  },
  startButtonView: {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
  },
  bottomColor: {
    marginTop:rv(50),
    display: 'flex',
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  startButton: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
    width: '50%',
    borderRadius: rv(80),
    paddingVertical: rv(12),
  },
  startButtonText: {
    fontSize: rv(18),
    fontFamily: 'regular',
    textAlign: 'center',
    color: '#FED830',
    width: '85%',
  },
  winnerView: {
    display: 'flex',
    width: '85%',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: rv(20),
    backgroundColor: '#000',
  },
  tryAgainButton: {
    width: '85%',
    marginTop: rv(20),
    padding: 10,
  },
  winnerText: {
    fontSize: 30,
  },
  tryAgainButton: {
    padding: 5,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  tryAgainText: {
    fontSize: rv(18),
    fontWeight: 'bold',
    color: '#fff',
  },
});
