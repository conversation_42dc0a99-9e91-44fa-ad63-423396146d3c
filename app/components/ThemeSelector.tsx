import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { ThemeMode } from 'app/types/theme';

/**
 * Theme selector component with three options: Light, Dark, System
 * Useful for settings screens
 */
export const ThemeSelector: React.FC = () => {
  const { theme, themeMode, setTheme } = useTheme();
  const styles = createStyles(theme);

  const themeOptions: { mode: ThemeMode; label: string; icon: string }[] = [
    { mode: 'light', label: 'Light', icon: '☀️' },
    { mode: 'dark', label: 'Dark', icon: '🌙' },
    { mode: 'system', label: 'System', icon: '🔄' },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Theme</Text>
      <View style={styles.optionsContainer}>
        {themeOptions.map((option) => (
          <TouchableOpacity
            key={option.mode}
            style={[
              styles.option,
              themeMode === option.mode && styles.selectedOption,
            ]}
            onPress={() => setTheme(option.mode)}
          >
            <Text style={styles.icon}>{option.icon}</Text>
            <Text
              style={[
                styles.optionText,
                themeMode === option.mode && styles.selectedOptionText,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const createStyles = createThemedStyles((theme) => ({
  container: {
    padding: theme.spacing.lg,
  },
  title: {
    fontSize: rv(18),
    fontFamily: 'bold' as const,
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  optionsContainer: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
  },
  option: {
    flex: 1,
    alignItems: 'center' as const,
    padding: theme.spacing.md,
    marginHorizontal: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  selectedOption: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary,
  },
  icon: {
    fontSize: rv(24),
    marginBottom: theme.spacing.xs,
  },
  optionText: {
    fontSize: rv(14),
    fontFamily: 'medium' as const,
    color: theme.colors.textSecondary,
    textAlign: 'center' as const,
  },
  selectedOptionText: {
    color: theme.colors.background,
    fontFamily: 'bold' as const,
  },
}));

export default ThemeSelector;
