import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from 'app/hooks/useTheme';

interface ThemedStatusBarProps {
  /**
   * Override the theme's default status bar style
   */
  style?: 'light' | 'dark' | 'auto';
  
  /**
   * Override the background color (Android only)
   */
  backgroundColor?: string;
  
  /**
   * Whether to hide the status bar
   */
  hidden?: boolean;
  
  /**
   * Animation type when changing status bar properties
   */
  animated?: boolean;
}

/**
 * Theme-aware StatusBar component
 * Automatically uses the correct status bar style based on current theme
 */
export const ThemedStatusBar: React.FC<ThemedStatusBarProps> = ({
  style,
  backgroundColor,
  hidden = false,
  animated = true,
}) => {
  const { theme } = useTheme();
  
  // Determine status bar style
  let statusBarStyle: 'light' | 'dark' | 'auto' = 'auto';
  
  if (style) {
    statusBarStyle = style;
  } else {
    // Use theme's default status bar style
    statusBarStyle = theme.colors.statusBarStyle === 'light-content' ? 'light' : 'dark';
  }
  
  return (
    <StatusBar
      style={statusBarStyle}
      backgroundColor={backgroundColor || theme.colors.headerBackground}
      hidden={hidden}
      animated={animated}
    />
  );
};

export default ThemedStatusBar;
