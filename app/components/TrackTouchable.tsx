import React from 'react';
import { TouchableOpacity } from 'react-native';
import useTrackActivity from 'app/hooks/useTrackActivity';

const TrackedTouchableOpacity = ({
  onPress,
  activityType = 'click',
  ...rest
}: any) => {
  const { trackActivity } = useTrackActivity(activityType);

  const handlePress = () => {
    // Increment the count
    trackActivity();
    // Call the original onPress, if provided
    if (onPress) {
      onPress();
    }
  };

  return <TouchableOpacity {...rest} onPress={handlePress} />;
};

export default TrackedTouchableOpacity;
