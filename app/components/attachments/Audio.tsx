import { View, Text, TouchableOpacity } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import React from 'react';
import { FileHelper } from 'app/helpers';
import { responsiveValue } from 'app/providers/responsive-value';
import { useTheme } from 'app/hooks/useTheme';

export default function Audio(props: any) {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      onPress={() => {
        if (props.link) {

          props.navigation.push('Common', {
            screen: 'preview-attachment',
            params: {
              type: 'audio',
              attachment: props.link,
            },
          });
        }
      }}
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 15,
        borderRadius: 5,
        backgroundColor: theme.colors.cardBackground,
      }}
    >
      <View
        style={{
          flexDirection: 'row',
        }}
      >
        <Ionicons
          name='play-circle'
          size={21}
          style={{ marginRight: 5 }}
          color={theme.colors.accent}
        />
        <Text
          style={{
            fontSize: responsiveValue(13),
            fontWeight: 'bold',
            paddingRight: 15,
            color: theme.colors.text,
          }}
        >
          Audio File
        </Text>
      </View>

      <Text
        style={{
          fontSize: responsiveValue(13),
          color: theme.colors.textSecondary,
        }}
      >
        {FileHelper.byteToFileSize(props.attachmentSize)}
      </Text>
    </TouchableOpacity>
  );
}
