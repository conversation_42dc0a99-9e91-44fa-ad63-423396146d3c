import { View, Text, TouchableOpacity,Image } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import React from 'react';
import { FileHelper } from 'app/helpers';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { useTheme } from 'app/hooks/useTheme';

export default function Video(props: any) {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      onPress={() => {
        if (props.link) {
          props.navigation.push('Common', {
            screen: 'preview-attachment',
            params: {
              type: 'video',
              attachment: props.link,
            },
          });
        }
      }}
      style={{
        height: hp('30%'),
        width: '100%',
        backgroundColor: theme.colors.surface,
        paddingTop: hp('10.5%'),
      }}
    >
      <View
        style={{
          marginBottom: -hp('17.5%'),
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 999,
          flexDirection: 'column',
        }}
      >
        <Ionicons name='play-circle' size={35} color={theme.colors.accent} />
        <Text
          style={{
            color: theme.colors.text,
            fontWeight: 'bold',
          }}
        >
          {FileHelper.byteToFileSize(props.attachmentSize)}
        </Text>
      </View>
      <View
        style={{
          backgroundColor: theme.colors.surface,
        }}
      >
        <Image
          source={{
            uri: props.link,
          }}
          style={{
            height: hp('30%'),
            width: '100%',
          }}
          resizeMode="cover"
        />
      </View>
    </TouchableOpacity>
  );
}
