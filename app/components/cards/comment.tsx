import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import React, { useEffect, useState } from "react";
import Avatar from "../elements/Avater";
import { CustomText } from "../elements";
import { timeAgo } from "app/helpers/time-ago";
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from "react-native-popup-menu";
import Ionicons from "@expo/vector-icons/Ionicons";
import ReportDialog from "app/components/dialogs/report";
import { Axios } from "app/api/axios";
import ThumbSVG from "../svgReactComponent/ThumSVG";
import CommentSVG from "../svgReactComponent/CommentSVG";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { default as themeFont } from "app/assets/themes/fonts.json";
import { Image } from "expo-image";
import Document from "../attachments/Document";
import Audio from "../attachments/Audio";
import Video from "../attachments/Video";
import { responsiveValue as rv } from "app/providers/responsive-value";
import MenuSVG from "app/assets/svg/menu.svg";
import { useSelector } from "react-redux";
import { userId } from "app/redux/user/reducer";
import { useTopicSubComments } from "app/redux/topic/hooks";
import FlagSVG from "app/assets/svg/flagasInapp.svg";
import { showModal, hideModal } from "app/providers/modals";
import { DeleteSVG } from "app/providers/svg/loader";
import { topic } from "app/api";
import ReplySVG from "@/app/assets/svg/Reply.svg";
import { useTheme } from "app/hooks/useTheme";
import { createOptimizedThemedStyles, createThemedStyles } from "app/utils/createThemedStyles";
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";
const Loading = require("../../assets/loading.gif");

interface ICommentCard {
  item: any;
  refreshFunction?: () => void;
  navigation: any;
  setSubCommentData: any;
}

// Container Component
const CommentCardContainer = ({
  item,
  refreshFunction = () => {},
  navigation,
  setSubCommentData,
}: ICommentCard) => {
  const { SlideInMenu } = renderers;
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const [isLiked, setIsLiked] = useState(false);
  const [numLikes, setNumLikes] = useState(0);
  const myId = useSelector(userId);
  const [commentId, setCommentId] = useState<any>(null);
  const { data: subComments, isLoading: loadingSubComments } =
    useTopicSubComments(commentId);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});

  useEffect(() => {
    if (item.isLiked && item.likes) {
      setIsLiked(item.isLiked);
      setNumLikes(item.likes);
    }
  }, [item]);

  function openReportDetails(item: any, type: any) {
    setReportDetails({
      data: { _id: item },
      type,
    });
    setShowReportDialog(true);
  }

  function handleCommentLike({ commentId, commentType }: any) {
    setIsLiked(!isLiked);
    setNumLikes(isLiked ? numLikes - 1 : numLikes + 1);

    Axios({
      method: "post",
      url: "/topics/like-topic-comment/" + commentId + "?type=" + commentType,
    })
      .then((response) => {
        refreshFunction();
        // refetchComment();
      })
      .catch(() => {
        setIsLiked(!isLiked);
        setNumLikes(isLiked ? numLikes + 1 : numLikes - 1);
      });
  }

  async function deleteComments() {
    topic.deleteComment(item._id).then((data) => {
      showModal({
        modalVisible: true,
        title: "Alert",
        message: "Comment deleted",
        setModalVisible: hideModal, // Function to close the modal
        type: "success-alert",
        handleConfirm: () => {
          hideModal();
        },
        handleAlert: () => {
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Removed Successfully',
      // });
      refreshFunction!();
    });
  }
  async function deleteSubComments(subCommentId: any) {
    topic.deleteSubComment(subCommentId).then((data) => {
      showModal({
        modalVisible: true,
        title: "Alert",
        message: "Delete successfully",
        setModalVisible: hideModal, // Function to close the modal
        type: "success-alert",
        handleConfirm: () => {
          hideModal();
        },
        handleAlert: () => {
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Removed Successfully',
      // });
      refreshFunction!();
    });
  }

  return (
    <CommentCardPresentation
      theme={theme}
      styles={styles}
      item={item}
      navigation={navigation}
      myId={myId}
      isLiked={isLiked}
      numLikes={numLikes}
      commentId={commentId}
      subComments={subComments}
      loadingSubComments={loadingSubComments}
      showReportDialog={showReportDialog}
      reportDetails={reportDetails}
      handleCommentLike={handleCommentLike}
      deleteComments={deleteComments}
      deleteSubComments={deleteSubComments}
      openReportDetails={openReportDetails}
      setSubCommentData={setSubCommentData}
      setCommentId={setCommentId}
      setShowReportDialog={setShowReportDialog}
      SlideInMenu={SlideInMenu}
    />
  );
};

// Presentation Component
interface CommentCardPresentationProps {
  theme: any;
  styles: any;
  item: any;
  navigation: any;
  myId: string;
  isLiked: boolean;
  numLikes: number;
  commentId: any;
  subComments: any;
  loadingSubComments: boolean;
  showReportDialog: boolean;
  reportDetails: any;
  handleCommentLike: (params: any) => void;
  deleteComments: () => void;
  deleteSubComments: (subCommentId: any) => void;
  openReportDetails: (item: any, type: any) => void;
  setSubCommentData: (data: any) => void;
  setCommentId: (id: any) => void;
  setShowReportDialog: (show: boolean) => void;
  SlideInMenu: any;
}

const CommentCardPresentation: React.FC<CommentCardPresentationProps> = ({
  theme,
  styles,
  item,
  navigation,
  myId,
  isLiked,
  numLikes,
  commentId,
  subComments,
  loadingSubComments,
  showReportDialog,
  reportDetails,
  handleCommentLike,
  deleteComments,
  deleteSubComments,
  openReportDetails,
  setSubCommentData,
  setCommentId,
  setShowReportDialog,
  SlideInMenu,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.avatarContainer}>
        <TouchableOpacity
          onPress={() => {
            if (item?.userId?._id != myId) {
              navigation.push("Common", {
                screen: "private-chat",
                params: {
                  postid: item.userId.first_name + " " + item.userId.last_name,
                  userDetails: item.userId,
                },
              });
            }
          }}
        >
          <ReportDialog
            show={showReportDialog}
            setShow={setShowReportDialog}
            reportDetails={reportDetails}
          />

          <View style={styles.avatarWrapper}>
            <Avatar source={item.userId.profile_picture} size={rv(40)} />
          </View>
        </TouchableOpacity>
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          <View style={styles.userInfoSection}>
            <TouchableOpacity
              onPress={() =>
                navigation.push("Common", {
                  screen: "private-chat",
                  params: {
                    userDetails: item.userId,
                  },
                })
              }
              style={styles.nameTouchable}
            >
              <CustomText
                adjustsFontSizeToFit={false}
                numberOfLines={1}
                style={styles.userName}
              >
                {item.userId.first_name + " " + item.userId.last_name}
              </CustomText>
            </TouchableOpacity>

            <View style={styles.userInfoContainer}>
              <CustomText
                adjustsFontSizeToFit={false}
                numberOfLines={2}
                ellipsizeMode="tail"
                style={styles.userPosition}
              >
                {item.userId.company_position && item.userId.company
                  ? `${
                      item.userId.company_position?.replace(
                        /(\r\n|\n|\r)/gm,
                        ""
                      ) || "company"
                    } at ${item.userId.company}`
                  : "Position at company"}
              </CustomText>
            </View>
            <View style={styles.timeContainer}>
              <CustomText
                adjustsFontSizeToFit={true}
                numberOfLines={1}
                style={styles.timeText}
              >
                {timeAgo(item.createdAt)}
              </CustomText>
            </View>
          </View>
          <View style={styles.menuContainer}>
            <Menu renderer={SlideInMenu}>
              <MenuTrigger>
                <View style={styles.menuTrigger}>
                  <MenuSVG width={15} height={15} />
                </View>
              </MenuTrigger>

              <MenuOptions
                customStyles={{
                  optionText: [styles.menuText],
                  optionsContainer: [styles.menuOptionsContainer],
                }}
              >
                <View style={styles.menuContent}>
                  {item.userId._id === myId ? (
                    <MenuOption onSelect={() => deleteComments()}>
                      <View style={styles.menuItem}>
                        <DeleteSVG width={20} height={20} />
                        <CustomText style={styles.deleteMenuItemText}>
                          Delete comment
                        </CustomText>
                      </View>
                    </MenuOption>
                  ) : null}
                  {item.userId._id === myId ? (
                    <MenuOption
                      onSelect={() => {
                        openReportDetails(item._id, "comment");
                      }}
                    >
                      <View style={styles.menuItem}>
                        <FlagSVG width={20} />
                        <CustomText style={styles.menuItemText}>
                          Flag comment as inappropriate
                        </CustomText>
                      </View>
                    </MenuOption>
                  ) : null}
                </View>
              </MenuOptions>
            </Menu>
          </View>
        </View>
        <View style={styles.commentTextContainer}>
          <View style={styles.commentTextWrapper}>
            <CustomText style={styles.commentText}>{item.comment}</CustomText>
          </View>
        </View>

        {/* Comment Medias */}

        {item.image ? (
          <TouchableOpacity
            style={styles.attachment}
            onPress={() =>
              navigation.push("Common", {
                screen: "fullscreen-image",
                params: {
                  image: item.image,
                },
              })
            }
          >
            <Image
              source={{
                uri: item.image,
              }}
              style={styles.commentImage}
              resizeMode="cover"
            />
          </TouchableOpacity>
        ) : null}
        {item.document ? (
          <View style={styles.attachment}>
            <Document
              attachmentSize={item.attachmentSize}
              link={item.document}
              navigation={navigation}
            />
          </View>
        ) : null}

        {item.audio ? (
          <View style={styles.attachment}>
            <Audio
              attachmentSize={item.attachmentSize}
              link={item.audio}
              navigation={navigation}
            />
          </View>
        ) : null}

        {item.video ? (
          <View style={styles.attachment}>
            <Video
              attachmentSize={item.attachmentSize}
              link={item.video}
              navigation={navigation}
            />
          </View>
        ) : null}

        <View style={styles.actionsContainer}>
          <View style={styles.actionsRow}>
            <TouchableOpacity
              style={styles.replyButton}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
              onPress={() => {
                setSubCommentData({
                  id: item._id,
                  comment: item.comment,
                });
              }}
            >
              <ReplySVG width={30} />
              <CustomText style={styles.replyText}>Reply</CustomText>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setCommentId(item._id);
              }}
              style={styles.commentsButton}
            >
              <CommentSVG
                color={theme.colors.textPlaceholder}
                style={styles.commentIcon}
              />

              <CustomText style={styles.commentsText}>
                {item.subCommentsCount} Comment
                {item.subCommentsCount > 1 ? "s" : ""}
              </CustomText>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                handleCommentLike({
                  commentId: item._id,
                  commentType: "comment",
                });
              }}
              style={styles.likesButton}
            >
              <ThumbSVG
                color={isLiked ? "#E64028" : theme.colors.textPlaceholder}
                style={styles.likeIcon}
              />

              <CustomText
                style={[
                  styles.likesText,
                  { color: isLiked ? "#E64028" : theme.colors.textPlaceholder },
                ]}
              >
                {numLikes}
              </CustomText>
            </TouchableOpacity>
          </View>
        </View>

        {/* SubComment Section */}

        <View
          style={{
            flex: 1,
            marginVertical: rv(3),
            width: "100%",
          }}
        >
          {loadingSubComments ? (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Image
                source={Loading}
                style={{
                  width: rv(50),
                  height: rv(50),
                }}
              />
            </View>
          ) : subComments && subComments.length > 0 ? (
            subComments.map((subComment: any, index: number) => (
              <View
                key={index}
                style={{
                  flex: 1,
                  flexDirection: "row",
                  justifyContent: "flex-start",
                  alignItems: "flex-start",
                  paddingTop: 5,
                }}
              >
                <View style={{ width: "15%", marginRight: rv(10) }}>
                  <TouchableOpacity
                    style={{
                      paddingRight: 3,
                    }}
                    onPress={() =>
                      navigation.push("Common", {
                        screen: "private-chat",
                        params: {
                          postid:
                            subComment.userId.first_name +
                            " " +
                            subComment.userId.last_name,
                          userDetails: subComment.userId,
                        },
                      })
                    }
                  >
                    <Avatar
                      source={subComment.userId.profile_picture}
                      size={30}
                    />
                  </TouchableOpacity>
                </View>

                <View style={{ width: "85%" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "flex-start",
                      alignItems: "center",
                      marginBottom: rv(3),
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        width: "60%",
                        paddingTop: rv(2),
                      }}
                      onPress={() =>
                        navigation.push("Common", {
                          screen: "private-chat",
                          params: {
                            postid:
                              subComment.userId.first_name +
                              " " +
                              subComment.userId.last_name,
                            userDetails: subComment.userId,
                          },
                        })
                      }
                    >
                      <CustomText
                        adjustsFontSizeToFit={true}
                        numberOfLines={1}
                        style={[
                          styles.font_thirteen,
                          {
                            color: "#000000",
                            fontFamily: "bold",
                          },
                        ]}
                        // textType='bold'
                      >
                        {subComment.userId.first_name +
                          " " +
                          subComment.userId.last_name}
                      </CustomText>
                      <View style={{ width: "100%" }}>
                        <CustomText
                          adjustsFontSizeToFit={false}
                          numberOfLines={2}
                          ellipsizeMode="tail"
                          style={[
                            styles.font_twelve,
                            {
                              // flexDirection: 'row',
                              // width: '80%',

                              color: "#9D9D9D",
                              fontFamily: "medium",
                            },
                          ]}
                        >
                          {subComment.userId.company_position &&
                          subComment.userId.company
                            ? `${
                                subComment.userId.company_position?.replace(
                                  /(\r\n|\n|\r)/gm,
                                  ""
                                ) || "company"
                              } at ${subComment.userId.company}`
                            : "Position at Company"}
                        </CustomText>
                      </View>
                      <View
                        style={{
                          alignItems: "flex-start",
                        }}
                      >
                        <CustomText
                          adjustsFontSizeToFit={true}
                          numberOfLines={1}
                          style={[
                            styles.font_eleven,
                            {
                              color: theme.colors.textSecondary,
                              marginTop: 2,
                            },
                          ]}
                        >
                          {timeAgo(subComment.createdAt)}
                        </CustomText>
                      </View>
                    </TouchableOpacity>

                    <View
                      style={{
                        width: "40%",
                        alignItems: "flex-end",
                        flexDirection: "row",
                        paddingHorizontal: 5,
                      }}
                    >
                      <View
                        style={{
                          width: "80%",
                          alignItems: "flex-end",
                        }}
                      ></View>

                      <View
                        style={{
                          width: "20%",
                        }}
                      >
                        <Menu renderer={SlideInMenu}>
                          <MenuTrigger>
                            <View
                              style={{
                                alignItems: "flex-end",
                                flexDirection: "row",
                                padding: rv(3),
                              }}
                            >
                              <MenuSVG width={15} height={15} />
                            </View>
                          </MenuTrigger>

                          <MenuOptions
                            customStyles={{
                              optionText: [styles.menuText],
                              optionsContainer: [
                                {
                                  borderRadius: 15,
                                  backgroundColor: theme.colors.surface,
                                },
                              ],
                            }}
                          >
                            <View
                              style={{
                                margin: 5,
                                flexDirection: "column",
                                marginVertical: 10,
                                padding: 15,
                              }}
                            >
                              {subComment.userId._id === myId ? (
                                <MenuOption
                                  onSelect={() =>
                                    deleteSubComments(subComment._id)
                                  }
                                >
                                  <View
                                    style={{
                                      flexDirection: "row",
                                      alignItems: "center",
                                      marginBottom: rv(10),
                                      marginHorizontal: 10,
                                    }}
                                  >
                                    <DeleteSVG width={20} height={20} />
                                    <CustomText
                                      style={[
                                        styles.font_thirteen,
                                        {
                                          color: theme.colors.error,

                                          marginLeft: rv(5),
                                          fontFamily: "medium",

                                          // Consistent margin
                                        },
                                      ]}
                                    >
                                      Delete Sub Comment
                                    </CustomText>
                                  </View>
                                </MenuOption>
                              ) : null}
                              {subComment.userId._id === myId ? (
                                <MenuOption
                                  onSelect={() => {
                                    openReportDetails(
                                      subComment._id,
                                      "sub-comment"
                                    );
                                  }}
                                >
                                  <View
                                    style={{
                                      flexDirection: "row",
                                      width: "100%",
                                      alignItems: "center",
                                      marginHorizontal: 10,
                                    }}
                                  >
                                    <FlagSVG width={20} />
                                    <CustomText
                                      style={[
                                        styles.font_twelve,
                                        {
                                          color: theme.colors.text,

                                          marginLeft: rv(5),
                                          fontFamily: "medium",
                                        },
                                      ]}
                                    >
                                      Flag comment as inappropriate
                                    </CustomText>
                                  </View>
                                </MenuOption>
                              ) : null}
                            </View>
                          </MenuOptions>
                        </Menu>
                      </View>
                    </View>
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "flex-start",
                      alignItems: "flex-start",
                    }}
                  >
                    <View style={{ width: "95%" }}>
                      <CustomText
                        style={[
                          styles.font_twelve,
                          {
                            color: theme.colors.textSecondary,

                            fontFamily: "medium",
                          },
                        ]}
                      >
                        {subComment.comment}
                      </CustomText>
                    </View>
                  </View>
                </View>
              </View>
            ))
          ) : null}
        </View>
      </View>
    </View>
  );
};

// Themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    flexDirection: "row" as const,
    justifyContent: "flex-start" as const,
    alignItems: "flex-start" as const,
    paddingTop: rv(2),
    borderBottomColor: theme.colors.border,
    paddingHorizontal: 20,
    backgroundColor: theme.colors.cardBackground,
  },
  avatarContainer: {
    width: "20%",
  },
  avatarWrapper: {
    marginBottom: 10,
    marginLeft: 10,
    marginRight: 10,
  },
  contentContainer: {
    width: "80%",
  },
  headerRow: {
    width: "100%",
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    alignItems: "flex-start" as const,
    flex: 1,
  },
  userInfoSection: {
    width: "80%",
  },
  nameTouchable: {
    justifyContent: "center" as const,
  },
  userName: {
    color: theme.colors.text,
    fontSize: fs("MD"),
    fontFamily: "bold" as const,
  },
  userInfoContainer: {
    width: "100%",
  },
  userPosition: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: "medium" as const,
  },
  timeContainer: {
    width: "80%",
    alignItems: "flex-start" as const,
  },
  timeText: {
    fontSize: fs("SM"),
    color: theme.colors.textSecondary,
    flexWrap: "nowrap" as const,
  },
  menuContainer: {
    alignItems: "flex-end" as const,
    flexDirection: "row" as const,
  },
  menuTrigger: {
    alignItems: "flex-end" as const,
    flexDirection: "row" as const,
    padding: rv(3),
  },
  menuOptionsContainer: {
    borderRadius: 15,
    backgroundColor: theme.colors.surface,
  },
  menuContent: {
    margin: 5,
    flexDirection: "column" as const,
    marginVertical: 10,
    padding: 15,
  },
  menuItem: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    marginBottom: rv(10),
    marginHorizontal: 10,
  },
  menuItemText: {
    color: theme.colors.text,
    fontSize: fs("BASE"),
    marginLeft: rv(5),
    fontFamily: "medium" as const,
  },
  deleteMenuItemText: {
    color: theme.colors.error,
    fontSize: fs("MD"),
    marginLeft: rv(5),
    fontFamily: "medium" as const,
  },
  menuText: {
    color: theme.colors.textSecondary,
    fontSize: fs("MD"),
    padding: 4,
    width: "100%",
    fontFamily: "regular" as const,
  },
  commentTextContainer: {
    flexDirection: "row" as const,
    justifyContent: "flex-start" as const,
    alignItems: "flex-start" as const,
  },
  commentTextWrapper: {
    width: "95%",
  },
  commentText: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    lineHeight: 20,
    fontFamily: "medium" as const,
  },
  attachment: {
    marginTop: 8,
    width: wp("68%"),
  },
  commentImage: {
    height: hp("30%"),
    borderRadius: wp("2%"),
    borderColor: theme.colors.border,
    borderWidth: wp("0.6%"),
    flex: 1,
    flexDirection: "column" as const,
  },
  actionsContainer: {
    width: "90%",
    flexDirection: "column" as const,
    marginTop: rv(5),
    marginBottom: rv(5),
  },
  actionsRow: {
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
  },
  replyButton: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    gap: rv(5),
  },
  replyText: {
    color: theme.colors.textSecondary,
    marginLeft: 5,
    fontSize: 13,
    paddingTop: 6,
    fontFamily: "medium" as const,
  },
  commentsButton: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    justifyContent: "center" as const,
  },
  commentIcon: {
    marginRight: 5,
  },
  commentsText: {
    color: theme.colors.textPlaceholder,
    marginLeft: 3,
    fontSize: fs("SM"),
    paddingTop: 6,
    fontFamily: "regular" as const,
  },
  likesButton: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    justifyContent: "center" as const,
  },
  likeIcon: {
    marginRight: 5,
  },
  likesText: {
    fontSize: fs("SM"),
    marginTop: 4,
    marginLeft: 3,
    fontFamily: "regular" as const,
  },
  font_twelve: {
    fontSize: fs("BASE"),
  },
  font_thirteen: {
    fontSize: fs("MD"),
  },
  font_eleven: {
    fontSize: fs("SM"),
  },
}));

// Export the container component as the main export
export const CommentCard = CommentCardContainer;
export default CommentCard;
