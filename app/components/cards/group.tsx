import React, { FunctionComponent, useContext, useState } from "react";
import { View, TouchableOpacity, Image, StyleSheet } from "react-native";
import { CustomText } from "../elements";
import Avatar from "../elements/Avater";
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from "react-native-popup-menu";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { default as themeFont } from "app/assets/themes/fonts.json";
import ReportDialog from "app/components/dialogs/report";
import { Axios } from "app/api/axios";
import { ShowAlert } from "app/providers/toast";
import { responsiveValue as rv } from "app/providers/responsive-value";
import MenuSVG from "app/assets/svg/menu.svg";
import { useTranslation } from "react-i18next";
import ReportGroupSVG from "app/assets/svg/reportUser.svg";
import MuteSVG from "app/assets/svg/muteUser.svg";
import UnMuteSVG from "app/assets/svg/unmuteUser.svg";
import ExitGroupSVG from "app/assets/svg/ExitGroup.svg";
import { userId, userToken } from "app/redux/user/reducer";
import { useSelector } from "react-redux";
import { timeAgo } from "app/helpers/time-ago";
import ThumbSVG from "app/assets/svg/thumb.svg";
import Document from "app/components/attachments/Document";
import Audio from "app/components/attachments/Audio";
import Video from "app/components/attachments/Video";
import { useEffect } from "react";
import CommentSVG from "../svgReactComponent/CommentSVG";
import { showModal, hideModal } from "app/providers/modals";
import { useTheme } from "app/hooks/useTheme";
import {
  createOptimizedThemedStyles,
  createThemedStyles,
} from "app/utils/createThemedStyles";
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";

type GroupItemProps = {
  item?: any;
  placeholder?: any;
  navigation: any;
  recommended?: boolean;
  menu?: boolean;
  onToggleMuted: () => void; // Function that handles the mute/unmute logic
  refreshFunction?: () => void;
};

// Container Component
const GroupContainer: FunctionComponent<GroupItemProps> = ({
  item,
  onToggleMuted,
  navigation,
  recommended,
  refreshFunction,
  menu = true,
}) => {
  const { SlideInMenu } = renderers;
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});
  const { t } = useTranslation();
  const Id = useSelector(userId);
  const [isMuted, setIsMuted] = useState(item.muted);
  const [isLiked, setIsLiked] = useState(false);
  const [numLikes, setNumLikes] = useState(0);

  const blurhash =
    "|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[";

  //
  // const handleToggle =()=>{
  //   setIsMuted(prevMuted => {
  //     const newMuted = !prevMuted
  //     if(onToggleMuted){
  //       onToggleMuted()
  //     }
  //     return newMuted
  //   })

  // }
  useEffect(() => {
    if (item.isLiked && item.likes) {
      setIsLiked(item.isLiked);
      setNumLikes(item.likes);
    }
  }, [item]);

  function handleLike() {
    setIsLiked(!isLiked);
    setNumLikes(isLiked ? numLikes - 1 : numLikes + 1);

    // topic
    //   .likeTopic(item._id)
    //   .then((data) => {
    //     refreshFunction!();
    //   })
    //   .catch((err) => {
    //     setIsLiked(!isLiked);
    //     setNumLikes(isLiked ? numLikes + 1 : numLikes - 1);
    //   });
  }

  function openReportDetails(item: any) {
    setReportDetails({ data: { _id: item }, type: "group" });
    setShowReportDialog(true);
  }

  const handleToggleMuted = () => {
    setIsMuted((prevMuted: any) => !prevMuted);
    if (onToggleMuted) {
      onToggleMuted();
    }
  };

  async function leaveGroup(groupId: string) {
    await Axios({
      method: "delete",
      url: "/group/leave/" + groupId,
    }).then((response) => {
      const message =
        response.data.message === "You left the group successfully"
          ? "You have left the group"
          : response.data.message;

      showModal({
        modalVisible: true,
        title: "Alert",
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: "success-alert",
        handleConfirm: () => {
          hideModal();
        },
        handleAlert: () => {
          hideModal();
        },
        navigation,
      });

      // ShowAlert({
      //   type: 'success',
      //   message: response.data.message,
      // });
      refreshFunction!();
    });
  }

  function muteNotification() {
    const message = item.muted
      ? "Group chat unmuted!"
      : "Group chat muted. You will no longer receive notifications from this group";

    Axios({
      method: "post",
      url: "/app/mute-notifications/",
      data: {
        contentId: item._id,
        type: "group",
        action: item.muted ? "unmute" : "mute",
      },
    }).then((response: any) => {
      refreshFunction!();
      showModal({
        modalVisible: true,
        title: "Alert",
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: "success-alert",
        handleConfirm: () => {
          console.log("Confirmed!");
          hideModal();
        },
        handleAlert: () => {
          console.log("Alert handled!");
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Notifications has been muted successfully',
      // });
    });
  }

  return (
    <TouchableOpacity
      onPress={() =>
        navigation.push("Common", {
          screen: "group-chat",
          params: {
            groupDetails: item,
          },
        })
      }
      style={styles.container}
    >
      <ReportDialog
        show={showReportDialog}
        setShow={setShowReportDialog}
        reportDetails={reportDetails}
      />
      <View style={{ marginBottom: 10, marginLeft: 5, marginRight: 15 }}>
        <Avatar size={45} source={item.image} type="group" />
      </View>
      <View
        style={{
          width: "80%",
          flexDirection: "column",
        }}
      >
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
            marginRight: wp("5%"),
          }}
        >
          <View
            style={{
              width: "70%",
              justifyContent: "center",
            }}
          >
            <CustomText
              style={[
                styles.font_md,
                {
                  fontFamily: "bold",
                  color: theme.colors.text,
                },
              ]}
            >
              {item.name}
            </CustomText>
          </View>

          <View
            style={{
              width: "30%",
              alignItems: "flex-end",
            }}
          >
            {recommended ? (
              <CustomText
                adjustsFontSizeToFit={false}
                numberOfLines={1}
                style={[
                  styles.font_xs,
                  {
                    fontFamily: "medium",
                    color: theme.colors.textSecondary,
                  },
                ]}
              >
                {item.participants.length} Member
                {item.participants.length > 1 ? "s" : ""}
              </CustomText>
            ) : null}

            {!recommended && menu ? (
              <Menu renderer={SlideInMenu}>
                <MenuTrigger>
                  <View
                    style={{
                      alignItems: "flex-end",
                      flexDirection: "row",
                      padding: rv(3),
                    }}
                  >
                    <MenuSVG width={15} height={15} />
                  </View>
                </MenuTrigger>
                <MenuOptions
                      customStyles={{
                  optionText: [styles.menuText],
                  optionsContainer: [styles.menuOptionsContainer],
                }}
                  // customStyles={{
                  //   optionText: [styles.text],
                  //   optionsContainer: [
                  //     {
                  //       borderRadius: 15,
                  //       // backgroundColor: theme.colors.surface,
                      
                  //     },
                  //   ],
                  // }}
                >
                  <View
                    style={{
                      margin: 5,
                      flexDirection: "column",
                      marginVertical: 10,
                      padding: 15,
                      
                    }}
                  >
                    <MenuOption
                      onSelect={() => {
                        openReportDetails(item._id);
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          width: "100%",
                          marginBottom: 10,
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={{
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "center",
                            gap: rv(5),
                          }}
                        >
                          <ReportGroupSVG width={20} />
                          <CustomText
                            style={[
                              styles.font_base,{
                               color: theme.colors.text,

                              fontFamily: "medium",
                            }]}
                          >
                            {t("Groups_opt_reportGroup")}
                          </CustomText>
                        </View>
                      </View>
                    </MenuOption>

                    <MenuOption
                      onSelect={() => {
                        muteNotification();
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          width: "100%",
                          marginBottom: 10,
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={{
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "center",
                          }}
                        >
                          <TouchableOpacity
                            onPress={handleToggleMuted}
                            style={{
                              display: "flex",
                              flexDirection: "row",
                              alignItems: "center",
                              gap: rv(5),
                            }}
                          >
                            {isMuted ? (
                              <MuteSVG width={20} />
                            ) : (
                              <UnMuteSVG width={20} />
                            )}
                            <CustomText
                              style={[
                                styles.font_base,{
                                 color: theme.colors.text,
                            
                                fontFamily: "medium",
                              }]}
                            >
                              {isMuted ? "Unmute Group" : "Mute Group"}
                            </CustomText>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </MenuOption>

                    <MenuOption
                      onSelect={() => {
                        leaveGroup(item._id);
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          width: "100%",
                          marginBottom: 10,
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={{
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "center",
                            gap: rv(5),
                          }}
                        >
                          <ExitGroupSVG width={20} />

                          <CustomText
                            style={[
                              styles.font_base,{
                              color: theme.colors.text,
                              
                              fontFamily: "medium",
                            }]}
                          >
                            {t("Groups_opt_exitGroup")}
                          </CustomText>
                        </View>
                      </View>
                    </MenuOption>
                  </View>
                </MenuOptions>
              </Menu>
            ) : null}
          </View>
        </View>
        <View style={{ width: "100%" }}>
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={1}
            style={[
              styles.font_xs,
              {
                flexDirection: "row",
                width: "100%",

                color: "#9D9D9D",
                fontFamily: "regular",
              },
            ]}
          >
            {item.participants.length} Member
            {item.participants.length > 1 ? "s" : ""}
          </CustomText>
        </View>

        <View
          style={{
            width: "70%",
          }}
        >
          {item.description && (
            <CustomText
              style={[
                styles.font_md,
                {
                  color: "#636363",
                  lineHeight: 20,
                  fontFamily: "medium",
                },
              ]}
            >
              {item.description.slice(0, 35)}
              {item.description.length > 35 ? "..." : ""}
            </CustomText>
          )}
        </View>

        {/* <View
          style={{
            alignItems: 'flex-start',
          }}
        >
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={1}
            style={{
              fontSize: rv(9),
              color: '#9D9D9D',
              flexWrap: 'nowrap',
            }}
          >
            {timeAgo(item.createdAt)}
          </CustomText>
        </View> */}

        {/* <View
          style={{
            marginRight: wp('5%'),

          }}
        >
          <View
            style={{
              flexDirection: 'column',

            }}
          >
            <CustomText
              style={{
                fontSize: rv(10),
                color: '#636363',
                lineHeight: 20,
              }}
            >
              {item.content}
            </CustomText>

            {item.image ? (
              <TouchableOpacity
                style={{

                  flex: 1,
                  width: '100%',
                }}
                onPress={() =>
                  navigation.push('Common', {
                    screen: 'fullscreen-image',
                    params: {
                      image: item.image,
                    },
                  })
                }
              >
                <Image
                  style={{
                    width: '100%',
                    height: hp('30%'),
                    borderRadius: wp('2%'),
                    borderColor: 'white',
                    borderWidth: wp('0.6%'),
                    marginTop: 8,
                  }}
                  source={item.image}
                  placeholder={blurhash}
                  contentFit='cover'
                  transition={1000}
                />
              </TouchableOpacity>
            ) : null}
            {item.audio ? (
              <View style={styles.attachment}>
                <Audio
                  attachmentSize={item.attachmentSize}
                  link={item.audio}
                  navigation={navigation}
                />
              </View>
            ) : null}

            {item.video ? (
              <View style={styles.attachment}>
                <Video
                  attachmentSize={item.attachmentSize}
                  link={item.video}
                  navigation={navigation}
                />
              </View>
            ) : null}

            {item.document ? (
              <View style={styles.attachment}>
                <Document
                  attachmentSize={item.attachmentSize}
                  link={item.document}
                  navigation={navigation}
                />
              </View>
            ) : null}
          </View>
        </View> */}
      </View>
    </TouchableOpacity>
  );
};

// Themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    width: "100%",
    flex: 1,
    flexDirection: "row" as const,
    justifyContent: "flex-start" as const,
    alignItems: "flex-start" as const,
    backgroundColor: theme.colors.cardBackground,
    paddingVertical: rv(8),
  },
  text: {
    color:"red",
    // color: theme.colors.textSecondary,
    textAlign: "center" as const,
    fontSize: fs("MD"),
    width: "87%",
    fontFamily: "regular" as const,
  },
  attachment: {
    marginVertical: 5,
    width: wp("68%"),
  },
  font_sm: {
    fontSize: fs("SM"),
  },
  font_md: {
    fontSize: fs("MD"),
  },
  font_xs: {
    fontSize: fs("XS"),
  },
  font_base:{
    fontSize: fs("BASE"),
  },
    menuText: {
    color: theme.colors.textSecondary,
    fontSize: wp('4%'),
    width: '87%',
    marginLeft: wp('4%'),
    fontFamily: 'medium' as const,
  },
    menuOptionsContainer: {
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    backgroundColor: theme.colors.surface,

  },
}));

// Export the container component as the main export
export const Group = GroupContainer;
export default Group;
