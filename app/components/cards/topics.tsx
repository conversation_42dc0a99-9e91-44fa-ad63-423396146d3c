import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from 'react-native-popup-menu';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { Image } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import ReportDialog from 'app/components/dialogs/report';
import Document from 'app/components/attachments/Document';
import Audio from 'app/components/attachments/Audio';
import Video from 'app/components/attachments/Video';
import { CustomText } from '../elements/Text';
import { useSelector } from 'react-redux';
import { userId, userToken } from 'app/redux/user/reducer';
import Avatar from 'app/components/elements/Avater';
import { main, topic } from 'app/api';
import { ShowAlert } from 'app/providers/toast';
import { triggerStyles } from 'app/assets/styles/MenuStyles';
import CommentSVG from '../svgReactComponent/CommentSVG';
import { timeAgo } from 'app/helpers/time-ago';
import MenuSVG from 'app/assets/svg/menu.svg';
import MuteGroupSVG from 'app/assets/svg/muteUser.svg';
// import FlagSVG from 'app/assets/svg/flagasInapp.svg';
import EditTopicSVG from 'app/assets/svg/edit-3.svg';
import UnMuteSVG from 'app/assets/svg/unmuteUser.svg';
import { showModal, hideModal } from 'app/providers/modals';
import ThumbSVG from '../svgReactComponent/ThumSVG';
import FlagSVG from '@/app/assets/svg/flagAsInUser.svg';

import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import React, {
  useContext,
  useState,
  useEffect,
  useRef,
  FunctionComponent,
} from 'react';
import FollowButton from '../elements/FollowButton';
import { IsLoggedIn, userAuthInfo } from 'app/redux/user/reducer';
import { logout } from 'app/redux/user/hooks';
import { useTranslation } from 'react-i18next';
import { Axios } from 'app/api/axios';
import { DeleteSVG } from 'app/providers/svg/loader';
import { useSingleTopic } from 'app/redux/topic/hooks';
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createScalableThemedStyles } from 'app/utils/createThemedStyles';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
export type TopicProps = {
  navigation: any;
  item?: any;
  convo?: boolean;
  onToggleMuted?: () => void; // Function that handles the mute/unmute logic
  muted?: boolean;
  refreshFunction?: () => void;
  route?: any;
};

// Container Component
const TopicCardContainer: FunctionComponent<TopicProps> = ({
  item,
  onToggleMuted,
  navigation,
  convo = false,
  refreshFunction,
}) => {
  console.log(item, 'item');
  const isLoggedIn = useSelector(IsLoggedIn);
  const Id = useSelector(userId);
  const token = useSelector(userToken);
  const userAuth = useSelector(userAuthInfo);
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const myId = useSelector(userId);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});
  const { SlideInMenu } = renderers;
  const [isLiked, setIsLiked] = useState(false);
  const [numLikes, setNumLikes] = useState(0);
  const [isMuted, setIsMuted] = useState<boolean>(item.muted);
  const blurhash =
    '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

  const handleToggleMuted = () => {
    setIsMuted((prevMuted) => !prevMuted);
    if (onToggleMuted) {
      onToggleMuted();
    }
  };

  useEffect(() => {
    if (item.isLiked && item.likes) {
      setIsLiked(item.isLiked);
      setNumLikes(item.likes);
    }

    console.log(JSON.stringify(item.image), '==see topic content');
  }, [item]);

  function handleLike() {
    setIsLiked(!isLiked);
    setNumLikes(isLiked ? numLikes - 1 : numLikes + 1);

    topic
      .likeTopic(item._id)
      .then((data) => {
        refreshFunction!();
      })
      .catch((err) => {
        setIsLiked(!isLiked);
        setNumLikes(isLiked ? numLikes + 1 : numLikes - 1);
      });
  }

  async function removeFromConversation() {
    topic.removeFromConvos(item._id).then((data) => {
      console.log(data);
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'This topic has now been removed from your convos',
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Removed Successfully',
      // });
      refreshFunction!();
    });
  }

  async function deleteFromTopic() {
    topic.deleteTopic(item._id).then((data) => {
      console.log(data);
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Topic deleted',
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Removed Successfully',
      // });
      refreshFunction!();
    });
  }

  function muteNotification() {
    console.log('got here');
    let payload = {
      contentId: item._id,
      type: 'topic',
      action: item.muted ? 'unmute' : 'mute',
    };
    console.log(payload);
    console.log(item, 'itemss');
    main.muteNotifications(payload).then((data) => {
      console.log(data);

      const message = item.muted
        ? 'Topic Unmuted!'
        : 'Topic muted. You will no longer receive notifications from this topic';
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Muted Successfully',
      // });
      refreshFunction!();
    });
  }

  function openReportDetails(item: any, type: string) {
    setReportDetails({ data: { _id: item._id }, type });
    setShowReportDialog(true);
  }

  return (
    <TopicCardPresentation
      theme={theme}
      styles={styles}
      item={item}
      navigation={navigation}
      t={t}
      myId={myId}
      isLiked={isLiked}
      numLikes={numLikes}
      isMuted={isMuted}
      showReportDialog={showReportDialog}
      reportDetails={reportDetails}
      blurhash={blurhash}
      handleLike={handleLike}
      handleToggleMuted={handleToggleMuted}
      muteNotification={muteNotification}
      deleteFromTopic={deleteFromTopic}
      removeFromConversation={removeFromConversation}
      openReportDetails={openReportDetails}
      setShowReportDialog={setShowReportDialog}
      convo={convo}
      SlideInMenu={SlideInMenu}
    />
  );
};

// Presentation Component
interface TopicCardPresentationProps {
  theme: any;
  styles: any;
  item: any;
  navigation: any;
  t: any;
  myId: string;
  isLiked: boolean;
  numLikes: number;
  isMuted: boolean;
  showReportDialog: boolean;
  reportDetails: any;
  blurhash: string;
  handleLike: () => void;
  handleToggleMuted: () => void;
  muteNotification: () => void;
  deleteFromTopic: () => void;
  removeFromConversation: () => void;
  openReportDetails: (item: any, type: string) => void;
  setShowReportDialog: (show: boolean) => void;
  convo: boolean;
  SlideInMenu: any;
}

const TopicCardPresentation: React.FC<TopicCardPresentationProps> = ({
  theme,
  styles,
  item,
  navigation,
  t,
  myId,
  isLiked,
  numLikes,
  isMuted,
  showReportDialog,
  reportDetails,
  blurhash,
  handleLike,
  handleToggleMuted,
  muteNotification,
  deleteFromTopic,
  removeFromConversation,
  openReportDetails,
  setShowReportDialog,
  convo,
  SlideInMenu,
}) => {
  return (
    <TouchableOpacity
      onPress={() =>
        navigation.navigate('Common', {
          screen: 'view-topic',
          params: {
            postid: item._id,
          },
        })
      }
      style={styles.container}
    >
      <ReportDialog
        show={showReportDialog}
        setShow={setShowReportDialog}
        reportDetails={reportDetails}
      />

      <View style={styles.avatarContainer}>
        <TouchableOpacity
          style={styles.avatarTouchable}
          onPress={() => {
            if (item?.userId?._id != myId) {
              navigation.navigate('Common', {
                screen: 'private-chat',
                params: {
                  userDetails: item?.userId,
                },
              });
            }
          }}
        >
          <View style={styles.avatarWrapper}>
            <Avatar source={item?.userId?.profile_picture} size={47} />
          </View>
        </TouchableOpacity>
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          <View style={styles.nameContainer}>
            <TouchableOpacity
              style={styles.nameTouchable}
              onPress={() => {
                if (item?.userId?._id != myId) {
                  navigation.navigate('Common', {
                    screen: 'private-chat',
                    params: {
                      userDetails: item?.userId,
                    },
                  });
                }
              }}
            >
              <CustomText
                adjustsFontSizeToFit={false}
                numberOfLines={1}
                ellipsizeMode='tail'
                style={styles.userName}
              >
                {`${item?.userId?.first_name} ${item?.userId?.last_name}`.slice(
                  0,
                  35
                ) +
                  (item?.userId?.first_name?.length +
                    item?.userId?.last_name?.length >
                  35
                    ? '...'
                    : '')}
              </CustomText>
            </TouchableOpacity>

            <TouchableOpacity style={{}}></TouchableOpacity>
          </View>

          <View style={styles.menuContainer}>
            <Menu renderer={SlideInMenu}>
              <MenuTrigger>
                <View style={styles.menuTrigger}>
                  <MenuSVG width={15} height={15} />
                </View>
              </MenuTrigger>

              <MenuOptions
                customStyles={{
                  optionText: [styles.menuText],
                  optionsContainer: [styles.menuOptionsContainer],
                }}
              >
                <View style={styles.menuContent}>
                  {myId == item?.userId?._id ? (
                    <MenuOption
                      onSelect={() => {
                        navigation.navigate('Common', {
                          screen: 'edit-topic',
                          params: {
                            topicData: item,
                          },
                        });
                      }}
                    >
                      <View style={styles.menuItem}>
                        <EditTopicSVG width={20} />
                        <CustomText style={styles.menuItemText}>
                          Edit post
                        </CustomText>
                      </View>
                    </MenuOption>
                  ) : null}

                  {myId !== item?.userId?._id && (
                    <MenuOption>
                      <FollowButton userId={item.userId?._id} />
                    </MenuOption>
                  )}

                  <MenuOption onSelect={() => muteNotification()}>
                    <View style={styles.menuItem}>
                      {item.muted ? (
                        <UnMuteSVG width={20} />
                      ) : (
                        <MuteGroupSVG width={20} />
                      )}
                      <CustomText style={styles.menuItemText}>
                        {item.muted ? 'Unmute post' : 'Mute post'}
                      </CustomText>
                    </View>
                  </MenuOption>
                  {myId == item?.userId?._id ? (
                    <MenuOption onSelect={() => deleteFromTopic()}>
                      <View style={styles.menuItem}>
                        <DeleteSVG width={20} height={20} />
                        <CustomText style={styles.deleteMenuItemText}>
                          Delete post
                        </CustomText>
                      </View>
                    </MenuOption>
                  ) : null}

                  {convo ? (
                    <MenuOption onSelect={() => removeFromConversation()}>
                      <View style={styles.menuItem}>
                        <FlagSVG width={20} />
                        <CustomText style={styles.menuItemText}>
                          {t('convos_SubMenu')}
                        </CustomText>
                      </View>
                    </MenuOption>
                  ) : null}
                  {myId !== item?.userId?._id && (
                    <MenuOption
                      onSelect={() => openReportDetails(item, 'topic')}
                    >
                      <View style={styles.menuItem}>
                        <FlagSVG width={20} />
                        <CustomText style={styles.menuItemText}>
                          {t('Topics_flag')}
                        </CustomText>
                      </View>
                    </MenuOption>
                  )}
                </View>
              </MenuOptions>
            </Menu>
          </View>
        </View>

        <View style={styles.userInfoContainer}>
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={2}
            style={styles.userPosition}
          >
            {item.userId?.company_position && item.userId.company
              ? `${
                  item.userId.company_position?.replace(/(\r\n|\n|\r)/gm, '') ||
                  'position'
                } at ${item.userId.company}`
              : 'Position at Company'}
          </CustomText>

        </View>
        <View style={styles.userInfoContainer}>
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={2}
            style={styles.userSubscription}
          >
            {item.userId?.subscriptionType
              ? `${
                  item.userId.subscriptionType
                    .charAt(0)
                    .toUpperCase() +
                  item.userId.subscriptionType.slice(1)
                } Member`
              : 'Free Member'}
          </CustomText>
        </View>

        <View style={styles.timeContainer}>
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={1}
            style={styles.timeText}
          >
            {timeAgo(item.createdAt)}
          </CustomText>
        </View>
        <View style={styles.postContentContainer}>
          <View style={styles.postContent}>
            <CustomText style={styles.postText}>
              {item.content}
            </CustomText>

            {item.image ? (
              <TouchableOpacity
                style={styles.imageContainer}
                onPress={() =>
                  navigation.push('Common', {
                    screen: 'fullscreen-image',
                    params: {
                      image: item.image,
                    },
                  })
                }
              >
                <Image
                  style={styles.postImage}
                  source={{ uri: item.image }}
                />
              </TouchableOpacity>
            ) : null}
            {item.audio ? (
              <View style={styles.attachment}>
                <Audio
                  attachmentSize={item.attachmentSize}
                  link={item.audio}
                  navigation={navigation}
                />
              </View>
            ) : null}

            {item.video ? (
              <View style={styles.attachment}>
                <Video
                  attachmentSize={item.attachmentSize}
                  link={item.video}
                  navigation={navigation}
                />
              </View>
            ) : null}

            {item.document ? (
              <View style={styles.attachment}>
                <Document
                  attachmentSize={item.attachmentSize}
                  link={item.document}
                  navigation={navigation}
                />
              </View>
            ) : null}
          </View>
        </View>
        <View style={styles.actionsContainer}>
          <View style={styles.commentsSection}>
            <CommentSVG
              color={theme.colors.textPlaceholder}
              style={styles.commentIcon}
            />

            <CustomText style={styles.commentsText}>
              {item.noOfComments} {t('Topic_Comments')}
              {item.noOfComments > 1 ? 's' : ''}
            </CustomText>
          </View>

          <TouchableOpacity
            onPress={() => handleLike()}
            style={styles.likesSection}
          >
            <ThumbSVG style={{}} color={isLiked ? '#FE3233' : theme.colors.textPlaceholder} />

            <CustomText style={[styles.likesText, { color: isLiked ? '#FE3233' : theme.colors.textPlaceholder }]}>
              {numLikes} {t('comments_likes')}
              {numLikes > 1 ? 's' : ''}
            </CustomText>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

// Themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    width: '100%',
    flex: 1,
    flexDirection: 'row' as const,
    justifyContent: 'flex-start' as const,
    alignItems: 'flex-start' as const,
    backgroundColor: theme.colors.cardBackground,
    paddingVertical: rv(8),
  },
  avatarContainer: {
    width: '20%',
    justifyContent: 'flex-end' as const,
  },
  avatarTouchable: {
    alignItems: 'flex-end' as const,
  },
  avatarWrapper: {
    marginBottom: 10,
    marginRight: 15,
  },
  contentContainer: {
    width: '80%',
    flexDirection: 'column' as const,
  },
  headerRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    marginRight: wp('5%'),
  },
  nameContainer: {
    flexDirection: 'row' as const,
  },
  nameTouchable: {
    width: '85%',
    justifyContent: 'center' as const,
  },
  userName: {
    color: theme.colors.text,
    fontSize: fs("LG"),
    flexWrap: 'nowrap' as const,
    fontFamily: 'bold' as const,
  },
  menuContainer: {
    alignItems: 'center' as const,
    justifyContent: 'flex-end' as const,
    flexWrap: 'nowrap' as const,
    flexDirection: 'row' as const,
  },
  menuTrigger: {
    alignItems: 'flex-end' as const,
    flexDirection: 'row' as const,
    padding: rv(3),
  },
  menuOptionsContainer: {
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    backgroundColor: theme.colors.surface,

  },
  menuContent: {
    margin: 5,
    flexDirection: 'column' as const,
    marginVertical: 10,
    padding: 15,
  },
  menuItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: rv(10),
  },
  menuItemText: {
    color: theme.colors.text,
    fontSize: fs("BASE"),
    marginLeft: rv(5),
    fontFamily: 'medium' as const,
  },
  deleteMenuItemText: {
    color: theme.colors.error,
    fontSize: fs("BASE"),
    marginLeft: rv(5),
    fontFamily: 'medium' as const,
  },
  menuText: {
    color: theme.colors.textSecondary,
    fontSize: wp('4%'),
    width: '87%',
    marginLeft: wp('4%'),
    fontFamily: 'medium' as const,
  },
  userInfoContainer: {
    width: '100%',
    display: 'flex' as const,
    flexDirection: 'column' as const,
  },
  userPosition: {
    flexDirection: 'row' as const,
    width: '80%',
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: 'medium' as const,
  },
  userSubscription: {
    flexDirection: 'row' as const,
    width: '80%',
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: 'medium' as const,
  },
  timeContainer: {
    alignItems: 'flex-start' as const,
  },
  timeText: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    flexWrap: 'nowrap' as const,
    fontFamily: 'medium' as const,
  },
  postContentContainer: {
    marginRight: wp('5%'),
  },
  postContent: {
    flexDirection: 'column' as const,
  },
  postText: {
    fontSize: fs("MD"),
    color: theme.colors.textSecondary,
    lineHeight: 20,
    fontFamily: 'medium' as const,
  },
  imageContainer: {
    flex: 1,
    width: '100%',
  },
  postImage: {
    width: '100%',
    height: hp('30%'),
    borderRadius: wp('2%'),
    borderColor: theme.colors.border,
    borderWidth: wp('0.6%'),
    marginTop: rv(4),
  },
  attachment: {
    marginVertical: rv(8),
    width: wp('68%'),
  },
  actionsContainer: {
    flexDirection: 'row' as const,
    marginTop: 10,
    marginRight: wp('5%'),
    justifyContent: 'space-between' as const,
  },
  commentsSection: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  commentIcon: {
    marginRight: 5,
  },
  commentsText: {
    color: theme.colors.textPlaceholder,
    marginLeft: 3,
    fontSize: fs("SM"),
    paddingTop: 6,
    fontFamily: 'regular' as const,
  },
  likesSection: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  likesText: {
    fontSize: fs("SM"),
    marginLeft: 5,
    marginRight: 40,
    marginTop: 4,
    fontFamily: 'regular' as const,
  },
}));
// Export the container component as the main export
export const TopicCard = TopicCardContainer;
export default TopicCard;
