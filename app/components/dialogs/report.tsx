import React, { useContext, useState } from 'react';
import { StyleSheet, View, Modal, Text, TouchableOpacity } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Ionicons from '@expo/vector-icons/Ionicons';
import Checkbox from 'expo-checkbox';
import { CustomText, CustomButton } from '../elements';
import { useReportUser } from 'app/redux/user/hooks';
import { useReport } from 'app/hooks/useReport';
import { responsiveValue  } from 'app/providers/responsive-value';
import { useTheme } from 'app/hooks/useTheme';

type reportTypes = 'user' | 'comment' | 'group' | 'topic';

type ReportDialogProps = {
  reportDetails: {
    type?: reportTypes;
    data: {
      _id: string;
      description?:string;
    };
  } | any;
  setShow: any;
  show: boolean;
};

type reportCategories = 'spam' | 'harassment' | 'deceit';

export default function ReportDialog(props: ReportDialogProps) {
  const [selectedCategory, setSelectedCategory] = useState<reportCategories | null>(null);
  console.log(selectedCategory,"selected category")
  const [loading, setLoading] = useState(false);
  const { theme } = useTheme();

  const { report, isLoading } = useReport(props.reportDetails.type);

  const reportEntity = async () => {
    setLoading(true);
    try {
      // Call the report function with the appropriate payload
      const payload = {
        _id: props.reportDetails.data._id,
        description: selectedCategory, // Use the selected category as the description
      };
      await report(props.reportDetails.data._id, selectedCategory);
      props.setShow(false);
    } catch (error) {
      console.log("error occurred",error)
      // Handle error
    } finally {
      setLoading(false);
    }
  };

  function onCheckChanged(category: reportCategories) {
    if (selectedCategory === category) {
      setSelectedCategory(null); // Uncheck if it's already selected
    } else {
      setSelectedCategory(category); // Select the new category
    }
  }

  return (
    <View>
      {props.reportDetails && props.reportDetails.data ? (
        <Modal
          transparent={true}
          animationType={'none'}
          visible={props.show}
          onRequestClose={() => {
            props.setShow(false);
          }}
        >
          <View style={styles.modalBackground}>
            <View style={[styles.activityIndicatorWrapper, { backgroundColor: theme.colors.cardBackground }]}>
              <View
                style={{
                  justifyContent: 'space-between',
                  flexDirection: 'row',
                }}
              >
                <CustomText
                  style={{
                    fontSize: responsiveValue(13),
                    textTransform: 'capitalize',
                    color: theme.colors.text,
                  }}
                  textType="bold"
                >
                  Report Posts
                </CustomText>

                <TouchableOpacity onPress={() => props.setShow(false)}>
                  <Ionicons name="close-circle" color={theme.colors.buttonQuaternary} size={30} style={{marginRight: responsiveValue(15)}} />
                </TouchableOpacity>
              </View>
              <CustomText
                style={{
                  marginTop: responsiveValue(20),
                  fontSize: responsiveValue(13),
                  fontFamily: 'semiBold',
                  color: theme.colors.text,
                }}
              >
                Tell us why you are reporting
              </CustomText>

              <View style={{ paddingRight: 25, marginBottom: 30}}>
                <View
                  style={{
                    flexDirection: 'row',
                   
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: responsiveValue(10),
                  }}
                >
                  <CustomText style={{
                    fontSize:responsiveValue(10),
                    color: theme.colors.text,
                  }}>Spam</CustomText>
                  <View>
                    <Checkbox
                      value={selectedCategory === 'spam'}
                      onValueChange={() => onCheckChanged('spam')}
                      color={selectedCategory === 'spam' ? theme.colors.primary : undefined}
                    />
                  </View>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: responsiveValue(10),
                  }}
                >
                  <CustomText style={{
                    fontSize:responsiveValue(10),
                    color: theme.colors.text,
                  }}>Harassment</CustomText>
                  <View>
                    <Checkbox
                      value={selectedCategory === 'harassment'}
                      onValueChange={() => onCheckChanged('harassment')}
                      color={
                        selectedCategory === 'harassment' ? theme.colors.primary : undefined
                      }
                    />
                  </View>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: responsiveValue(10),
                  }}
                >
                  <CustomText style={{
                    fontSize:responsiveValue(10),
                    color: theme.colors.text,
                  }}>Deceit</CustomText>
                  <View>
                    <Checkbox
                      value={selectedCategory === 'deceit'}
                      onValueChange={() => onCheckChanged('deceit')}
                      color={selectedCategory === 'deceit' ? theme.colors.primary : undefined}
                    />
                  </View>
                </View>
              </View>
              <View
                style={{
                  paddingBottom: responsiveValue(10),
                }}
              >
                <CustomButton
                buttonTheme='quaternary'
                  label={`Report ${props.reportDetails.type}`}
                  onPress={() => {
                    reportEntity();
                  }}
                  loading={loading}

                   disabled={!selectedCategory}  // Disable button if no category is selected
                />
              </View>
            </View>
          </View>
        </Modal>
      ) : null}
    </View>
  );
}


const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  activityIndicatorWrapper: {
    width: '85%',
    borderRadius: 10,
    display: 'flex',
    padding: '7%',
    flexDirection: 'column',
  },
  splashbtn: {
    alignContent: 'center',
    alignSelf: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
    height: 60,
    width: 250,
    borderColor: 'black',
    borderWidth: 2,
    marginTop: 1,
    marginBottom: 20,
    borderRadius: 30,
    paddingTop: 25,
    fontStyle: 'normal',
    fontWeight: 'bold',
    fontSize: responsiveValue(13),
    lineHeight: 14,
    textAlign: 'center',
    color: '#F2875D', // This is part of unused splashbtn style
    display: 'flex',
    marginLeft: 20,
    marginRight: 20,
  },
});