import React, { FunctionComponent } from 'react';
import { Image, StyleSheet, View } from 'react-native';
import DefaultProfileSVG from 'app/assets/svg/connectifyDefaultthumbnail.svg';
import DefaultGroupSVG from 'app/assets/svg/connectifyDefaultthumbnail.svg';

type AvatarProps = {
  size?: number;
  source?: string;
  type?: 'user' | 'group';
};
const Avatar: FunctionComponent<AvatarProps> = ({ source, type = 'user', size = 45 }) => {
  const styles = StyleSheet.create({
    image: {
      width: size,
      height: size,
      borderRadius: size / 2,
    },
    svgContainer: {
      width: size,
      height: size,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  if (source) {
    return <Image source={{ uri: source }} style={styles.image} />;
  } else if (type === 'user') {
    return (
      <View style={styles.svgContainer}>
        <DefaultProfileSVG width={size} height={size} />
      </View>
    );
  } else {
    return (
      <View style={styles.svgContainer}>
        <DefaultGroupSVG width={size} height={size} />
      </View>
    );
  }
};

export default Avatar;