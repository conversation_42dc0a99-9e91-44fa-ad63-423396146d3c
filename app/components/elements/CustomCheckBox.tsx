import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';

interface CustomCheckboxProps {
  value: boolean;
  onValueChange: (newValue: boolean) => void;
  color?: string; // Optional color prop for the checkbox's border and checkmark
  size?: number;  // Optional size prop for the checkbox
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  value,
  onValueChange,
  color,  // Color prop for the checkbox's border and checkmark
  size = 24,       // Default size if none is provided
}) => {
  const { theme } = useTheme();

  // Use provided color or fallback to theme colors
  const checkboxColor = color || theme.colors.primary;
  const uncheckedBorderColor = theme.colors.border;

  return (
    <TouchableOpacity onPress={() => onValueChange(!value)} style={styles.checkboxContainer}>
      <View
        style={[
          styles.checkbox,
          {
            width: size,
            height: size,
            borderColor: value ? checkboxColor : uncheckedBorderColor,
            backgroundColor: value ? checkboxColor : 'transparent',
          },
        ]}
      >
        {value && <View style={[styles.checkmark, { backgroundColor: theme.colors.background }]} />}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  checkboxContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  checkbox: {
    borderWidth: 2,
    borderRadius: 12,  // Makes the checkbox circular
    justifyContent: 'center',
    alignItems: 'center',
  },

  checkmark: {
    width: 10,
    height: 10,
    borderRadius: 5,  // Makes the checkmark circular
  },
  
});

export default CustomCheckbox;
