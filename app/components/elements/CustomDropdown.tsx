import { useState } from "react";
import { FlatList, TextInput, TouchableOpacity, View, Text, StyleSheet } from "react-native";
import { responsiveValue as rv } from "app/providers/responsive-value";
import { useTheme } from 'app/hooks/useTheme';
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";
import { createOptimizedThemedStyles } from "app/theme";

interface CustomDropdownProps {
    options: { id: string; name: string }[];
    // Ensure it matches what you're passing
    selectedValue: string;
    onSelect: (value: string) => void;
    placeholder?: string;
  }
  
  const CustomDropdown: React.FC<CustomDropdownProps> = ({
    options,
    selectedValue,
    onSelect,
    placeholder
  }) => {
    const [showDropdown, setShowDropdown] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");

      const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  
    // Ensure data is always an array
    const filteredData = options.filter((item) =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  
    return (
      <View style={styles.container}>
        {/* Dropdown Button */}
        <TouchableOpacity
          onPress={() => setShowDropdown(!showDropdown)}
          style={styles.dropdownButton}
        >
          <Text style={styles.selectedText}>
            {selectedValue
              ? options.find((item) => item.id === selectedValue)?.name
              : placeholder}
          </Text>
          <Text style={styles.arrow}>▼</Text>
        </TouchableOpacity>
  
        {/* Dropdown List */}
        {showDropdown && (
          <View style={styles.dropdownContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Search..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
  
            <FlatList
              data={filteredData}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.dropdownItem,
                    selectedValue === item.id && styles.selectedItem,
                  ]}
                  onPress={() => {
                    onSelect(item.id);
                    setShowDropdown(false);
                  }}
                >
                  <Text style={styles.itemText}>{item.name}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        )}
      </View>
    );
  };
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
    dropdownItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: rv(10),
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
      },
    
      dropdownContainer: {
        maxHeight: 200, // Set a maximum height for the dropdown
        backgroundColor: '#F4F4F4',
        
        borderWidth: 2,
        borderColor: '#F5F5F5',
        marginTop: 5,
    
      },
      container: {
        flex: 1,
    
    
      },
      label: {
        fontSize: 16,
        fontFamily: 'medium',
        marginBottom: 10,
      },
      dropdown: {
    
        paddingHorizontal: 20,
        backgroundColor: 'transparent',
        marginBottom: 5,
        borderStyle: 'solid',
    
        fontSize: fs("MD"),
        fontFamily: 'regular',
        borderWidth: 2,
        borderColor: '#F5F5F5',
        color: '#696969',
        width: '100%',
        height: rv(42),
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
      },
      dropdownText: {
        color: '#98A2B3',
        marginLeft: rv(10)
      },
      searchInput: {

      },
      arrow : {

      },
      selectedText: {

      },
      itemText : {

      },
      selectedItem: {

      }, 

      dropdownButton :{

      }
  }))
  
  export default CustomDropdown