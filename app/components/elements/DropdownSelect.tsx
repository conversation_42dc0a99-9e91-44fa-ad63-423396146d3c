import React, { ReactNode, useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, StyleSheet } from 'react-native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Arrow from '@/app/assets/svg/lucosa-arrow.svg'; // Import your arrow icon component
import { useTheme } from 'app/hooks/useTheme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
import { createOptimizedThemedStyles } from 'app/theme';

interface DropdownSelectProps {
  data: {
    icon: ReactNode;
    label: string;
    value: string;
  }[]; // Array of objects containing label, icon, and value
  selectedValue: string; // Selected value
  onSelect: (value: string) => void; // Function to handle selection
  placeholder?: string; // Optional placeholder text
}

const DropdownSelect: React.FC<DropdownSelectProps> = ({
  data,
  selectedValue,
  onSelect,
  placeholder = 'Select an option'
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
    const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  return (
    <View style={{ marginTop: 5 }}>
      {/* Select Button */}
      <TouchableOpacity
        onPress={() => setShowDropdown(!showDropdown)}
        style={styles.dropdown}
      >
        <Text style={styles.text}>
          {selectedValue ? selectedValue : placeholder}
        </Text>
        <Arrow width={13} />
      </TouchableOpacity>

      {/* Dropdown Options using FlatList */}
      {showDropdown && (
        <FlatList
          data={data}
          keyExtractor={(item) => item.value}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => {
                onSelect(item.value);
                setShowDropdown(false);
              }}
              style={[
                styles.item,
                selectedValue === item.value && styles.selectedValue
              ]}
            >
              <View style={styles.row}>
                {item.icon}
                <Text style={styles.itemText}>{item.label}</Text>
              </View>
            </TouchableOpacity>
          )}
          style={[styles.dropdownContainer]} // Limit height to roughly one option
        />
      )}
    </View>
  );
};

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  dropdown: {
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
    marginBottom: 5,
    borderStyle: 'solid',
    fontSize: fs("MD"),
    fontFamily: 'regular',
    borderWidth: 2,
    borderColor: theme.colors.border,
       color: theme.colors.textSecondary,
    width: '100%',
    height: rv(42),
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  text: {
      fontSize: fs("MD"),
       color: theme.colors.textSecondary,
    fontFamily: 'regular'
  },
  dropdownContainer: {
    borderWidth: 2,
      borderColor: theme.colors.border,
     backgroundColor: theme.colors.surface,
    // The maxHeight is added inline in the component so that it can be adjusted as needed.
  },
  item: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd'
  },
  itemText: {
     fontSize: fs("MD"),
    color: theme.colors.textSecondary,
  },
  row: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: rv(10)
  },
  selectedValue:{
     backgroundColor: theme.colors.surfaceVariant,

  }

}));

export default DropdownSelect;
