import React, { useState, useEffect } from 'react';
import { TouchableOpacity, View } from 'react-native';
import {
  useIsFollowingUser,
  useFollowUser,
  useUnfollowUser,
} from 'app/redux/follow/hooks';
import { CustomText } from './Text';
import FollowSVG from 'app/assets/svg/followUser.svg'
import { responsiveValue as rv } from 'app/providers/responsive-value';
import UnFollowSVG from 'app/assets/svg/unfollowUser.svg'
import { useTheme, createThemedStyles, createOptimizedThemedStyles } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

interface FollowButtonProps {
  userId?: string;
}

// Presentation component props
interface FollowButtonPresentationProps extends FollowButtonProps {
  styles: any;
  isFollowing: boolean;
  handleFollowToggle: () => void;
}

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  button: {
    width: 85,
    borderRadius: 12,
    borderWidth: 1,
    padding: 4,
    paddingHorizontal: 10,
    borderColor: theme.colors.accent,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontFamily: 'regular',
    fontSize: fs("MD"),
    color: theme.colors.text,
  },
}));

// Container Component (handles logic, state, theme)
const FollowButtonContainer = ({ userId }: FollowButtonProps) => {
 const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const { data } = useIsFollowingUser(userId);
  const { mutateAsync: followUser } = useFollowUser();
  const { mutateAsync: unfollowUser } = useUnfollowUser();
  const [isFollowing, setIsFollowing] = useState(false);

  const handleFollowToggle = async () => {
    try {
      if (isFollowing) {
        await unfollowUser(userId);
        setIsFollowing(false);
      } else {
        await followUser(userId);
        setIsFollowing(true);
      }
    } catch (error) {
      console.error('Error updating follow status', error);
    }
  };

  useEffect(() => {
    setIsFollowing(data?.data?.isFollowing || false);
  }, [data]);

  return (
    <FollowButtonPresentation
      styles={styles}
      isFollowing={isFollowing}
      handleFollowToggle={handleFollowToggle}
      userId={userId}
    />
  );
};

// Presentational Component (pure UI)
const FollowButtonPresentation = ({
  styles,
  isFollowing,
  handleFollowToggle
}: FollowButtonPresentationProps) => {
  return (
    <TouchableOpacity onPress={handleFollowToggle}>
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          alignItems: 'center',
          gap: rv(5),
          marginBottom: rv(10)
        }}
      >
        <View>
          {isFollowing ? (
           <UnFollowSVG width={20} />
          ) : (
           <FollowSVG width={20} />
          )}
        </View>
        <CustomText style={styles.buttonText}>
          {isFollowing ? 'Unfollow' : 'Follow'}
        </CustomText>
      </View>
    </TouchableOpacity>
  );
};

// Export container as default
const FollowButton = FollowButtonContainer;

export default FollowButton;
