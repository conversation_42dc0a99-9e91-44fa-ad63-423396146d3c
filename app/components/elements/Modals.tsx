import React from "react";
import {
  Modal,
  View,
  Text,
  Button,
  StyleSheet,
  Image,
  TouchableOpacity,
} from "react-native";
import { responsiveValue as rv } from "app/providers/responsive-value";
// import { useNavigation } from '@react-navigation/native';
import { SupportSVG } from "app/providers/svg/loader";
import { useTranslation } from "react-i18next";
import { CustomText } from "./Text";
import { useTheme } from "app/hooks/useTheme";
import {
  createOptimizedThemedStyles,
  createThemedStyles,
} from "app/utils/createThemedStyles";
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";
interface Props {
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  NavigateToCreditPage?: () => void;
  handleConfirm: () => void;
  handleAlert?: () => void;
  title?: string;
  message?: string;
  type:
    | "credit"
    | "stake"
    | "cashout"
    | "ultimate"
    | "helpdesk"
    | "new-credit"
    | "win-game"
    | "minimum"
    | "alert"
    | "success-alert"
    | "share-alert"
    | "support-us"
    | "delete"
    | "win-spin-the-wheel"
    | "lose-spin-the-wheel"
    | "error-alert"
    | "confirm-alert"; // Include 'cashout' type here
  navigation?: any;
}

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
  },
  centeredView: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent background
  },
  modalView: {
    minWidth: rv(200),
    paddingTop: 20,
  },
  modalViews: {
    margin: 20,
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    padding: 15,
    alignItems: "center",
  },
  modalText: {
    color: theme.colors.text,
    fontSize: fs("MD"),
    fontFamily: "regular",
    marginBottom: 15,
    textAlign: "center",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  buttonContainer2: {
    flexDirection: "row",
    justifyContent: "center",
  },
  closeButton: {
    position: "absolute",
    top: 10,
    right: 10,
    padding: 10,
    zIndex: 1,
  },
  closeButtonText: {
    fontSize: fs(""),
    fontFamily: "bold",
  },
  cancelButton: {
    backgroundColor: theme.colors.border,
    paddingHorizontal: rv(20),
    paddingVertical: rv(10),
    borderRadius: rv(8),
    marginRight: rv(10),
  },
  okButton: {
    display: "flex",
    minWidth: rv(100),
    paddingHorizontal: rv(20),
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: rv(7),
    borderRadius: 10,
    backgroundColor: theme.colors.primary,
  },
  cashoutButton: {
    width: "43%",
    borderRadius: 10,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    paddingHorizontal: rv(30),
    paddingVertical: 10,
  },
  font_thirteen: {
    fontSize: fs("MD"),
  },
  font_eleven: {
    fontSize: fs("SM"),
  },
}));

const CustomModal: React.FC<Props> = ({
  modalVisible,
  title,
  message,
  setModalVisible,
  NavigateToCreditPage,
  handleConfirm,
  handleAlert,
  type,
  navigation,
}) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const handleCancel = () => {
    setModalVisible(false);
  };

  const { t } = useTranslation();
  const handleNavigateToCredit = () => {
    setModalVisible(false);
    if (type === "helpdesk") {
      navigation.goBack();
      return;
    }
    navigation.navigate("Credit" as never);
  };

  const handleNavigateToQuiz = () => {
    navigation.navigate("QuizScreen" as never);
  };

  console.log(type, "type");

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={modalVisible}
      onRequestClose={handleCancel}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalViews}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => {
              if (type === "lose-spin-the-wheel") {
                handleNavigateToCredit();
              } else {
                handleCancel();
              }
            }}
          >
            <Image
              source={require("../../assets/Close.png")}
              style={{ width: 30, height: 30 }}
            />
          </TouchableOpacity>
          {type === "credit" ||
          type === "stake" ||
          type === "ultimate" ||
          type === "new-credit" ||
          type === "minimum" ||
          type === "alert" ||
          type === "error-alert" ||
          type === "delete" ||
          type === "lose-spin-the-wheel" ? (
            <Image
              source={
                type === "error-alert"
                  ? require("../../assets/warning.png")
                  : require("../../assets/warning.png")
              }
              style={{ width: rv(40), height: rv(40) }}
            />
          ) : type === "win-game" ? (
            <Image
              source={require("../../assets/Frame.png")}
              style={{ width: rv(40), height: rv(40) }}
            />
          ) : type === "support-us" || type === "share-alert" ? (
            <SupportSVG
              width={rv(40)}
              height={rv(40)}
              style={{ marginRight: rv(10) }}
            />
          ) : (
            <Image
              source={require("../../assets/Frame.png")}
              style={{ width: rv(40), height: rv(40) }}
            />
          )}
          <View style={styles.modalView}>
            <Text
              style={[
                styles.font_thirteen,
                {
                  fontFamily: "semiBold",

                  textAlign: "center",
                  color: "#4F4F4F",
                  paddingBottom: rv(10),
                },
              ]}
            >
              {type === "error-alert" || type === "alert"
                ? title || "Error"
                : type === "success-alert"
                ? title || "Success"
                : type === "credit"
                ? "No Subscription"
                : type === "cashout"
                ? t("infoReceived")
                : type === "new-credit"
                ? t("woah_there")
                : type === "ultimate"
                ? t("insufficient_pennytots")
                : type === "helpdesk"
                ? t("infoReceived")
                : type === "minimum"
                ? t("oops")
                : type === "win-game"
                ? t("received")
                : type === "support-us"
                ? t("support_us")
                : type === "share-alert"
                ? "Share Alert"
                : type === "delete"
                ? title
                : type === "win-spin-the-wheel"
                ? t("ready_to_cash_in")
                : type === "lose-spin-the-wheel"
                ? t("gameOver")
                : t("forfeitChallenge")}
            </Text>
            <CustomText style={styles.modalText}>
              {type === "error-alert" ||
              type === "alert" ||
              type === "success-alert"
                ? message
                : type === "credit"
                ? "You are out of access days. Click Ok to renew"
                : type === "new-credit"
                ? t("youve_mastered_the_game_mode_t")
                : type === "stake"
                ? t("leaving_now_means_losing_your_")
                : type === "cashout"
                ? t("submit_thanks")
                : type === "ultimate"
                ? t("playPennytot")
                : type === "helpdesk"
                ? t("contact_thanks")
                : type === "minimum"
                ? t("min_purchase")
                : type === "win-game"
                ? t("we_will_be_in_contact_shortly")
                : type === "support-us"
                ? t("support_message")
                : type === "share-alert"
                ? "Invite fellow alumni or students"
                : type === "win-spin-the-wheel"
                ? t("end_game_prompt")
                : type === "lose-spin-the-wheel"
                ? t("play_again")
                : t("out_of_pennytots")}
            </CustomText>
            {type === "cashout" && (
              <View style={{ alignItems: "center" }}>
                <TouchableOpacity
                  style={styles.cashoutButton}
                  onPress={handleNavigateToCredit}
                >
                  <Text
                    style={[
                      styles.font_thirteen,
                      {
                        fontFamily: "regular",
                        color: "white",
                        alignSelf: "center",
                      },
                    ]}
                  >
                    {t("Okay")}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
            <View
              style={
                type === "ultimate" ||
                type === "helpdesk" ||
                type === "minimum" ||
                type === "alert" ||
                type === "error-alert" ||
                type === "success-alert" ||
                type === "win-game" ||
                type === "support-us" ||
                type === "new-credit"
                  ? styles.buttonContainer2
                  : styles.buttonContainer
              }
            >
              {type === "ultimate" ||
              type === "helpdesk" ||
              type === "minimum" ||
              type === "alert" ||
              type === "error-alert" ||
              type === "success-alert" ||
              type === "win-game" ? (
                title === "Confirm Downgrade" ? (
                  <>
                    <TouchableOpacity
                      style={styles.cancelButton}
                      onPress={handleAlert || handleCancel}
                    >
                      <CustomText
                        style={[
                          styles.font_thirteen,
                          { fontFamily: "regular", color: "#163E23" },
                        ]}
                      >
                        Cancel
                      </CustomText>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.okButton}
                      onPress={handleConfirm}
                    >
                      <CustomText
                        style={[
                          styles.font_thirteen,
                          { fontFamily: "regular", color: "white" },
                        ]}
                      >
                        Confirm
                      </CustomText>
                    </TouchableOpacity>
                  </>
                ) : (
                  <TouchableOpacity
                    style={{ ...styles.okButton, alignItems: "center" }}
                    onPress={
                      type === "alert" ||
                      type === "error-alert" ||
                      type === "success-alert"
                        ? () => {
                            if (typeof handleAlert === "function") {
                              handleAlert();
                            } else {
                              handleConfirm();
                            }
                          }
                        : handleConfirm
                    }
                  >
                    <CustomText
                      style={[
                        styles.font_thirteen,
                        { fontFamily: "regular", color: "white" },
                      ]}
                    >
                      {t("ok")}
                    </CustomText>
                  </TouchableOpacity>
                )
              ) : type === "confirm-alert" ? (
                <>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={handleAlert || handleCancel}
                  >
                    <CustomText
                      style={[
                        styles.font_thirteen,
                        { fontFamily: "regular", color: "#163E23" },
                      ]}
                    >
                      Cancel
                    </CustomText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.okButton}
                    onPress={handleConfirm}
                  >
                    <CustomText
                      style={[
                        styles.font_thirteen,
                        { fontFamily: "regular", color: "white" },
                      ]}
                    >
                      Confirm
                    </CustomText>
                  </TouchableOpacity>
                </>
              ) : type === "credit" ||
                type === "cashout" ||
                type === "delete" ? (
                <>
                  {(type === "credit" || type === "delete") && (
                    <>
                      <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={handleCancel}
                      >
                        <CustomText
                          style={[
                            styles.font_eleven,
                            { fontFamily: "regular", color: "#163E23" },
                          ]}
                        >
                          Cancel
                        </CustomText>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.okButton}
                        onPress={
                          type === "delete"
                            ? handleConfirm
                            : handleNavigateToCredit
                        }
                      >
                        <CustomText
                          style={[
                            styles.font_eleven,
                            { fontFamily: "regular", color: "white" },
                          ]}
                        >
                          {t("ok")}
                        </CustomText>
                      </TouchableOpacity>
                    </>
                  )}
                </>
              ) : type == "new-credit" ? (
                <>
                  <TouchableOpacity
                    style={{ ...styles.okButton }}
                    onPress={handleConfirm} // assuming you have a handler for this
                  >
                    <CustomText
                      style={[styles.font_thirteen, { fontFamily: "regular" }]}
                    >
                      {t("sounds_great")}
                    </CustomText>
                  </TouchableOpacity>
                </>
              ) : type == "support-us" ? (
                <>
                  <TouchableOpacity
                    style={{ ...styles.okButton, alignItems: "center" }}
                    onPress={handleConfirm}
                  >
                    <CustomText
                      style={[styles.font_thirteen, { fontFamily: "regular" }]}
                    >
                      {t("sure")}
                    </CustomText>
                  </TouchableOpacity>
                </>
              ) : type == "lose-spin-the-wheel" ? (
                <>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={handleNavigateToCredit}
                  >
                    <CustomText
                      style={[styles.font_thirteen, { fontFamily: "regular" }]}
                    >
                      {t("no")}
                    </CustomText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.okButton}
                    onPress={handleConfirm}
                  >
                    <Text
                      style={[styles.font_thirteen, { fontFamily: "regular" }]}
                    >
                      {t("yes")}
                    </Text>
                  </TouchableOpacity>
                </>
              ) : type == "share-alert" ? (
                <>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={handleCancel}
                  >
                    <CustomText
                      style={[styles.font_thirteen, { fontFamily: "regular" }]}
                    >
                      {"Cancel"}
                    </CustomText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.okButton}
                    onPress={handleConfirm}
                  >
                    <Text
                      style={[
                        styles.font_thirteen,
                        { fontFamily: "regular", color: "white" },
                      ]}
                    >
                      {"Share"}
                    </Text>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={handleConfirm}
                  >
                    <CustomText
                      style={[styles.font_thirteen, { fontFamily: "regular" }]}
                    >
                      {t("leave")}
                    </CustomText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.okButton}
                    onPress={handleCancel}
                  >
                    <CustomText
                      style={[styles.font_thirteen, { fontFamily: "regular" }]}
                    >
                      {t("stay")}
                    </CustomText>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default CustomModal;
