import { ViewStyle } from 'react-native';
import React, { useMemo } from 'react';
import SelectDropdown from 'react-native-select-dropdown';
import Ionicons from '@expo/vector-icons/Ionicons';
import { CommonStyles } from 'app/assets/styles';
import { responsiveValue } from 'app/providers/responsive-value';

interface ISelectData {
  label: string;
  value: string;
}

interface ISelect {
  data: ISelectData[];
  value: any;
  search?: boolean;
  style?: ViewStyle;
  onSelect: (value: string, index: number) => void;
}

const Select = ({
  data,
  value,
  onSelect,
  search,
  style = {},
  ...rest
}: ISelect) => {
  const selectedIndex = useMemo(() => {
    if (value) {
      const index = data.findIndex((item) => item.value === value);
      console.log("Selected Index:", index);
      return index !== -1 ? index : undefined;
    }
    return undefined;
  }, [value, data]);

  return (
    <SelectDropdown
      data={data}
      defaultButtonText="Select an Option"
      defaultValueByIndex={selectedIndex}
      onSelect={(selectedItem: ISelectData, index: number) =>
        onSelect(selectedItem.value, index)
      }
      buttonTextAfterSelection={(selectedItem: ISelectData) => {
        return selectedItem.label;
      }}
      rowTextForSelection={(selectedItem: ISelectData) => {
        return selectedItem.label;
      }}
      renderDropdownIcon={(isOpened: boolean) => {
        return (
          <Ionicons
            name={isOpened ? 'chevron-up' : 'chevron-down'}
            size={responsiveValue(14)}
            color="#696969"
          />
        );
      }}
      dropdownIconPosition="right"
      buttonStyle={{
        ...CommonStyles.inputField,
        width: '100%',
        fontSize: responsiveValue(13),
        ...style,
      }}
      buttonTextStyle={{
        fontFamily: 'regular',
        fontSize: responsiveValue(13),
        color: '#696969',
        textAlign: 'left', // Ensures text is left aligned
      }}
      rowTextStyle={{
        fontFamily: 'regular',
        fontSize: responsiveValue(13),
      }}
      rowStyle={{
        borderBottomWidth: 0,
      }}
      search={search}
      {...rest}
    />
  );
};

export default Select;
