import { useState } from "react";
import { TouchableOpacity, Text, View, Modal, FlatList } from "react-native";
import { responsiveValue as rv } from "app/providers/responsive-value";
import Arrow from "@/app/assets/svg/lucosa-arrow.svg";
import countries from "world-countries";
import { Vi } from "react-flags-select";
import { useTheme } from "app/hooks/useTheme";
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";
import { createOptimizedThemedStyles } from "app/theme";

interface CountryItem {
  label: string;
  value: string;
  flag: string;
}

interface SelectProps {
  data: any;
  value: any;
  onSelect: (selected: CountryItem) => void;
}
const SelectCountry: React.FC<SelectProps> = ({ data, value, onSelect }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  return (
    <View>
      {/* Display the selected value */}
      <TouchableOpacity
        style={styles.border}
        onPress={() => setModalVisible(true)}
      >
        <View
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
          }}
        >
          <Text style={styles.countryFlag}>{value?.flag}</Text>
          <Text
            style={styles.countryLabel}
          >
            {value?.label}
          </Text>
        </View>
        <View>
          <Arrow width={13} />
        </View>
      </TouchableOpacity>

      {/* Modal for country selection */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View
          style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
        >
          <View style={styles.container}>
            <Text style={styles.label}>Select a Country</Text>

            <FlatList
              data={data}
              keyExtractor={(item) => item.value}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    padding: 10,
                    borderBottomWidth: 1,
                    borderBottomColor: "#ddd",
                    alignItems: "center",
                  }}
                  onPress={() => {
                    onSelect(item);
                    setModalVisible(false);
                  }}
                >
                  <Text style={styles.countryFlag}>{item.flag}</Text>
                  <Text style={styles.countryLabel}>{item.label}</Text>
                </TouchableOpacity>
              )}
              style={{ maxHeight: rv(400) }}
            />

            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              style={{ marginTop: 20 }}
            >
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    width: "80%",
    backgroundColor: theme.colors.surfaceVariant,
    color: "red",
    borderRadius: 10,
    padding: 10,
  },
  border: {
    flexDirection: "row",
    paddingHorizontal: 20,
    backgroundColor: "transparent",
    marginBottom: 15,
    borderStyle: "solid",
    marginTop: 10,

    borderWidth: 2,
    borderColor: theme.colors.border,

    height: rv(42),
    justifyContent: "space-between",
    alignItems: "center",
  },
  label: {
    fontSize: fs("MD"),
    fontWeight: "bold",
    marginBottom: 10,
    color: theme.colors.textSecondary,
  },

  countryFlag: {
    fontSize: fs("MD"),
    marginRight: 10,
  },
  countryLabel: {
    fontSize: fs("MD"),
    color: theme.colors.textSecondary,

    fontFamily: "regular",

    textAlign: "center",
  },
  cancelText: {
    color: theme.colors.secondary,
    textAlign: "center",
    fontSize: fs("MD"),
  },
}));

export default SelectCountry;
