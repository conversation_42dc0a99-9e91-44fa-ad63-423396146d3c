import React, { useRef, useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, TouchableOpacity, Alert } from 'react-native';
import { Video } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import { useWindowDimensions } from 'react-native';
import Orientation from 'react-native-orientation-locker';

export default function PreviewVideo({ videoLink, style }: any) {
  const video = useRef(null);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [aspectRatio, setAspectRatio] = useState(16 / 9); // Default aspect ratio
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const [orientation, setOrientation] = useState('PORTRAIT');
  

  // Handle orientation changes
  useEffect(() => {
    // Save the current app orientation before modifying
    let initialOrientation = 'PORTRAIT';
    Orientation.getOrientation((currentOrientation) => {
      initialOrientation = currentOrientation;
    });

    // Unlock orientation for this screen only
    Orientation.unlockAllOrientations();

    // Set initial orientation
    Orientation.getOrientation((currentOrientation) => {
      setOrientation(currentOrientation);
    });

    // Listen for orientation changes
    const orientationChangeHandler = (newOrientation) => {
      setOrientation(newOrientation);
    };

    Orientation.addOrientationListener(orientationChangeHandler);

    // Cleanup: Restore the initial orientation when component unmounts
    return () => {
      Orientation.removeOrientationListener(orientationChangeHandler);
      // Lock back to the initial orientation (or app's default, e.g., portrait)
      Orientation.lockToPortrait();
    };
  }, []);

  const handleReadyForDisplay = (event) => {
    const { naturalSize } = event;
    if (naturalSize && naturalSize.width > 0 && naturalSize.height > 0) {
      const calculatedAspectRatio = naturalSize.width / naturalSize.height;
      setAspectRatio(calculatedAspectRatio);
    }
    setIsVideoReady(true);
  };

  const handlePlaybackStatusUpdate = (status) => {
    if (!status.isLoaded) {
      if (status.error) {
        console.error(`Encountered a fatal error during playback: ${status.error}`);
      }
    } else {
      if (status.didJustFinish) {
        setIsPlaying(false);
      } else {
        setIsPlaying(status.isPlaying);
      }
    }
  };

  const handleVideoError = (error) => {
    console.error('Video error:', error);
    Alert.alert('Error', 'Failed to load the video.');
  };

  const handlePlayPress = async () => {
    if (video.current) {
      const status = await video.current.getStatusAsync();
      if (status.positionMillis === status.durationMillis) {
        await video.current.replayAsync();
      } else {
        await video.current.playAsync();
      }
    }
  };

  // Calculate video dimensions based on orientation and aspect ratio
  const isLandscape = orientation.includes('LANDSCAPE');
  const screenWidth = isLandscape ? windowHeight : windowWidth;
  const screenHeight = isLandscape ? windowWidth : windowHeight;
  const videoWidth = screenWidth;
  const videoHeight = screenWidth / aspectRatio;

  // Ensure video fits within screen height
  const adjustedHeight = Math.min(videoHeight, screenHeight);
  const adjustedWidth = adjustedHeight * aspectRatio;

  return (
    <View style={[styles.container, style]}>
      {!isVideoReady && (
        <ActivityIndicator
          size="large"
          color="#ffffff"
          style={styles.loadingIndicator}
        />
      )}
      {videoLink && (
        <View style={styles.videoContainer}>
          <Video
            ref={video}
            style={[
              styles.video,
              {
                width: adjustedWidth,
                height: adjustedHeight,
                opacity: isVideoReady ? 1 : 0,
              },
            ]}
            source={{ uri: videoLink }}
            resizeMode="contain"
            onReadyForDisplay={handleReadyForDisplay}
            onError={handleVideoError}
            onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
            useNativeControls
          />
          {/* {!isPlaying && isVideoReady && (
            <TouchableOpacity
              style={styles.playButtonContainer}
              onPress={handlePlayPress}
            >
              <Ionicons name="play-circle" size={70} color="white" />
            </TouchableOpacity>
          )} */}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
    alignItems: 'center',
    justifyContent: 'center',
  },
  videoContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
  },
  video: {
    backgroundColor: 'black',
  },
  loadingIndicator: {
    position: 'absolute',
    alignSelf: 'center',
  },
  playButtonContainer: {
    ...StyleSheet.absoluteFillObject, // covers the entire video
    justifyContent: 'center',
    alignItems: 'center',
  },

});