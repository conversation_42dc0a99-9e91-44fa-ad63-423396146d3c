/**
 * Theme-related constants and default values
 */

// Default theme mode
export const DEFAULT_THEME_MODE = 'system';

// Storage keys
export const THEME_STORAGE_KEY = 'app_theme_mode';

// Animation durations for theme transitions
export const THEME_TRANSITION_DURATION = 300;

// Common opacity values
export const OPACITY = {
  DISABLED: 0.5,
  OVERLAY: 0.2,
  OVERLAY_DARK: 0.6,
  PRESSED: 0.7,
  LOADING: 0.8,
} as const;

// Common z-index values
export const Z_INDEX = {
  MODAL: 1000,
  OVERLAY: 999,
  HEADER: 100,
  TAB_BAR: 50,
  FLOATING_BUTTON: 10,
} as const;

// Common border widths
export const BORDER_WIDTH = {
  THIN: 0.5,
  NORMAL: 1,
  THICK: 1.5,
  EXTRA_THICK: 2,
} as const;

// Common font weights (for consistency)
export const FONT_WEIGHT = {
  REGULAR: 'regular',
  MEDIUM: 'medium',
  BOLD: 'bold',
  SEMI_BOLD: 'semiBold',
  EXTRA_BOLD: 'extraBold',
  BLACK: 'black',
  ITALIC: 'italic',
} as const;

// Global Font Size System - Pre-computed responsive font sizes
// These are calculated once and cached for optimal performance
export const FONT_SIZES = {
  // Tiny text (captions, labels)
  XS: 10,
  // Small text (secondary info)
  SM: 11,
  // Body text (default)
  BASE: 12,
  // Medium text (subheadings)
  MD: 13,
  // Large text (headings)
  LG: 14,
  // Extra large text (titles)
  XL: 16,
  // 2X large text (main titles)
  XXL: 18,
  // 3X large text (hero titles)
  XXXL: 21,
  // 4X large text (display)
  XXXXL: 24,
} as const;

// Font size type for TypeScript
export type FontSizeKey = keyof typeof FONT_SIZES;

// Common component heights
export const COMPONENT_HEIGHT = {
  BUTTON: 50,
  INPUT: 50,
  TAB_BAR: 60,
  HEADER: 56,
  LIST_ITEM: 60,
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  SMALL: 320,
  MEDIUM: 768,
  LARGE: 1024,
  EXTRA_LARGE: 1200,
} as const;

// Common animation configurations
export const ANIMATIONS = {
  SPRING: {
    damping: 15,
    stiffness: 150,
  },
  TIMING: {
    duration: THEME_TRANSITION_DURATION,
  },
} as const;
