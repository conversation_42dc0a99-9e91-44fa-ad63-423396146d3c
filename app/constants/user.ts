import { t } from 'i18next';



const genders = [
  { label: 'Male', value:  'Male' },
  { label: 'Female', value: 'Female'},
];

const ageRange = [
  { label: '18-25', value: '18-25' },
  { label: '26-35', value: '26-35' },
  { label: '36-45', value: '36-45' },
  { label: '46-55', value: '46-55' },
  { label: '56-65', value: '56-65' },
  { label: 'Over 65', value: 'Over 65' },
];

export const userConstants = {
  genders,
  ageRange,
};

export const APIKeys = {
  apple: 'appl_nBfgwwFGGvuNbgswvHvyjfybiPn',
  google: 'goog_PfkrLJpmfxDzCkVComgEgatEDaI',
};