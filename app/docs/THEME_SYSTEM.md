# Theme System Documentation

## Overview

The theme system provides comprehensive dark mode support while maintaining the exact current appearance in light mode. It follows a component separation pattern for better maintainability.

## Architecture

### Core Files
- `app/types/theme.ts` - TypeScript interfaces and types
- `app/assets/themes/colors.ts` - Color definitions for light and dark modes
- `app/assets/themes/index.ts` - Theme configuration and exports
- `app/providers/ThemeProvider.tsx` - Theme context provider
- `app/hooks/useTheme.ts` - Theme access hooks
- `app/utils/createThemedStyles.ts` - Style creation utilities

### Theme Structure
```typescript
interface Theme {
  mode: 'light' | 'dark';
  colors: ThemeColors;
  spacing: ThemeSpacing;
  borderRadius: ThemeBorderRadius;
  shadows: ThemeShadows;
}
```

## Usage Patterns

### 1. Component Separation Pattern

**Container Component** (handles state, logic, theme):
```typescript
const MyScreenContainer: React.FC = () => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [data, setData] = useState([]);
  
  // Logic here...
  
  return (
    <MyScreenPresentation
      theme={theme}
      styles={styles}
      data={data}
      onAction={handleAction}
    />
  );
};
```

**Presentational Component** (pure UI rendering):
```typescript
interface MyScreenPresentationProps {
  theme: Theme;
  styles: any;
  data: any[];
  onAction: () => void;
}

const MyScreenPresentation: React.FC<MyScreenPresentationProps> = ({
  theme,
  styles,
  data,
  onAction,
}) => {
  return (
    <View style={styles.container}>
      {/* UI elements */}
    </View>
  );
};
```

### 2. Creating Themed Styles

```typescript
import { createThemedStyles } from 'app/utils/createThemedStyles';

const createStyles = createThemedStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
  },
  text: {
    color: theme.colors.text,
    fontSize: rv(16),
    fontFamily: 'medium',
  },
  button: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    ...theme.shadows.medium,
  },
}));
```

### 3. Using Theme Hooks

```typescript
import { useTheme, useThemeValues, useThemeActions } from 'app/hooks/useTheme';

// Full theme context
const { theme, themeMode, toggleTheme, setTheme } = useTheme();

// Only theme values
const theme = useThemeValues();

// Only theme actions
const { toggleTheme, setTheme } = useThemeActions();
```

### 4. Theme-Aware Status Bar

```typescript
import { ThemedStatusBar } from 'app/components/ThemedStatusBar';

// Automatic theme-based styling
<ThemedStatusBar />

// Override specific properties
<ThemedStatusBar style="dark" backgroundColor="#custom" />
```

## Color System

### Light Mode Colors (Current Appearance)
- Primary: `#163E23` (current green)
- Secondary: `#ECFFD4` (current light green)
- Background: `#FFFFFF` (white)
- Text: `#000000` (black)

### Dark Mode Colors
- Primary: `#4CAF50` (adjusted green)
- Secondary: `#2E7D32` (darker green)
- Background: `#121212` (dark gray)
- Text: `#FFFFFF` (white)

### Accessing Colors
```typescript
const { theme } = useTheme();

// Use theme colors instead of hardcoded values
backgroundColor: theme.colors.background,
color: theme.colors.text,
borderColor: theme.colors.border,
```

## Migration Guidelines

### Converting Existing Components

1. **Identify hardcoded colors**:
   ```typescript
   // Before
   backgroundColor: '#FFFFFF',
   color: '#000000',
   
   // After
   backgroundColor: theme.colors.background,
   color: theme.colors.text,
   ```

2. **Separate container and presentation**:
   ```typescript
   // Before - mixed logic and UI
   const MyScreen = () => {
     const [data, setData] = useState([]);
     return <View style={{backgroundColor: '#fff'}}>{/* UI */}</View>;
   };
   
   // After - separated
   const MyScreenContainer = () => {
     const { theme } = useTheme();
     const [data, setData] = useState([]);
     return <MyScreenPresentation theme={theme} data={data} />;
   };
   ```

3. **Use themed styles**:
   ```typescript
   // Before
   const styles = StyleSheet.create({
     container: { backgroundColor: '#FFFFFF' }
   });
   
   // After
   const createStyles = createThemedStyles((theme) => ({
     container: { backgroundColor: theme.colors.background }
   }));
   ```

## Best Practices

1. **Always use theme colors** instead of hardcoded hex values
2. **Separate container and presentational components** for better organization
3. **Use createThemedStyles utility** for consistent style creation
4. **Test both light and dark modes** during development
5. **Maintain exact light mode appearance** - no visual changes allowed
6. **Use TypeScript interfaces** for type safety

## Testing

### Visual Regression Testing
- Ensure light mode appears identical to current state
- Verify dark mode renders correctly
- Test theme switching functionality

### Component Testing
```typescript
// Test both themes
const themes = ['light', 'dark'];
themes.forEach(mode => {
  test(`renders correctly in ${mode} mode`, () => {
    // Test component with theme
  });
});
```

## Common Patterns

### Conditional Styling
```typescript
// Based on theme mode
const dynamicStyle = theme.mode === 'dark' 
  ? { opacity: 0.8 } 
  : { opacity: 1.0 };

// Based on theme colors
const borderStyle = {
  borderColor: theme.colors.border,
  borderWidth: theme.mode === 'dark' ? 1 : 0.5,
};
```

### Platform-Specific Theming
```typescript
import { getPlatformThemedValue } from 'app/utils/createThemedStyles';

const shadowStyle = getPlatformThemedValue(
  theme,
  theme.shadows.medium, // iOS
  { elevation: 4 },     // Android
);
```

## Troubleshooting

### Common Issues
1. **Theme not updating**: Ensure component is wrapped in ThemeProvider
2. **Colors not changing**: Check if using theme.colors instead of hardcoded values
3. **Performance issues**: Use createThemedStyles for memoized styles
4. **Type errors**: Import proper TypeScript interfaces from app/types/theme

### Debug Tools
```typescript
// Log current theme
console.log('Current theme:', theme);

// Check theme mode
console.log('Theme mode:', themeMode);
```
