import { Camera, CameraType } from 'expo-camera';
// import * as ImagePicker from 'react-native-image-picker';

import { showModal, hideModal } from 'app/providers/modals';

// import RNFetchBlob from 'rn-fetch-blob';
import {
  IMAGE_SIZE_LIMIT,
  VIDEO_DURATION_LIMIT,
  VIDEO_SIZE_LIMIT,
} from '../Data/constants';
import { FileHelper } from '.';
// import { Video as VideoCompressor } from 'react-native-compressor';
// import { ProcessingManager } from 'react-native-video-processing';
import { ShowAlert } from 'app/providers/toast';
import * as ImagePicker from 'expo-image-picker';

export const CameraHelper = {
  openCamera: async function () {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: 'Permissions not granted to access camera',
          setModalVisible: hideModal, // Function to close the modal
          type: 'alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });

        return null;
      }

      const options = {
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
      };

      const result = await ImagePicker.launchCameraAsync(options);

      if (result.canceled) {
        console.log('User cancelled camera');
        return null;
      }
      const data = {
        name: result.assets[0].uri.split('/').pop(),
        type: 'image/jpeg',
        uri: result.assets[0].uri,
      };
      return data;
    } catch (error) {
      console.log('Error:', error);
      return null;
    }
  },
  selectVideoFromGallery: async function(setProcessingFile) {
    setProcessingFile(true)
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: 'Permissions not granted to access media library',
          setModalVisible: hideModal, // Function to close the modal
          type: 'alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });

        return null;
      }
  
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsEditing: false,
        quality: 1,
      });

      if (result.canceled) {
        console.log('User cancelled video picker');
        setProcessingFile(false)
        return null;
      }
      if (result.assets[0].fileSize > VIDEO_SIZE_LIMIT){
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: 'Video not sent. Video exceeds duration of 1 min / 50 mb',
          setModalVisible: hideModal, // Function to close the modal
          type: 'alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });
        
        setProcessingFile(false)
         return null;
      }


        
      
      // const videoInfo = await ImagePicker.getFileInfoAsync(result.uri, {
      //   size: true,
      // });
      
      // if (videoInfo.size > VIDEO_SIZE_LIMIT) {
      //   alert('Video size exceeds the limit.');
      //   return null;
      // }
  
      // const videoDuration = await getDurationAsync(result.uri);
  
      if (result.assets[0].duration / 1000 > VIDEO_DURATION_LIMIT) {
        setProcessingFile(false)
        console.log(result.assets[0].duration)
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: 'Not sent, Video duration exceeds the limit of 1 min',
          setModalVisible: hideModal, // Function to close the modal
          type: 'alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });
     
        return null;
      }
      setProcessingFile(false)
      const data = {
        name: result.assets[0].uri.split('/').pop(),
        type: 'video/mp4', // Change this as needed based on the result's type
        uri: result.assets[0].uri,
      };
      return data;
    } catch (error) {
      console.log('Error:', error);
      setProcessingFile(false)
      return null;
      
    }
  },
  selectImageFromGallery: async function () {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: 'Permissions not granted to media library',
          setModalVisible: hideModal, // Function to close the modal
          type: 'alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });
        return null;
      }
  
      const options = {
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 1, // 100% quality
        maxFileSize: 1000 * 1024,
      };
  
      const result = await ImagePicker.launchImageLibraryAsync(options);
      console.log(result.assets[0].uri)
      if (result.canceled) {
        console.log('User cancelled image picker');
        return null;
      } else if (result.error) {
        console.log('ImagePicker Error: ', result.error);
        return null;
      } else {
        const data = {
          name: result.assets[0].uri.split('/').pop(),
          type: 'image/jpeg', // Change this as needed based on the result's type
          uri: result.assets[0].uri,
        };
        return data;
      }
    } catch (error) {
      console.log('Error:', error);
      return null;
    }
  }
}
