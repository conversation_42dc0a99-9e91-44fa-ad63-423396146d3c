import * as Device from 'expo-device';
import { Platform } from 'react-native';

import { logUserError, logCrashReport } from 'app/api/user';
import { prepareErrorPayload, prepareCrashPayload } from 'app/utils/errorUtils';

// Global error handler function
function globalErrorHandler(error, isFatal) {
  const payload = prepareErrorPayload(error);

  // Send error log to backend
  logUserError(payload).catch((err) => {
    console.error('Failed to send error log:', err);
  });

  if (isFatal) {
    // Prepare crash report payload
    const crashPayload = prepareCrashPayload(
      error,
      Platform.OS === 'ios' ? 'iOS' : 'Android',
      '1.0.0', // Replace with your app version
      Device.modelName, // Requires expo-device
      Platform.Version.toString()
    );

    // Send crash report to backend
    logCrashReport(crashPayload).catch((err) => {
      console.error('Failed to send crash report:', err);
    });

    // Optionally, you can display a custom error screen or restart the app
  }
}

// Set the global error handler
ErrorUtils.setGlobalHandler(globalErrorHandler);
