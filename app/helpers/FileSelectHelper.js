import * as DocumentPicker from 'expo-document-picker';
import * as MediaLibrary from 'expo-media-library';

// Function to request media library permissions
async function getPermission() {
  const { status } = await MediaLibrary.requestPermissionsAsync();

  if (status !== 'granted') {
    throw new Error('Permissions not granted to access media library');
  }

  return true;
}

export async function FileSelectHelper(fileType, isMultiple = false, withFullPath) {
  try {
    await getPermission();

    const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'm4a'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'];

    // Set the appropriate MIME type based on fileType
    let mimeType = '*/*'; // Default to all files

    if (fileType === 'audio') {
      mimeType = 'audio/*';
    } else if (fileType === 'video') {
      mimeType = 'video/*';
    }
    // Add more conditions if you support other file types

    // Since 'multiple' is not supported on mobile, we handle single selection
    const response = await DocumentPicker.getDocumentAsync({
      type: mimeType,
      copyToCacheDirectory: withFullPath ? false : true,
    });

    console.log(response, 'response');

    // Check if the user canceled the picker
    if (response.canceled) {
      return null;
    } else if (response.assets && response.assets.length > 0) {
      // Proceed to process the selected file(s)
      const files = response.assets.map((asset) => {
        const { uri, name, size, mimeType: assetMimeType } = asset;
        console.log(assetMimeType, 'mimeType');

        // Check file type based on extension
        const fileExtension = name.split('.').pop().toLowerCase();

        if (fileType === 'audio' && !audioExtensions.includes(fileExtension)) {
          throw new Error('Selected file is not an audio file');
        } else if (fileType === 'video' && !videoExtensions.includes(fileExtension)) {
          throw new Error('Selected file is not a video file');
        }

        return {
          name,
          path: uri,
          fileName: name,
          type: assetMimeType || '', // Use mimeType from asset
          uri,
          fileSize: size,
        };
      });

      console.log(files, 'selected');

      // If multiple files are not allowed, return the first file
      return isMultiple ? files : files[0];
    } else {
      throw new Error('No file selected');
    }
  } catch (error) {
    console.error('Error processing file:', error);
    throw error; // Re-throw the original error
  }
}
