import React, { useEffect } from 'react';
import { BackHandler } from 'react-native';
import { useNavigation } from '@react-navigation/native';

const withBackHandler = (WrappedComponent: React.ComponentType<any>) => {
  return (props: any) => {
    const navigation = useNavigation();

    useEffect(() => {
      const backAction = () => {
        // 1) Check if the parent navigator is a tab navigator
        const parentState = navigation.getParent()?.getState?.();
        const parentType = parentState?.type;  // e.g. 'tab', 'stack', etc.

        // If NOT in bottom tab navigator, allow normal back behavior
        if (parentType !== 'tab') {
          return false; // let default back behavior happen
        }

        // 2) We ARE in a tab navigator, so run your custom logic
        const state = navigation.getState();
        const currentRoute = state.routes[state.index].name;

        if (currentRoute !== 'TopicScreen') {
          console.log('I ran in the bottom tab navigator!');
          navigation.navigate('Home', {
            screen: 'Quiz',
            params: {
              screen: 'GameMode',
              params: {
                screen: 'TopicScreen',
              },
            },
          });
          return true; // prevent default back behavior
        }

        // If we are on 'TopicScreen', let normal back happen
        return false;
      };

      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        backAction
      );

      return () => backHandler.remove();
    }, [navigation]);

    return <WrappedComponent {...props} />;
  };
};

export default withBackHandler;
