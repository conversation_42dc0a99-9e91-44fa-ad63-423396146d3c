import React from 'react';
import { useEffect } from 'react';
import * as Notifications from 'expo-notifications';
import * as navigation from 'app/navigation/root';
import { registerDeviceToken } from './queries';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { setDeviceToken } from 'app/redux/main/reducer';

export default function useNotification() {
  const dispatch = useDispatch();
  const savedToken = useSelector(state => state.main.deviceToken);
  const userToken = useSelector(state => state.user.token);


  useEffect(() => {
    const registerForPushNotificationsAsync = async () => {
      try {
        if (Platform.OS === 'android') {
          await Notifications.setNotificationChannelAsync('default', {
            name: 'default',
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#FF231F7C',
          });
        }

        if (!Device.isDevice) {
          return;
        }

        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }

        if (finalStatus !== 'granted') {
          console.log('❌ Push notification permission denied:', finalStatus);
          return;
        }

        console.log('✅ Push notification permissions granted');

        // Get the project ID from Constants
        const projectId = Constants.expoConfig?.extra?.eas?.projectId;
        
        if (!projectId) {
          console.error('❌ No project ID found in Constants.expoConfig');
          return;
        }

        console.log('🎫 Fetching Expo push token with project ID:', projectId);
        
        // Get the token using the project ID from Constants
        const expoPushToken = await Notifications.getExpoPushTokenAsync({
          projectId: projectId
        });

        const newToken = expoPushToken.data;
        console.log('📬 New Expo push token:', newToken);
        console.log('💾 Saved token in Redux:', savedToken);
        console.log('🔑 User auth token:', userToken ? 'exists' : 'none');

        // If token has changed, update it in Redux
        if (newToken !== savedToken) {
          console.log('🔄 Token changed, updating in Redux');
          dispatch(setDeviceToken(newToken));
          
          // If user is logged in, register the new token with backend
          if (userToken) {
            try {
              console.log('📡 Registering token with backend...');
              await registerDeviceToken(newToken);
              console.log('✅ Token successfully registered with backend');
            } catch (error) {
              console.error('❌ Failed to register token with backend:', error);
            }
          } else {
            console.log('👤 User not logged in, skipping backend registration');
          }
        } else {
          console.log('🟰 Token unchanged, no update needed');
        }
      } catch (error) {
        console.error('❌ Error during push notification setup:', error);
      }
    };

    // Register for push notifications
    registerForPushNotificationsAsync();

    // Set up notification handlers
    const notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('📨 Received notification:', notification);
    });

    // Complete notification validation matching backend structure
    const validateNotificationData = (data) => {
      if (!data || !data.type) {
        console.warn('⚠️ Notification missing type field');
        return false;
      }

      // Updated validation to match backend structure
      const requiredFields = {
        chat: ['userId', 'userDetails'], // userDetails contains user info for chat
        topic: ['topicId'], // postid is legacy, topicId is primary
        group: ['groupId', 'groupDetails'], // groupDetails contains group info
        quiz: [], // Quiz can work without specific fields
        follow: ['userId', 'userDetails'] // userDetails contains follower info
      };

      const required = requiredFields[data.type];
      if (required && required.length > 0) {
        const hasRequiredField = required.some(field => data[field]);
        if (!hasRequiredField) {
          console.warn(`⚠️ ${data.type} notification missing required fields:`, required);
          return false;
        }
      }

      return true;
    };

    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;

      console.log('📱 Notification clicked:', {
        type: data?.type,
        deepLink: data?.deepLink,
        timestamp: data?.timestamp,
        hasData: !!data
      });

      // Validate notification data
      if (!validateNotificationData(data)) {
        console.error('❌ Invalid notification data, navigating to home');
        navigation.navigate('Home');
        return;
      }

      try {
        // Handle notification navigation based on exact backend structure
        console.log(`📱 Handling ${data.type} notification:`, {
          type: data.type,
          deepLink: data.deepLink,
          timestamp: data.timestamp
        });

        switch (data.type) {
          case 'chat':
            // Navigate to chat screen with user details from backend
            console.log('Navigate to chat:', {
              userId: data.userId,
              userName: data.userName,
              userAvatar: data.userAvatar
            });

            navigation.navigate('Common', {
              screen: 'private-chat',
              params: {
                userDetails: data.userDetails // Backend provides complete userDetails object
              },
            });
            break;

          case 'topic':
            // Navigate to topic/post screen
            console.log('Navigate to topic:', {
              topicId: data.topicId,
              topicTitle: data.topicTitle,
              topicAuthor: data.topicAuthor
            });

            navigation.navigate('Common', {
              screen: 'view-topic',
              params: {
                postid: data.topicId // Use topicId as postid for the screen
              },
            });
            break;

          case 'group':
            // Navigate to group screen
            console.log('Navigate to group:', {
              groupId: data.groupId,
              groupName: data.groupName,
              groupAvatar: data.groupAvatar
            });

            navigation.navigate('Common', {
              screen: 'group-chat',
              params: {
                groupDetails: data.groupDetails // Backend provides complete groupDetails object
              },
            });
            break;

          case 'quiz':
            // Navigate to quiz screen with question data
            console.log('Navigate to quiz:', {
              questionId: data.questionId,
              questionText: data.questionText
            });

            navigation.navigate('Home', {
              screen: 'Quiz',
              params: {
                screen: 'GameMode',
                params: {
                  screen: 'QuizScreen',
                  params: {
                    questionData: data.questionData, // Complete question object from backend
                    questionId: data.questionId
                  },
                },
              },
            });
            break;

          case 'follow':
            // Navigate to profile of the person who followed the user
            console.log('Navigate to follower profile:', {
              userId: data.userId,
              userName: data.userDetails?.first_name,
              userAvatar: data.userDetails?.profile_picture
            });

            navigation.navigate('Common', {
              screen: 'view-profile',
              params: {
                userId: data.userId,
                userDetails: data.userDetails // Backend provides complete userDetails object
              },
            });
            break;

          default:
            console.warn(`⚠️ Unknown notification type: ${data.type}`);
            // Fallback to home screen
            navigation.navigate('Home');
        }
      } catch (error) {
        console.error('❌ Error handling notification navigation:', error);
        console.error('Notification data:', data);
        // Fallback navigation
        navigation.navigate('Home');
      }
    });

    return () => {
      console.log('🧹 Cleaning up notification listeners');
      Notifications.removeNotificationSubscription(notificationListener);
      Notifications.removeNotificationSubscription(responseListener);
    };
  }, [savedToken, userToken, dispatch]); // Add dependencies to re-run when these change
}
