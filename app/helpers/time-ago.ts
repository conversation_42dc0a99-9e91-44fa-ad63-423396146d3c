import moment from 'moment';

export function timeAgo(time: string) {
  const now = moment();
  const then = moment(time);
  const diffInSeconds = now.diff(then, 'seconds');
  const diffInMinutes = now.diff(then, 'minutes');
  const diffInHours = now.diff(then, 'hours');
  const diffInDays = now.diff(then, 'days');
  const diffInWeeks = now.diff(then, 'weeks');
  const diffInYears = now.diff(then, 'years');

  if (diffInSeconds < 60) {
    return `${diffInSeconds}s`;
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h`;
  } else if (diffInDays < 7) {
    return `${diffInDays}d`;
  } else if (diffInWeeks < 52) {
    return `${diffInWeeks}w`;
  } else {
    return `${diffInYears}y`;
  }
}
