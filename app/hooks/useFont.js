import * as Font from 'expo-font';

export default useFonts = async () => {
  await Font.loadAsync({
    regular: require('app/assets/fonts/Poppins-Regular.ttf'),
    medium: require('app/assets/fonts/Poppins-Medium.ttf'),
    bold: require('app/assets/fonts/Poppins-Bold.ttf'),
    italic: require('app/assets/fonts/Poppins-Italic.ttf'),
    extraBold: require('app/assets/fonts/Poppins-ExtraBold.ttf'),
    black: require('app/assets/fonts/Poppins-Black.ttf'),
    semiBold: require('app/assets/fonts/Poppins-SemiBold.ttf'),
    openSans: require('app/assets/fonts/OpenSans-Regular.ttf'),
    openSansItalic: require('app/assets/fonts/OpenSans-Italic.ttf'),
    openSansBold: require('app/assets/fonts/OpenSans-Bold.ttf'),
    cormorantRegular: require('app/assets/fonts/Cormorant-Regular.ttf'),
    cormorantMedium: require('app/assets/fonts/Cormorant-Medium.ttf'),
    cormorantBold: require('app/assets/fonts/Cormorant-Bold.ttf'),
    cormorantBoldItalic: require('app/assets/fonts/Cormorant-BoldItalic.ttf'),

    cormorantItalic: require('app/assets/fonts/Cormorant-Italic.ttf'),
    cormorantLightItalic: require('app/assets/fonts/Cormorant-LightItalic.ttf'),
    cormorantLight: require('app/assets/fonts/Cormorant-Light.ttf'),

    cormorantMediumItalic: require('app/assets/fonts/Cormorant-MediumItalic.ttf'),
    cormorantSemiBold: require('app/assets/fonts/Cormorant-SemiBold.ttf'),
    cormorantSemiBoldItalic: require('app/assets/fonts/Cormorant-SemiBoldItalic.ttf'),
    hammersmithRegular: require('app/assets/fonts/HammersmithOne-Regular.ttf'),

    // All other fonts here
  });
};
