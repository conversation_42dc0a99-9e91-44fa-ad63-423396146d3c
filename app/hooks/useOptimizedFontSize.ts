import { useContext } from 'react';
import { OptimizedFontSizeContext } from 'app/providers/OptimizedFontSizeProvider';
import type { OptimizedFontSizeContextType } from 'app/providers/OptimizedFontSizeProvider';

/**
 * Hook to access optimized font size context
 * Provides high-performance font size scaling functionality
 */
export const useOptimizedFontSize = (): OptimizedFontSizeContextType => {
  const context = useContext(OptimizedFontSizeContext);
  
  if (context === undefined) {
    throw new Error('useOptimizedFontSize must be used within an OptimizedFontSizeProvider');
  }
  
  return context;
};

/**
 * Hook to get just the optimized font size function
 * Useful when you only need to get font sizes
 */
export const useFs = () => {
  const { fs } = useOptimizedFontSize();
  return fs;
};

/**
 * Hook to get the font size scale setter
 * Useful for settings screens
 */
export const useFontSizeScale = () => {
  const { fontSizeScale, setFontSizeScale } = useOptimizedFontSize();
  return { fontSizeScale, setFontSizeScale };
};

/**
 * Hook to get multiple font sizes at once
 * Useful for creating style objects with multiple font sizes
 */
export const useMultipleFontSizes = () => {
  const { getFontSizes } = useOptimizedFontSize();
  return getFontSizes;
};
