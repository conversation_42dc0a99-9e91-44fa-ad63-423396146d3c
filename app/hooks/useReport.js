import { group,topic,user } from 'app/api';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ShowAlert } from 'app/providers/toast';
import { showModal,hideModal } from 'app/providers/modals';
import { useNavigation } from '@react-navigation/native';





export function useReport(reportType) {
  const navigation = useNavigation()
  let reportFunction;
  console.log("I won ",reportType)

  switch (reportType) {
    case 'user':
      reportFunction = user.reportUser;
      break;
    case 'comment':
      reportFunction = topic.reportSubComments;
      break;
    case 'group':
      reportFunction = group.reportGroup;
      break;
    case 'topic':
      reportFunction = topic.reportTopic;
      break;
    case undefined:
      reportFunction = topic.reportTopic;
      break
    default:
      throw new Error("Unknown parameter")
  }

  
  const { mutateAsync: report, isLoading } = useMutation(reportFunction, 
    {
      onSuccess: (response, variables, context) => {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: response.message,
          setModalVisible: hideModal, // Function to close the modal
          type: 'alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
          navigation
        });
      },
    }
  );
  // const handleReport = async (Id, description) => {
  //   await report({Id,description});
  // };

  return { report, isLoading };
}