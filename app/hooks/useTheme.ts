import { useContext } from 'react';
import { ThemeContext } from 'app/providers/ThemeProvider';
import { ThemeContextType } from 'app/types/theme';

/**
 * Custom hook to access theme context
 * Provides theme values, current mode, and theme switching functions
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

/**
 * Hook to get only the theme object (colors, spacing, etc.)
 * Useful when you only need theme values without switching functionality
 */
export const useThemeValues = () => {
  const { theme } = useTheme();
  return theme;
};

/**
 * Hook to get only the current theme mode
 */
export const useThemeMode = () => {
  const { themeMode } = useTheme();
  return themeMode;
};

/**
 * Hook to get theme switching functions
 */
export const useThemeActions = () => {
  const { toggleTheme, setTheme } = useTheme();
  return { toggleTheme, setTheme };
};

export default useTheme;
