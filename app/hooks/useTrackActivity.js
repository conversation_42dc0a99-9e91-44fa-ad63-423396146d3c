// useTrackActivity.js
import { useActivity } from 'app/providers/ActivityContext';

const useTrackActivity = (type) => {
  const { incrementActivity, modalVisible, resetModalVisibility,activityCounts } = useActivity();

  // Function to track activity
  const trackActivity = () => {
    incrementActivity(type);
  };

  // Function to handle modal close
  const handleModalClose = () => {
    resetModalVisibility(type);
  };

  return { trackActivity, modalVisible, handleModalClose, activityCounts };
};

export default useTrackActivity;
