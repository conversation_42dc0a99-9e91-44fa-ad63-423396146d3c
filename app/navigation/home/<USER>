import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Welcome from 'app/screens/onboarding/welcome';
import Company from 'app/screens/onboarding/company';
import AreaOfInterests from 'app/screens/onboarding/area-of-interests';
import Personal from 'app/screens/onboarding/personal';
import Login from 'app/screens/guests/login';
import ForgotPasswordScreen from 'app/screens/guests/forgot-password';
import Register from 'app/screens/guests/register';
import StatusAndGraduation from 'app/screens/onboarding/StatusAndGraduation';
import VerifyEmail from 'app/screens/guests/verify-email';
import { useSelector } from 'react-redux';
import { userAuthInfo } from 'app/redux/user/reducer';

const OnboardingStack = createNativeStackNavigator();

function OnboardingNavigator() {
  const user = useSelector(userAuthInfo);

  // Determine initial route based on user verification status
  const getInitialRoute = () => {
    if (!user) return "verify-email";

    // If user is not verified (check both verified and email_verified fields), start with email verification
    if (!user.verified && !user.email_verified) {
      return "verify-email";
    }

    // If verified, start with welcome screen
    return "welcome";
  };

  return (
    <OnboardingStack.Navigator
      screenOptions={{ headerShown: false }}
      initialRouteName={getInitialRoute()}
    >
      <OnboardingStack.Screen name='verify-email' component={VerifyEmail} />
      <OnboardingStack.Screen name='welcome' component={Welcome} />
      <OnboardingStack.Screen name='personal' component={Personal} />
      <OnboardingStack.Screen name='company' component={Company} />
      <OnboardingStack.Screen name='area-of-interest' component={AreaOfInterests} />
      <OnboardingStack.Screen name='login' component={Login} />
      <OnboardingStack.Screen name='forgot-password' component={ForgotPasswordScreen} />
      <OnboardingStack.Screen name='register' component={Register} />
      <OnboardingStack.Screen name='status' component={StatusAndGraduation} />
    </OnboardingStack.Navigator>
  );
}

export default OnboardingNavigator;