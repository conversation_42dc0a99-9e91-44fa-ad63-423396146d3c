import React,{useState,useEffect} from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, TouchableOpacity, SafeAreaView } from 'react-native';
import { CustomText } from 'app/components/elements';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Image, Text } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { Theme } from 'app/types/theme';
import Topic from 'app/screens/bottom/topic';
import Search from 'app/screens/bottom/search';
import Chat from 'app/screens/bottom/chats';
import Group from 'app/screens/bottom/groups';
import Quiz from 'app/screens/bottom/quiz';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import SVG from 'app/providers/svg';
import ConvoNotification from 'app/screens/bottom/convo-notification';
import Convo from 'app/screens/bottom/convo';
import ChatSVG from 'app/assets/svg/chat.svg';
import { useTranslation } from 'react-i18next';
import ChallengeMode from 'app/screens/bottom/challengeMode';
import GameMode from 'app/screens/bottom/gameMode';
import WinChallengeMode from 'app/screens/bottom/winChallengeMode';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import CreditScreen from 'app/screens/drawer/credit';
import HelpDesk from 'app/screens/drawer/helpdesk';
import HelpdeskStackNavigator from './drawer/helpdesk';
import UltimateChallenge from 'app/screens/bottom/ultimateChallenge';
import UltimateGameMode from 'app/screens/bottom/ultimateGame';
import CustomBottomTab from 'app/screens/bottom/customButtonTab';
import UltimateCongrat from 'app/screens/bottom/UltimateCongrat';
import CashOutForm from 'app/screens/common/cash-out-form';
import SpinWheel from 'app/screens/bottom/SpinWheel';
import IntroSpinWheel from 'app/screens/bottom/SpinWheelIntro';
import SettingsStackNavigator from  './drawer/settings'
import { useRoute } from '@react-navigation/native';
import Logo_inapp from '@/app/assets/svg/connectifyHorizontallogo.svg'
import SidebarNav from '@/app/assets/svg/lucosa-sidemenu-icon.svg'
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
// import { SafeAreaView } from 'react-native-safe-area-context';

const BottomTab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();


const HomeNavigationDrawerStructure = (props: any) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const toggleDrawer = () => {
    props.navigationProps.toggleDrawer();
  };

  return (
    <SafeAreaView style={styles.menuButtonContainer}>
      <TouchableOpacity
        onPress={() => toggleDrawer()}
        style={styles.menuButton}
        activeOpacity={0.7}
      >
        <SidebarNav />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

<Stack.Navigator initialRouteName='QuizScreen'>
  <Stack.Screen
    name='QuizScreen'
    component={Quiz}
    options={{ headerShown: false }}
  />
  <Stack.Screen
    name='UltimateGameMode'
    component={UltimateGameMode}
    options={{ headerShown: false }}
  />
  <Stack.Screen
    name='BottomTapNavigator'
    component={BottomTabNavigator}
    options={{ headerShown: false }}
  />
</Stack.Navigator>;

function BottomTabNavigator() {
  const { t } = useTranslation();
 const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  function MyTabBar({ state, descriptors, navigation }) {
    return (
      <View style={styles.tabBarContainer}>
        <SafeAreaView style={styles.tabBarSafeArea}>
          {state.routes.map((route :any, index:any) => {
            const { options } = descriptors[route.key];
            const label =
              options.tabBarLabel !== undefined
                ? options.tabBarLabel
                : options.title !== undefined
                ? options.title
                : route.name;

            const isFocused = state.index === index;

            const TabIcon = isFocused ? options.tabBarIconFocused : options.tabBarIconUnfocused;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                // The `merge: true` option makes sure that the params inside the tab screen are preserved
                navigation.navigate({ name: route.name, merge: true });
              }
            };

            const onLongPress = () => {
              navigation.emit({
                type: 'tabLongPress',
                target: route.key,
              });
            };

            const borderStyle =
              index !== state.routes.length - 1
                ? { borderRightWidth: 1, borderRightColor: theme.colors.border }
                : {};

            return (
              <TouchableOpacity
                accessibilityRole='button'
                accessibilityState={isFocused ? { selected: true } : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={onPress}
                onLongPress={onLongPress}
                style={[styles.tabItem, borderStyle]}
              >
                {isFocused ? (
                  <View style={styles.focusedTabContent}>
                    <View style={styles.focusedTabInner}>
                      <TabIcon />
                      <CustomText style={styles.focusedTabLabel}>
                        {label}
                      </CustomText>
                    </View>
                  </View>
                ) : (
                  <SafeAreaView style={styles.unfocusedTabContent}>
                    <TabIcon />
                  </SafeAreaView>
                )}
              </TouchableOpacity>
            );
          })}
        </SafeAreaView>
      </View>
    );
  }

  return (
    <BottomTab.Navigator
      initialRouteName='TopicScreen'
      tabBar={(props) => <MyTabBar {...props} />}
      screenOptions={{
        tabBarStyle: { backgroundColor: 'transparent',  }, // Set the background color here
        headerShown: false,
    
      }}
    >
       <BottomTab.Screen
        name={'TopicScreen'}
      
        options={{
          tabBarLabel: "Posts",
          tabBarIconFocused: () => <SVG name='homeDark' size={25} />,
          tabBarIconUnfocused: () => <SVG name='homeLight' size={25} />,
          title: 'Topics',
          headerShown: false,
        
        }}
        component={Topic}
      />
      <BottomTab.Screen
        name={'QuizScreen'}
        options={{
          tabBarLabel: 'Trivia',
          tabBarIconFocused: () => <SVG name='triviaDark' size={25} />,
          tabBarIconUnfocused: () => <SVG name='triviaLight' size={25} />,
        }}
        component={Quiz}
      />
     
      <BottomTab.Screen
        name={'GroupsScreen'}
        options={{
          tabBarLabel: t("BottomNav_groups"),
          tabBarIconFocused: () => <SVG name='groupDark' size={25} />,
          tabBarIconUnfocused: () => <SVG name='groupLight' size={25} />,
          title: 'Groups',
          headerShown: false
        }}
        component={Group}
      />

      <BottomTab.Screen
        name={'ChatsScreen'}
        options={{
          tabBarLabel: t("BottomNav_chats"),
          tabBarIconFocused: () => <SVG name="commentDark" size={25} />,
          tabBarIconUnfocused: () => <SVG name='commentLight' size={25} />,
          title: 'Chats',
          headerShown: false
        }}
        component={Chat}
      />

      <BottomTab.Screen
        name={'SearchScreen'}
        options={{
          tabBarLabel: t("BottomNav_search"),
          tabBarIconFocused: () => <SVG name='searchDark' size={25} />,
          tabBarIconUnfocused: () => <SVG name='searchLight' size={25} />,
          title: 'Search',
          headerShown: false
        }}
        component={Search}
      />
    </BottomTab.Navigator>
  );
}

export function BottomTabStack({ navigation }: any) {
 const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  return (
    <Stack.Navigator>
      <Stack.Screen
        options={{
          headerTitle: () => (
            <Logo_inapp style={{alignSelf: 'center'}} />
          ),
          headerShown: true,
          headerTitleAlign:"center",
          headerLeft: () => (
            <HomeNavigationDrawerStructure navigationProps={navigation} />
          ),
          headerRight: () => (
            <SafeAreaView style={styles.headerRightContainer}>
              <ConvoNotification navigation={navigation} />
            </SafeAreaView>
          ),
          headerStyle: { backgroundColor: theme.colors.headerBackground },
          headerShadowVisible: false,
        }}
        name='GameMode'
        component={BottomTabNavigator}
      />
      <Stack.Screen
        options={{

          headerShown: false,
        }}
        name='convos'

        component={Convo}
      />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name='ChallengeMode'
        component={ChallengeMode}
      />

      {/* <Stack.Screen
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
        name='GameMode'
        component={GameMode}
      /> */}
      <Stack.Screen
        name='Helpdesk'
        options={{
          title: 'Helpdesk',
          headerShown: false,
        }}
        component={HelpdeskStackNavigator}
      />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name='UltimateChallenge'
        component={UltimateChallenge}
      />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name='SettingsStackNavigator'
        component={SettingsStackNavigator}
      />

      <Stack.Screen
        name='UltimateCongrats'
        component={UltimateCongrat}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name='UltimateGameMode'
        component={UltimateGameMode}
      />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name='WinScreen'
        component={WinChallengeMode}
      />
      <Stack.Screen
        name='Credit'
        component={CreditScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name='cash-out-form'
        component={CashOutForm}
      />
      <Stack.Screen
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
        name='SpinWheel'
        component={SpinWheel}
      />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name='IntroSpinWheel'
        component={IntroSpinWheel}
      />
    
    </Stack.Navigator>
  );
}

// Styled components for tab bar
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  tabBarContainer: {
    backgroundColor: theme.colors.tabBarBackground,
    paddingTop: rv(5),
  },
  tabBarSafeArea: {
    flexDirection: 'row',
    paddingBottom: rv(0),
  },
  tabItem: {
    flex: 1,
    backgroundColor: theme.colors.tabBarBackground,
    paddingVertical: rv(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  focusedTabContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  focusedTabInner: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
  },
  focusedTabLabel: {
    color: theme.colors.tabBarActive,
    paddingTop: rv(5),
    fontSize: fs("SM"),
    fontFamily: 'bold',
  },
  unfocusedTabContent: {
    padding: rv(1),
  },
    headerContainer: {
    backgroundColor: theme.colors.headerBackground,
  },
  menuButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: rv(10),
  },
  menuButton: {
    padding: rv(8),
    borderRadius: rv(20),
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: rv(40),
    minHeight: rv(40),
  },
  headerRightContainer: {
    paddingRight: rv(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
}));

// Styled components for header
// const createHeaderStyles = createThemedStyles((theme: Theme) => ({
//   headerContainer: {
//     backgroundColor: theme.colors.headerBackground,
//   },
//   menuButtonContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingLeft: rv(10),
//   },
//   menuButton: {
//     padding: rv(8),
//     borderRadius: rv(20),
//     backgroundColor: 'transparent',
//     alignItems: 'center',
//     justifyContent: 'center',
//     minWidth: rv(40),
//     minHeight: rv(40),
//   },
//   headerRightContainer: {
//     paddingRight: rv(10),
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
// }));
