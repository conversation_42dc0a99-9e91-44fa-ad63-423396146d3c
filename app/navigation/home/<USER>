import React, { useMemo } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import DrawerNavigator from './drawer';
import CommonStackNavigator from './common';
import { useSelector } from 'react-redux';
import { userAuthInfo } from 'app/redux/user/reducer';
import OnboardingNavigator from './onboarding';
import useNotification from 'app/helpers/notification';


// Home stack includes authenticated stacks for logged users
const HomeStack = createNativeStackNavigator();

function HomeNavigator() {
  const user: AccountProfileProp = useSelector(userAuthInfo);
  useNotification()


  const initialRoute = useMemo(() => {
    // Check if user needs email verification first (check both verified and email_verified fields)
    if (user && (!user.verified && !user.email_verified)) {
      return 'Onboarding' // Will start at verify-email screen
    }

    // Check if user needs to complete onboarding (no interests)
    if (user && user.interests && user.interests?.length === 0) {
      return 'Onboarding'
    }

    return 'Home'
  }, []);

  if (!initialRoute) {
    return null
  }

  return (

    <HomeStack.Navigator
      initialRouteName={
        initialRoute
      }
    >

      <HomeStack.Screen
        name='Home'
        component={DrawerNavigator}
        options={{ headerShown: false }}
      />


      <HomeStack.Screen
        name='Onboarding'
        component={OnboardingNavigator}
        options={{ headerShown: false }}
      />

      <HomeStack.Screen
        name='Common'
        component={CommonStackNavigator}
        options={{ headerShown: false }}
      />
    </HomeStack.Navigator>

  );
}

export default HomeNavigator;
