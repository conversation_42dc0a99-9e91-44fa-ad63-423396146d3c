import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Welcome from 'app/screens/onboarding/welcome';
import Company from 'app/screens/onboarding/company';
import AreaOfInterests from 'app/screens/onboarding/area-of-interests';
import Personal from 'app/screens/onboarding/personal';
import Login from 'app/screens/guests/login';
import ForgotPasswordScreen from 'app/screens/guests/forgot-password';
import Register from 'app/screens/guests/register';
import StatusAndGraduation from 'app/screens/onboarding/StatusAndGraduation';
import VerifyEmail from 'app/screens/guests/verify-email';

const OnboardingStack = createNativeStackNavigator();

function OnboardingNavigator() {
  return (
    <OnboardingStack.Navigator screenOptions={{ headerShown: false }}>
      <OnboardingStack.Screen name='verify-email' component={VerifyEmail} />
      <OnboardingStack.Screen name='welcome' component={Welcome} />
      <OnboardingStack.Screen name='personal' component={Personal} />
      <OnboardingStack.Screen name='company' component={Company} />
      <OnboardingStack.Screen name='area-of-interest' component={AreaOfInterests} />
      <OnboardingStack.Screen name='login' component={Login} />
      <OnboardingStack.Screen name='forgot-password' component={ForgotPasswordScreen} />
      <OnboardingStack.Screen name='register' component={Register} />
      <OnboardingStack.Screen name='status' component={StatusAndGraduation} />
      

      
    </OnboardingStack.Navigator>
  );
}

export default OnboardingNavigator;