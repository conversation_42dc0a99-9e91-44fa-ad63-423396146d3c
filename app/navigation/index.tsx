import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useSelector } from 'react-redux';
import { Linking, Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import AuthNavigator from './guest';
import HomeNavigator from './home';
import { navigationRef, isReadyRef } from 'app/navigation/root';
import { IsLoggedIn } from 'app/redux/user/reducer';
import Purchases from 'react-native-purchases';
import { APIKeys } from '../constants/user';

// Global notification queue for cold start handling
let pendingNotificationData: any = null;


const Stack = createNativeStackNavigator();

export function AppStack() {
  const userId = useSelector((state:any) => state.user.userId);
  const isLoggedIn = useSelector(IsLoggedIn);

    useEffect(() => {
      if (userId) {
        // Configure RevenueCat with your user ID
        Purchases.configure({
          apiKey: Platform.OS === 'ios' ? APIKeys.apple : APIKeys.google,
          appUserID: userId,
        });
      }
    }, [userId]);

  // Add URL handling for app state changes
  useEffect(() => {
    const handleURL = (event: { url: string }) => {
      console.log('🔗 App received URL from background:', event.url);
      // The NavigationContainer will automatically handle the URL
    };

    // Listen for URL events when app comes from background
    const subscription = Linking.addEventListener('url', handleURL);

    return () => {
      subscription?.remove();
    };
  }, []);

  // Handle cold start notifications manually
  const handleColdStartNotification = (notificationData: any) => {
    console.log('🚀 Handling cold start notification:', notificationData);

    if (!navigationRef.current || !isLoggedIn) {
      console.warn('⚠️ Navigation not ready or user not logged in');
      return;
    }

    try {
      const { type } = notificationData;

      switch (type) {
        case 'chat':
          console.log('Navigate to chat (cold start):', {
            userId: notificationData.userId,
            userName: notificationData.userName
          });

          (navigationRef.current as any).navigate('Home', {
            screen: 'Common',
            params: {
              screen: 'private-chat',
              params: {
                userDetails: notificationData.userDetails
              }
            }
          });
          break;

        case 'topic':
          console.log('Navigate to topic (cold start):', {
            topicId: notificationData.topicId,
            topicTitle: notificationData.topicTitle
          });

          (navigationRef.current as any).navigate('Home', {
            screen: 'Common',
            params: {
              screen: 'view-topic',
              params: {
                postid: notificationData.topicId
              }
            }
          });
          break;

        case 'group':
          console.log('Navigate to group (cold start):', {
            groupId: notificationData.groupId,
            groupName: notificationData.groupName
          });

          (navigationRef.current as any).navigate('Home', {
            screen: 'Common',
            params: {
              screen: 'group-chat',
              params: {
                groupDetails: notificationData.groupDetails
              }
            }
          });
          break;

        case 'quiz':
          console.log('Navigate to quiz (cold start):', {
            questionId: notificationData.questionId,
            questionText: notificationData.questionText
          });

          (navigationRef.current as any).navigate('Home', {
            screen: 'Quiz',
            params: {
              screen: 'GameMode',
              params: {
                screen: 'QuizScreen',
                params: {
                  questionData: notificationData.questionData,
                  questionId: notificationData.questionId
                }
              }
            }
          });
          break;

        default:
          console.warn(`⚠️ Unknown notification type for cold start: ${type}`);
          (navigationRef.current as any).navigate('Home');
      }
    } catch (error) {
      console.error('❌ Error handling cold start notification:', error);
      (navigationRef.current as any).navigate('Home');
    }
  };
  return (
   <NavigationContainer
      ref={navigationRef as any}
      onReady={() => {
        (isReadyRef as any).current = true;
        console.log('🚀 Navigation is ready');

        // Handle any pending cold start notifications
        if (pendingNotificationData && isLoggedIn) {
          console.log('📦 Processing pending notification after navigation ready');
          const dataToProcess = pendingNotificationData;
          pendingNotificationData = null;

          // Small delay to ensure everything is fully initialized
          setTimeout(() => {
            handleColdStartNotification(dataToProcess);
          }, 500);
        }
      }}
      linking={{
        prefixes: ['connectify://', 'https://pennytot.app', 'https://pennytots-admin.netlify.app'],
        config: {
          screens: {
            Home: 'home',
            Auth: 'auth',
            // Add specific deep link patterns
            'chat/:userId': 'chat/:userId',
            'topic/:topicId': 'topic/:topicId',
            'group/:groupId': 'group/:groupId',
            'quiz/:questionId?': 'quiz/:questionId?',
            // Legacy support
            About: 'about',
            HelpDeskScreenStack: 'helpdesk',
            QuizScreen: 'quiz',
          },
        },
        async getInitialURL() {
          // First, check if app was opened from a deep link
          const url = await Linking.getInitialURL();
          console.log('🔗 Initial URL from deep link:', url);

          if (url != null) {
            return url;
          }

          // Handle URL from expo push notifications (COLD START)
          const response = await Notifications.getLastNotificationResponseAsync();
          console.log('📱 Cold start notification response:', response);

          if (response) {
            const data = response.notification.request.content.data;
            console.log('📱 Cold start notification data:', data);

            // Store notification data for delayed navigation
            // We'll handle this after navigation is ready
            if (data && data.type) {
              // Store in module-level variable for cold start handling
              pendingNotificationData = data;
              console.log('📦 Stored pending notification data for cold start');
            }

            // Use the new deepLink field from backend (primary)
            if (data.deepLink) {
              console.log('🚀 Using backend deep link for cold start:', data.deepLink);
              return data.deepLink;
            }

            // Fallback to legacy URL field
            if (data.url) {
              console.log('🔄 Using legacy notification URL for cold start:', data.url);
              return data.url;
            }

            // Manual deep link construction if deepLink is missing
            if (data.type && data.userId) {
              const constructedLink = `connectify://chat/${data.userId}`;
              console.log('🔧 Constructed chat deep link for cold start:', constructedLink);
              return constructedLink;
            }
            if (data.type && data.topicId) {
              const constructedLink = `connectify://topic/${data.topicId}`;
              console.log('🔧 Constructed topic deep link for cold start:', constructedLink);
              return constructedLink;
            }
            if (data.type && data.groupId) {
              const constructedLink = `connectify://group/${data.groupId}`;
              console.log('🔧 Constructed group deep link for cold start:', constructedLink);
              return constructedLink;
            }
            if (data.type === 'quiz' && data.questionId) {
              const constructedLink = `connectify://quiz/${data.questionId}`;
              console.log('🔧 Constructed quiz deep link for cold start:', constructedLink);
              return constructedLink;
            }

            // If we have notification data but can't construct a deep link,
            // return a special URL that we can handle in the subscribe method
            if (data.type) {
              console.log('🔧 Creating fallback URL for cold start navigation');
              return `connectify://notification/${data.type}`;
            }
          }

          return null;
        },
        subscribe(listener) {
          const onReceiveURL = ({ url }: { url: string }) => {
            console.log('🔗 Received URL via subscribe:', url);

            // Handle special cold start notification URLs
            if (url.startsWith('connectify://notification/')) {
              const notificationType = url.split('/').pop();
              console.log('🚀 Handling cold start notification type:', notificationType);

              // Get the stored notification data
              if (pendingNotificationData) {
                console.log('📦 Processing stored notification data:', pendingNotificationData);

                const dataToProcess = pendingNotificationData;
                // Clear the stored data
                pendingNotificationData = null;

                // Handle the navigation manually using the stored data
                setTimeout(() => {
                  handleColdStartNotification(dataToProcess);
                }, 1000); // Delay to ensure navigation is ready

                return; // Don't pass this special URL to React Navigation
              }
            }

            listener(url);
          };

          // Listen to incoming links from deep linking
          const eventListenerSubscription = Linking.addEventListener('url', onReceiveURL);

          // Listen to expo push notifications (for when app is running)
          const subscription = Notifications.addNotificationResponseReceivedListener(response => {
            const data = response.notification.request.content.data;
            console.log('📱 Received push notification (app running):', data);

            // Use the deepLink field if available
            if (data.deepLink) {
              console.log('🚀 Using backend deep link:', data.deepLink);
              listener(data.deepLink);
              return;
            }

            // Fallback to legacy URL field
            if (data.url) {
              console.log('🔄 Using legacy URL:', data.url);
              listener(data.url);
              return;
            }

            // Manual construction for notifications without deep links
            if (data.type) {
              let constructedUrl = '';

              if (data.type === 'chat' && data.userId) {
                constructedUrl = `connectify://chat/${data.userId}`;
              } else if (data.type === 'topic' && data.topicId) {
                constructedUrl = `connectify://topic/${data.topicId}`;
              } else if (data.type === 'group' && data.groupId) {
                constructedUrl = `connectify://group/${data.groupId}`;
              } else if (data.type === 'quiz' && data.questionId) {
                constructedUrl = `connectify://quiz/${data.questionId}`;
              }

              if (constructedUrl) {
                console.log('🔧 Constructed URL for running app:', constructedUrl);
                listener(constructedUrl);
                return;
              }
            }

            console.warn('⚠️ Could not handle notification, no valid URL found');
          });

          return () => {
            // Clean up the event listeners
            eventListenerSubscription.remove();
            subscription.remove();
          };
        },
      }}
    >
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isLoggedIn ? (
          <Stack.Screen name='Home' component={HomeNavigator} />
        ) : (
          <Stack.Screen name='Auth' component={AuthNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default AppStack;
