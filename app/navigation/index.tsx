import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useSelector } from 'react-redux';
import { Linking, Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import AuthNavigator from './guest';
import HomeNavigator from './home';
import { navigationRef, isReadyRef } from 'app/navigation/root';
import { IsLoggedIn } from 'app/redux/user/reducer';
import Purchases from 'react-native-purchases';
import { APIKeys } from '../constants/user';

// Global notification queue for cold start handling
let pendingNotificationData: any = null;

// Custom URL parser for web deep links
const parseCustomURL = (url: string): string | null => {
  try {
    console.log('🔍 Parsing URL:', url);

    // Handle web URLs like https://pennytots.com/group/123
    if (url.includes('pennytots.com')) {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      console.log('📍 URL pathname:', pathname);

      // Extract path segments
      const segments = pathname.split('/').filter(Boolean);
      console.log('📍 URL segments:', segments);

      if (segments.length >= 2) {
        const [type, id] = segments;
        console.log('📍 Extracted type:', type, 'id:', id);

        switch (type) {
          case 'group':
            console.log('🔧 Converting group URL to app link:', `connectify://group/${id}`);
            return `connectify://group/${id}`;
          case 'topic':
            console.log('🔧 Converting topic URL to app link:', `connectify://topic/${id}`);
            return `connectify://topic/${id}`;
          case 'chat':
            console.log('🔧 Converting chat URL to app link:', `connectify://chat/${id}`);
            return `connectify://chat/${id}`;
          case 'quiz':
            console.log('🔧 Converting quiz URL to app link:', `connectify://quiz/${id}`);
            return `connectify://quiz/${id}`;
          default:
            console.log('🔧 Unknown URL type:', type, 'defaulting to home');
            return 'connectify://home';
        }
      } else {
        console.log('🔧 Not enough segments, defaulting to home');
        return 'connectify://home';
      }
    }

    // Return original URL if it's already in the correct format
    return url;
  } catch (error) {
    console.error('❌ Error parsing URL:', error);
    return null;
  }
};

// Custom navigation handler for deep links
const handleCustomNavigation = (url: string) => {
  console.log('🚀 Handling custom navigation for URL:', url);

  if (!navigationRef.current) {
    console.warn('⚠️ Navigation ref not ready, retrying in 1 second...');
    setTimeout(() => {
      handleCustomNavigation(url);
    }, 1000);
    return;
  }

  console.log('✅ Navigation ref is ready, proceeding with navigation');

  // Check current navigation state
  try {
    const currentState = (navigationRef.current as any).getState();
    console.log('🔍 Current navigation state:', {
      index: currentState?.index,
      routeNames: currentState?.routeNames,
      routes: currentState?.routes?.map((r: any) => r.name)
    });
  } catch (stateError) {
    console.log('⚠️ Could not get navigation state:', stateError);
  }

  try {
    // Parse connectify:// URLs
    if (url.startsWith('connectify://')) {
      const urlWithoutProtocol = url.replace('connectify://', '');
      const segments = urlWithoutProtocol.split('/');
      const [type, id] = segments;

      console.log('🔍 Parsed segments:', { type, id });

      switch (type) {
        case 'group':
          if (id) {
            console.log('📱 Navigating to group:', id);
            const groupDetails = {
              _id: id,
              name: `Group ${id}`,
              image: null,
              participants: [],
              fromDeepLink: true
            };

            console.log('🔍 Group navigation params:', {
              screen: 'group-chat',
              groupDetails: groupDetails
            });

            try {
              (navigationRef.current as any).navigate('Common', {
                screen: 'group-chat',
                params: {
                  groupDetails: groupDetails
                }
              });
              console.log('✅ Group navigation command executed');
            } catch (error) {
              console.error('❌ Error during group navigation:', error);
            }
          }
          break;

        case 'topic':
          if (id) {
            console.log('📱 Navigating to topic:', id);
            try {
              (navigationRef.current as any).navigate('Common', {
                screen: 'view-topic',
                params: {
                  postid: id
                }
              });
              console.log('✅ Topic navigation command executed');
            } catch (error) {
              console.error('❌ Error during topic navigation:', error);
            }
          }
          break;

        case 'chat':
          if (id) {
            console.log('📱 Navigating to chat:', id);
            const chatParams = {
              postid: `User ${id}`,
              chatuser: id,
              loggedUser: 'current-user',
              userDetails: {
                _id: id,
                first_name: `User`,
                last_name: id,
                profile_picture: null,
                fromDeepLink: true
              }
            };

            console.log('🔍 Chat navigation params:', chatParams);

            try {
              (navigationRef.current as any).navigate('Common', {
                screen: 'private-chat',
                params: chatParams
              });
              console.log('✅ Chat navigation command executed');
            } catch (error) {
              console.error('❌ Error during chat navigation:', error);
            }
          }
          break;

        case 'quiz':
          if (id) {
            console.log('📱 Navigating to quiz:', id);
            (navigationRef.current as any).navigate('Home', {
              screen: 'Quiz',
              params: {
                screen: 'GameMode',
                params: {
                  screen: 'QuizScreen',
                  params: {
                    questionId: id
                  }
                }
              }
            });
          }
          break;

        case 'home':
        default:
          console.log('📱 Navigating to home');
          (navigationRef.current as any).navigate('Home');
          break;
      }
    }
  } catch (error) {
    console.error('❌ Error in custom navigation:', error);
    // Fallback to home
    (navigationRef.current as any).navigate('Home');
  }
};


const Stack = createNativeStackNavigator();

export function AppStack() {
  const userId = useSelector((state:any) => state.user.userId);
  const isLoggedIn = useSelector(IsLoggedIn);

    useEffect(() => {
      if (userId) {
        // Configure RevenueCat with your user ID
        Purchases.configure({
          apiKey: Platform.OS === 'ios' ? APIKeys.apple : APIKeys.google,
          appUserID: userId,
        });
      }
    }, [userId]);

  // Add URL handling for app state changes
  useEffect(() => {
    const handleURL = (event: { url: string }) => {
      console.log('🔗 App received URL from background:', event.url);
      // The NavigationContainer will automatically handle the URL
    };

    // Listen for URL events when app comes from background
    const subscription = Linking.addEventListener('url', handleURL);

    return () => {
      subscription?.remove();
    };
  }, []);

  // Handle cold start notifications manually
  const handleColdStartNotification = (notificationData: any) => {
    console.log('🚀 Handling cold start notification:', notificationData);

    if (!navigationRef.current || !isLoggedIn) {
      console.warn('⚠️ Navigation not ready or user not logged in');
      return;
    }

    try {
      const { type } = notificationData;

      switch (type) {
        case 'chat':
          console.log('Navigate to chat (cold start):', {
            userId: notificationData.userId,
            userName: notificationData.userName
          });

          (navigationRef.current as any).navigate('Home', {
            screen: 'Common',
            params: {
              screen: 'private-chat',
              params: {
                userDetails: notificationData.userDetails
              }
            }
          });
          break;

        case 'topic':
          console.log('Navigate to topic (cold start):', {
            topicId: notificationData.topicId,
            topicTitle: notificationData.topicTitle
          });

          (navigationRef.current as any).navigate('Home', {
            screen: 'Common',
            params: {
              screen: 'view-topic',
              params: {
                postid: notificationData.topicId
              }
            }
          });
          break;

        case 'group':
          console.log('Navigate to group (cold start):', {
            groupId: notificationData.groupId,
            groupName: notificationData.groupName
          });

          (navigationRef.current as any).navigate('Home', {
            screen: 'Common',
            params: {
              screen: 'group-chat',
              params: {
                groupDetails: notificationData.groupDetails
              }
            }
          });
          break;

        case 'quiz':
          console.log('Navigate to quiz (cold start):', {
            questionId: notificationData.questionId,
            questionText: notificationData.questionText
          });

          (navigationRef.current as any).navigate('Home', {
            screen: 'Quiz',
            params: {
              screen: 'GameMode',
              params: {
                screen: 'QuizScreen',
                params: {
                  questionData: notificationData.questionData,
                  questionId: notificationData.questionId
                }
              }
            }
          });
          break;

        default:
          console.warn(`⚠️ Unknown notification type for cold start: ${type}`);
          (navigationRef.current as any).navigate('Home');
      }
    } catch (error) {
      console.error('❌ Error handling cold start notification:', error);
      (navigationRef.current as any).navigate('Home');
    }
  };
  return (
   <NavigationContainer
      ref={navigationRef as any}
      onReady={() => {
        (isReadyRef as any).current = true;
        console.log('🚀 Navigation is ready');

        // Handle any pending cold start notifications
        if (pendingNotificationData && isLoggedIn) {
          console.log('📦 Processing pending notification after navigation ready');
          const dataToProcess = pendingNotificationData;
          pendingNotificationData = null;

          // Small delay to ensure everything is fully initialized
          setTimeout(() => {
            handleColdStartNotification(dataToProcess);
          }, 500);
        }
      }}
      linking={{
        prefixes: ['connectify://', 'https://pennytots.com', 'https://pennytots-admin.netlify.app'],
        config: {
          screens: {
            Home: 'home',
            Auth: 'auth',
          },
        },
        async getInitialURL() {
          // First, check if app was opened from a deep link
          const url = await Linking.getInitialURL();
          console.log('🔗 Initial URL from deep link:', url);

          if (url != null) {
            console.log('🔗 Processing initial URL:', url);

            // Handle web URLs (pennytots.com) - these are direct deep links, not notifications
            if (url.includes('pennytots.com')) {
              console.log('🌐 Handling initial web URL:', url);
              const parsedUrl = parseCustomURL(url);
              if (parsedUrl) {
                console.log('🔧 Parsed initial URL:', parsedUrl);
                // Delay navigation to ensure the app is ready
                setTimeout(() => {
                  handleCustomNavigation(parsedUrl);
                }, 1500);
                return null; // Don't let React Navigation handle this
              }
            }

            // Handle connectify:// URLs directly (but not notification URLs)
            if (url.startsWith('connectify://') && !url.includes('notification')) {
              console.log('🔗 Handling initial connectify URL:', url);
              setTimeout(() => {
                handleCustomNavigation(url);
              }, 1500);
              return null; // Don't let React Navigation handle this
            }

            // For notification URLs and other URLs, let the normal flow handle them
            return url;
          }

          // Handle URL from expo push notifications (COLD START)
          const response = await Notifications.getLastNotificationResponseAsync();
          console.log('📱 Cold start notification response:', response);

          if (response) {
            const data = response.notification.request.content.data;
            console.log('📱 Cold start notification data:', data);

            // Store notification data for delayed navigation
            // We'll handle this after navigation is ready
            if (data && data.type) {
              // Store in module-level variable for cold start handling
              pendingNotificationData = data;
              console.log('📦 Stored pending notification data for cold start');
            }

            // Use the new deepLink field from backend (primary)
            if (data.deepLink) {
              console.log('🚀 Using backend deep link for cold start:', data.deepLink);
              return data.deepLink;
            }

            // Fallback to legacy URL field
            if (data.url) {
              console.log('🔄 Using legacy notification URL for cold start:', data.url);
              return data.url;
            }

            // Manual deep link construction if deepLink is missing
            if (data.type && data.userId) {
              const constructedLink = `connectify://chat/${data.userId}`;
              console.log('🔧 Constructed chat deep link for cold start:', constructedLink);
              return constructedLink;
            }
            if (data.type && data.topicId) {
              const constructedLink = `connectify://topic/${data.topicId}`;
              console.log('🔧 Constructed topic deep link for cold start:', constructedLink);
              return constructedLink;
            }
            if (data.type && data.groupId) {
              const constructedLink = `connectify://group/${data.groupId}`;
              console.log('🔧 Constructed group deep link for cold start:', constructedLink);
              return constructedLink;
            }
            if (data.type === 'quiz' && data.questionId) {
              const constructedLink = `connectify://quiz/${data.questionId}`;
              console.log('🔧 Constructed quiz deep link for cold start:', constructedLink);
              return constructedLink;
            }

            // If we have notification data but can't construct a deep link,
            // return a special URL that we can handle in the subscribe method
            if (data.type) {
              console.log('🔧 Creating fallback URL for cold start navigation');
              return `connectify://notification/${data.type}`;
            }
          }

          return null;
        },
        subscribe(listener) {
          const onReceiveURL = ({ url }: { url: string }) => {
            console.log('🔗 Received URL via subscribe:', url);

            // Handle web URLs (pennytots.com) - these are direct deep links, not notifications
            if (url.includes('pennytots.com')) {
              console.log('🌐 Handling web URL directly:', url);
              const parsedUrl = parseCustomURL(url);
              if (parsedUrl) {
                console.log('🔧 Using parsed URL:', parsedUrl);
                handleCustomNavigation(parsedUrl);
                return; // Don't pass to React Navigation or notification handler
              } else {
                console.warn('⚠️ Failed to parse web URL:', url);
              }
            }

            // Handle special cold start notification URLs
            if (url.startsWith('connectify://notification/')) {
              const notificationType = url.split('/').pop();
              console.log('🚀 Handling cold start notification type:', notificationType);

              // Get the stored notification data
              if (pendingNotificationData) {
                console.log('📦 Processing stored notification data:', pendingNotificationData);

                const dataToProcess = pendingNotificationData;
                // Clear the stored data
                pendingNotificationData = null;

                // Handle the navigation manually using the stored data
                setTimeout(() => {
                  handleColdStartNotification(dataToProcess);
                }, 1000); // Delay to ensure navigation is ready

                return; // Don't pass this special URL to React Navigation
              }
            }

            // Handle connectify:// URLs directly (but not notification URLs)
            if (url.startsWith('connectify://') && !url.includes('notification')) {
              console.log('🔗 Handling connectify URL directly:', url);
              handleCustomNavigation(url);
              return;
            }

            // For all other URLs, let React Navigation handle them
            listener(url);
          };

          // Listen to incoming links from deep linking
          const eventListenerSubscription = Linking.addEventListener('url', onReceiveURL);

          // Note: Notification handling is done by useNotification hook in home navigator
          // This avoids duplicate notification listeners

          return () => {
            // Clean up the event listeners
            eventListenerSubscription.remove();
          };
        },
      }}
    >
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isLoggedIn ? (
          <Stack.Screen name='Home' component={HomeNavigator} />
        ) : (
          <Stack.Screen name='Auth' component={AuthNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default AppStack;
