// RootNavigation.js
import React from 'react';
export const isReadyRef = React.createRef();
export const navigationRef = React.createRef();
import { createNavigationContainerRef } from '@react-navigation/native';

export async function navigate(name, params) {
  if (isReadyRef.current && navigationRef.current) {
    // Perform navigation if the app has mounted

    navigationRef.current.navigate(name, params);
  } else {
    // You can decide what to do if the app hasn't mounted
    // You can ignore this, or add these actions to a queue you can call later

    setTimeout(() => {
      // navigationRef.current?.navigate(name, params);
      // console.log('we here', name, params);

      navigate(name, params);
    }, 1000);
  }
}
