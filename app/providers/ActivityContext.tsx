import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { useGetSponsoredAd } from 'app/redux/sponsor/hooks';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showModal, hideModal } from 'app/providers/modals'; // Import your modal functions
import { Share } from 'react-native';
import { navigate } from 'app/navigation/root';
import { InteractionManager } from 'react-native';


// Define types for the context state
type ActivityCounts = {
  sponsor: number;
  share: number;
};

type ModalVisible = {
  sponsor: boolean;
  share: boolean;
};

interface ActivityContextProps {
  sponsoredAd: any;     // shape it to your actual type e.g. SponsoredAd | null
  isAdLoading: boolean;
  // Add other activity context values here
}



interface ActivityContextType {
  activityCounts: ActivityCounts;
  incrementActivity: (type: keyof ActivityCounts) => void;
  modalVisible: ModalVisible;
  resetModalVisibility: (type: keyof ModalVisible) => void;
  sponsoredAd: any,
  isAdLoading: boolean,
}

// Create the context with the appropriate type
const ActivityContext = createContext<ActivityContextType | undefined>(undefined);

// Key for storing activity counts in AsyncStorage
const STORAGE_KEY = 'activity_counts';



// Function to handle native sharing
const handleNativeShare = async () => {
  console.log("entered here 1")
  try {
    const result = await Share.share({
      message: 'The Connectify Network app is now live! Join on Android (www.pennytots.com) or Apple (www.pennytots.com)',
      url: 'https://example.com', // Optional: Add a URL to be shared
      title: 'Share with your friends', // Optional: Title for Android
    });

    if (result.action === Share.sharedAction) {
      if (result.activityType) {
        console.log('Shared with activity type:', result.activityType);
      } else {
        console.log('Shared successfully!');
      }
    } else if (result.action === Share.dismissedAction) {
      console.log('Share dismissed');
    }
  } catch (error: any) {
    console.error('Failed to share:', error.message);
  }
};


// Provider component
export const ActivityProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [activityCounts, setActivityCounts] = useState<ActivityCounts>({ sponsor: 0, share: 0 });
  const [modalVisible, setModalVisible] = useState<ModalVisible>({ sponsor: false, share: false });
  const [nextModal, setNextModal] = useState<'share' | 'sponsor'>('share'); // Track next modal
  const { adData, isLoading,refetch } = useGetSponsoredAd();
  const [sponsoredAd, setSponsoredAd] = useState(null);

  useEffect(() => {
    if (adData) {
      // adData is the actual ad object from the backend
      setSponsoredAd(adData);
    }
  }, [adData]);



  // Save and load activity counts (unchanged)
  const saveActivityCounts = async (counts: ActivityCounts) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(counts));
    } catch (error) {
      console.error('Failed to save activity counts to storage:', error);
    }
  };

  // Function to load activity counts from AsyncStorage
  const loadActivityCounts = async () => {
    try {
      const storedCounts = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedCounts) {
        setActivityCounts(JSON.parse(storedCounts));
      }
    } catch (error) {
      console.error('Failed to load activity counts from storage:', error);
    }
  };

  // Function to increment the activity count
  const incrementActivity = (type: keyof ActivityCounts) => {
    setActivityCounts((prevCounts) => {
      const newCounts = {
        ...prevCounts,
        [type]: prevCounts[type] + 1,
      };
      saveActivityCounts(newCounts); // Save to local storage
      return newCounts;
    });
  };

  // Function to reset the activity count for a specific type
  const resetActivityCount = (type: keyof ActivityCounts) => {
    setActivityCounts((prevCounts) => {
      const newCounts = {
        ...prevCounts,
        [type]: 0,
      };
      saveActivityCounts(newCounts); // Save the reset count to local storage
      return newCounts;
    });
  };

  // Check thresholds for displaying modals
  useEffect(() => {
    // Check based on nextModal state
    if (nextModal === 'share' && activityCounts.share >= 10) {
      // Show share modal
      setModalVisible((prev) => ({ ...prev, share: true }));
      showModal({
        modalVisible: true,
        title: 'Share Alert',
        message: 'You have unlocked a share prompt!',
        setModalVisible: hideModal,
        type: 'share-alert',
        handleConfirm: () => {
          handleNativeShare()
          console.log('Share confirmed!');
          hideModal();
          setModalVisible((prev) => ({ ...prev, share: false }));
        },
        handleAlert: () => {
          console.log('Share alert handled!');
          hideModal();
          setModalVisible((prev) => ({ ...prev, share: false }));
        },
      });
      // Reset counts and set next modal
      resetActivityCount('share');
      resetActivityCount('sponsor');
      setNextModal('sponsor');
    } else if (nextModal === 'sponsor' && activityCounts.sponsor >= 9) {
      refetch()
      // Show sponsor modal
      setModalVisible((prev) => ({ ...prev, sponsor: true }));
      setTimeout(() => {
        navigate('Common', {
          screen: 'sponsor',
          params: { adImage: require("app/assets/images/knob.png") },
        });
      }, 5000);
      // Reset counts and set next modal
      resetActivityCount('sponsor');
      resetActivityCount('share');
      setNextModal('share');
    }
  }, [activityCounts, nextModal]);

  // Function to reset modal visibility
  const resetModalVisibility = (type: keyof ModalVisible) => {
    setModalVisible((prev) => ({ ...prev, [type]: false }));
    hideModal();
  };

  return (
    <ActivityContext.Provider
      value={{ activityCounts, incrementActivity, modalVisible, resetModalVisibility,sponsoredAd, isAdLoading: isLoading }}
    >
      {children}
    </ActivityContext.Provider>
  );
};

// Custom hook to use the ActivityContext
export const useActivity = (): ActivityContextType => {
  const context = useContext(ActivityContext);
  if (!context) {
    throw new Error('useActivity must be used within an ActivityProvider');
  }
  return context;
};


