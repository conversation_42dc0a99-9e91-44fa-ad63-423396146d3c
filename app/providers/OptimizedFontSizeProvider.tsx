import React, { createContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  FontSizeScale, 
  FONT_SIZE_MULTIPLIERS,
  fontSizeCache,
  setGlobalFontScale,
  fs,
  FontSizeKey
} from 'app/utils/optimizedFontSizes';

// Font size labels for UI
export const FONT_SIZE_LABELS: Record<FontSizeScale, string> = {
  'small': 'Small',
  'normal': 'Normal',
  'large': 'Large',
  'extra-large': 'Extra Large',
};

// Storage key for font size preference
const FONT_SIZE_STORAGE_KEY = '@app_font_size_optimized';
const DEFAULT_FONT_SIZE: FontSizeScale = 'normal';

// Optimized context type
export interface OptimizedFontSizeContextType {
  fontSizeScale: FontSizeScale;
  fontSizeMultiplier: number;
  setFontSizeScale: (scale: FontSizeScale) => void;
  // Optimized font size function
  fs: (sizeKey: FontSizeKey) => number;
  // Get multiple sizes at once
  getFontSizes: (sizeKeys: FontSizeKey[]) => Record<FontSizeKey, number>;
  isLoading: boolean;
}

// Create optimized font size context
export const OptimizedFontSizeContext = createContext<OptimizedFontSizeContextType | undefined>(undefined);

interface OptimizedFontSizeProviderProps {
  children: ReactNode;
}

export const OptimizedFontSizeProvider: React.FC<OptimizedFontSizeProviderProps> = ({ children }) => {
  const [fontSizeScale, setFontSizeScaleState] = useState<FontSizeScale>(DEFAULT_FONT_SIZE);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved font size from storage on app start
  useEffect(() => {
    loadSavedFontSize();
  }, []);

  // Update global font size scale whenever fontSizeScale changes
  useEffect(() => {
    setGlobalFontScale(fontSizeScale);
  }, [fontSizeScale]);

  const loadSavedFontSize = async () => {
    try {
      const savedFontSize = await AsyncStorage.getItem(FONT_SIZE_STORAGE_KEY);
      if (savedFontSize && Object.keys(FONT_SIZE_MULTIPLIERS).includes(savedFontSize)) {
        setFontSizeScaleState(savedFontSize as FontSizeScale);
      }
    } catch (error) {
      console.warn('Failed to load saved font size:', error);
      setFontSizeScaleState(DEFAULT_FONT_SIZE);
    } finally {
      setIsLoading(false);
    }
  };

  const saveFontSize = async (scale: FontSizeScale) => {
    try {
      await AsyncStorage.setItem(FONT_SIZE_STORAGE_KEY, scale);
    } catch (error) {
      console.warn('Failed to save font size:', error);
    }
  };

  const setFontSizeScale = useCallback((scale: FontSizeScale) => {
    setFontSizeScaleState(scale);
    saveFontSize(scale);
  }, []);

  // Memoized font size multiplier
  const fontSizeMultiplier = useMemo(() => {
    return FONT_SIZE_MULTIPLIERS[fontSizeScale];
  }, [fontSizeScale]);

  // Memoized font size function that uses current scale
  const optimizedFs = useCallback((sizeKey: FontSizeKey): number => {
    return fs(sizeKey, fontSizeScale);
  }, [fontSizeScale]);

  // Memoized function to get multiple font sizes
  const getFontSizes = useCallback((sizeKeys: FontSizeKey[]): Record<FontSizeKey, number> => {
    const result = {} as Record<FontSizeKey, number>;
    sizeKeys.forEach(key => {
      result[key] = fs(key, fontSizeScale);
    });
    return result;
  }, [fontSizeScale]);

  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo<OptimizedFontSizeContextType>(() => ({
    fontSizeScale,
    fontSizeMultiplier,
    setFontSizeScale,
    fs: optimizedFs,
    getFontSizes,
    isLoading,
  }), [fontSizeScale, fontSizeMultiplier, setFontSizeScale, optimizedFs, getFontSizes, isLoading]);

  return (
    <OptimizedFontSizeContext.Provider value={contextValue}>
      {children}
    </OptimizedFontSizeContext.Provider>
  );
};

// Export for backward compatibility
export { FONT_SIZE_MULTIPLIERS } from 'app/utils/optimizedFontSizes';
