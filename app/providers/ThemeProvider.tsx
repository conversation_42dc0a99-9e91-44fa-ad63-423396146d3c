import React, { createContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StatusBar } from 'expo-status-bar';
import { Appearance, ColorSchemeName } from 'react-native';
import { ThemeContextType, ThemeMode } from 'app/types/theme';
import { getTheme } from 'app/assets/themes';
import { THEME_STORAGE_KEY, DEFAULT_THEME_MODE } from 'app/constants/theme';

// Create theme context
export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [themeMode, setThemeMode] = useState<ThemeMode>(DEFAULT_THEME_MODE);
  const [systemColorScheme, setSystemColorScheme] = useState<ColorSchemeName>(
    Appearance.getColorScheme()
  );
  const [isLoading, setIsLoading] = useState(true);

  // Load saved theme from storage on app start
  useEffect(() => {
    loadSavedTheme();
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setSystemColorScheme(colorScheme);
    });

    return () => subscription?.remove();
  }, []);

  const loadSavedTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark' || savedTheme === 'system')) {
        setThemeMode(savedTheme as ThemeMode);
      }
    } catch (error) {
      console.warn('Failed to load saved theme:', error);
      // Default to system theme if loading fails
      setThemeMode(DEFAULT_THEME_MODE);
    } finally {
      setIsLoading(false);
    }
  };

  const saveTheme = async (mode: ThemeMode) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
    } catch (error) {
      console.warn('Failed to save theme:', error);
    }
  };

  const setTheme = (mode: ThemeMode) => {
    setThemeMode(mode);
    saveTheme(mode);
  };

  const toggleTheme = () => {
    if (themeMode === 'system') {
      // If currently on system, switch to the opposite of current system theme
      const newMode = systemColorScheme === 'dark' ? 'light' : 'dark';
      setTheme(newMode);
    } else {
      // Toggle between light and dark
      const newMode = themeMode === 'light' ? 'dark' : 'light';
      setTheme(newMode);
    }
  };

  // Resolve actual theme mode (convert 'system' to 'light' or 'dark')
  const resolveThemeMode = (): 'light' | 'dark' => {
    if (themeMode === 'system') {
      return systemColorScheme === 'dark' ? 'dark' : 'light';
    }
    return themeMode;
  };

  // Get current theme object
  const actualThemeMode = resolveThemeMode();
  const theme = getTheme(actualThemeMode);

  const contextValue: ThemeContextType = {
    theme,
    themeMode,
    toggleTheme,
    setTheme,
    isLoading,
  };

  // Convert theme status bar style to expo-status-bar format
  const statusBarStyle = theme.colors.statusBarStyle === 'light-content' ? 'light' : 'dark';

  return (
    <ThemeContext.Provider value={contextValue}>
      <StatusBar style={statusBarStyle} />
      {children}
    </ThemeContext.Provider>
  );
};
