import React from 'react';
import { Modal, View, StyleSheet, Text, TouchableOpacity, Image } from 'react-native';
import RootSiblings from 'react-native-root-siblings';
import CustomModal from 'app/components/elements/Modals'; // Adjust the import based on your file structure

interface GlobalModalProps {
  modalVisible: boolean;
  title?: string;
  message: string;
  setModalVisible: (visible: boolean) => void;
  NavigateToCreditPage?: () => void;
  handleConfirm: () => void;
  handleAlert?: () => void;
  navigation?:any;
  type: any;
}

let globalModal: RootSiblings | null = null;

export const showModal = (props: GlobalModalProps) => {
  const { modalVisible, title, message, setModalVisible, NavigateToCreditPage, handleConfirm, handleAlert, type,navigation } = props;
  console.log(type,"type")
  const modalComponent = (
    <CustomModal
      modalVisible={modalVisible}
      title={title}
      message={message}
      setModalVisible={setModalVisible}
      NavigateToCreditPage={NavigateToCreditPage}
      handleConfirm={handleConfirm}
      handleAlert={handleAlert}
      type={type}
      navigation={navigation}
    />
  );

  if (globalModal) {
    globalModal.update(modalComponent);
  } else {
    globalModal = new RootSiblings(modalComponent);
  }
};

export const hideModal = () => {
  if (globalModal instanceof RootSiblings) {
    globalModal.destroy();
    globalModal = null;
  }
};
