import { PixelRatio, Dimensions } from 'react-native';

const pixelRatio = PixelRatio.get();
const deviceHeight = Dimensions.get('window').height;
const deviceWidth = Dimensions.get('window').width;


export const responsiveValue = (size: number) => {
  let scaleFactor = 1;

  if (pixelRatio < 2) {
    scaleFactor = 1; // Default scale for low pixel density devices
  } else if (pixelRatio >= 2 && pixelRatio < 3) {
    // Medium pixel density devices
    if (deviceWidth < 360) {
      scaleFactor = 0.95;
    } else if (deviceHeight < 667) {
      scaleFactor = 1;
    } else if (deviceHeight <= 735) {
      scaleFactor = 0.95;
    } else {
      scaleFactor = 1.25;
    }
  } else if (pixelRatio >= 3) {
    // High pixel density devices
    if (deviceWidth <= 360) {
      scaleFactor = 1;
    } else if (deviceHeight < 667) {
      scaleFactor = 1.15;
    } else if (deviceHeight <= 735) {
      scaleFactor = 1.2;
    } else {
      scaleFactor = 1.27;
    }
  }

  return size * scaleFactor;
};
