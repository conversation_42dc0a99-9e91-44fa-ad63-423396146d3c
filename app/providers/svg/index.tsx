import React, { FunctionComponent } from 'react';
import { ISVG } from './types';

import LoginHeaderSVG from 'app/assets/svg/login-header.svg';
import ProfileSVG from 'app/assets/svg/profile.svg';
import HomeTabSVG from 'app/assets/svg/home-tab.svg';
import SearchSVG from 'app/assets/svg/search.svg';
import GroupSVG from 'app/assets/svg/group.svg';
import CommentSVG from 'app/assets/svg/comment.svg';
import ConvoSVG from 'app/assets/svg/convo.svg';
import ForgotPasswordSVG from 'app/assets/svg/forgot-password.svg';
import Quiz from 'app/assets/svg/quiz.svg';
import HomeLightSVG from 'app/assets/svg/postlucosa.svg';
import HomeDarkSVG from 'app/assets/svg/homelucosaDark.svg';
import TriviaLightSVG from 'app/assets/svg/trivialucosa.svg';
import TriviaDarkSVG from 'app/assets/svg/triviaDark.svg';
import CommentLightSVG from 'app/assets/svg/commentlucosa.svg';
import CommentDarkSVG from 'app/assets/svg/commentlucosaDark.svg';
import GroupLightSVG from 'app/assets/svg/grouplucosa.svg';
import GroupDarkSVG from 'app/assets/svg/grouplucosaDark.svg';
import SearchLightSVG from 'app/assets/svg/searchlucosa.svg';
import SearchDarkSVG from 'app/assets/svg/searchDark.svg';


const SVG: FunctionComponent<ISVG> = ({ name, width, height, size }) => {
  if (size) {
    width = size;
    height = size;
  }

  const widthProps = width ? { width } : {};
  const heightProps = height ? { height } : {};

  switch (name) {
    case 'comment':
      return <CommentSVG {...widthProps} {...heightProps} />;
    case 'login-header':
      return <LoginHeaderSVG {...widthProps} {...heightProps} />;
    case 'home-tab':
      return <HomeTabSVG {...widthProps} {...heightProps} />;
    case 'group':
      return <GroupSVG {...widthProps} {...heightProps} />;
    case 'search':
      return <SearchSVG {...widthProps} {...heightProps} />;
    case 'conversation':
      return <ConvoSVG {...widthProps} {...heightProps} />;
    case 'forgot-password':
        return <ForgotPasswordSVG {...widthProps} {...heightProps} />;
    case 'quiz':
      return <Quiz {...widthProps} {...heightProps} />;
      case 'triviaLight':
        return <TriviaLightSVG {...widthProps} {...heightProps} />;
      case 'triviaDark':
        return <TriviaDarkSVG {...widthProps} {...heightProps} />;
      case 'homeLight':
        return <HomeLightSVG {...widthProps} {...heightProps} />;
      case 'homeDark':
        return <HomeDarkSVG {...widthProps} {...heightProps} />;
      case 'commentLight':
        return <CommentLightSVG {...widthProps} {...heightProps} />;
      case 'commentDark':
        return <CommentDarkSVG {...widthProps} {...heightProps} />;
      case 'groupLight':
        return <GroupLightSVG {...widthProps} {...heightProps} />;
      case 'groupDark':
        return <GroupDarkSVG {...widthProps} {...heightProps} />;
      case 'searchLight':
        return <SearchLightSVG {...widthProps} {...heightProps} />;
      case 'searchDark':
        return <SearchDarkSVG {...widthProps} {...heightProps} />;
      default:
        return <CommentSVG {...widthProps} {...heightProps} />;
  }
};

export default SVG;
