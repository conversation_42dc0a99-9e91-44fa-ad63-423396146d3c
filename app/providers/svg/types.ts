export type SVGNames =
  'login-header' |
  'comment' |
  'home-tab' |
  'group' |
  'search' |
  'conversation' |
  'profile' |
  'forgot-password' |
  'quiz' |
  'triviaLight' |
  'homeLight' |
  'homeDark' |
  'triviaDark' |
  'commentLight' |
  'commentDark' |
  'groupLight' |
  'groupDark' |
  'searchLight' |
  'searchDark';

export interface ISVG {
    name: SVGNames;
    width?: number;
    height?: number;
    size?: number;
}