import { chat } from 'app/api';
import { useQuery } from '@tanstack/react-query';
import { queryClient } from 'app/redux/user/hooks';

export default function useMyChat() {
  return useQuery(['my-chats'], chat.getChats, {
    staleTime: 1000 * 60 * 3, // 3 minutes - chat list changes moderately
    cacheTime: 1000 * 60 * 60, // 1 hour cache
    refetchOnWindowFocus: false, // Disable automatic refetch on focus
    refetchOnMount: false, // Don't refetch if data is fresh
    // Enable background refetch for chat list since new messages arrive
    refetchInterval: 1000 * 60 * 2, // Refetch every 2 minutes in background
  });
}

// Hook for optimistic chat updates
export function useOptimisticChatUpdate() {
  return {
    updateChatLastMessage: (chatId: string, message: any) => {
      queryClient.setQueryData(['my-chats'], (oldData: any) => {
        if (!oldData) return oldData;

        return oldData.map((chat: any) => {
          if (chat._id === chatId) {
            return {
              ...chat,
              lastMessage: message,
              updatedAt: new Date().toISOString(),
            };
          }
          return chat;
        });
      });
    },

    addNewChat: (newChat: any) => {
      queryClient.setQueryData(['my-chats'], (oldData: any) => {
        if (!oldData) return [newChat];
        return [newChat, ...oldData];
      });
    }
  };
}

// Hook for optimistic message sending
export function useOptimisticMessageSend() {
  return {
    addOptimisticMessage: (chatId: string, message: any, tempId: string) => {
      const optimisticMessage = {
        ...message,
        _id: tempId,
        pending: true,
        createdAt: new Date().toISOString(),
      };

      // Update chat list with new last message
      queryClient.setQueryData(['my-chats'], (oldData: any) => {
        if (!oldData) return oldData;

        return oldData.map((chat: any) => {
          if (chat._id === chatId) {
            return {
              ...chat,
              lastMessage: optimisticMessage,
              updatedAt: new Date().toISOString(),
            };
          }
          return chat;
        });
      });

      return optimisticMessage;
    },

    updateMessageStatus: (chatId: string, tempId: string, realMessage: any) => {
      // Update the message with real data from server
      queryClient.setQueryData(['my-chats'], (oldData: any) => {
        if (!oldData) return oldData;

        return oldData.map((chat: any) => {
          if (chat._id === chatId && chat.lastMessage?._id === tempId) {
            return {
              ...chat,
              lastMessage: { ...realMessage, pending: false },
            };
          }
          return chat;
        });
      });
    },

    removeFailedMessage: (chatId: string, tempId: string) => {
      // Remove failed message and revert to previous last message
      queryClient.setQueryData(['my-chats'], (oldData: any) => {
        if (!oldData) return oldData;

        return oldData.map((chat: any) => {
          if (chat._id === chatId && chat.lastMessage?._id === tempId) {
            // You might want to fetch the actual last message from server
            // For now, just mark as failed
            return {
              ...chat,
              lastMessage: { ...chat.lastMessage, failed: true, pending: false },
            };
          }
          return chat;
        });
      });
    }
  };
}
