import { useMutation, useQuery } from '@tanstack/react-query';
import {queryClient} from "app/redux/user/hooks"
import { credit } from 'app/api/credit';
import { ShowAlert } from 'app/providers/toast';
import { showModal, hideModal } from 'app/providers/modals';

export function useGetCredit() {
  return useQuery(['credit'], credit.getCredit);
}

export function useGetSubscription() {
  return useQuery(['subscription'], credit.getSubscription);
}

export function useGetFreeCredit({ onSuccess : customOnSuccess , onError: customOnError  }) {
  return useMutation(
    () => {
      return credit.getFreeCredit();
    },
    {
      onSuccess: (response:any, variables, context) => {
        queryClient.invalidateQueries(['credit']);
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: response.message,
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });

        // ShowAlert({
        //   type: 'success',
        //   className: 'Success',
        //   message: response.message,
        // });
        if (customOnSuccess) {
          customOnSuccess(response, variables, context);
        }
      },
      onError: (error, variables, context) => {
        if (customOnError) {
          customOnError(error, variables, context);
        }
      },
    },
  );
}

export function useGetFreeSubscription({ onSuccess : customOnSuccess , onError: customOnError  }) {
  return useMutation(
    () => {
      return credit.getFreeSubscription();
    },
    {
      onSuccess: (response:any, variables, context) => {
        queryClient.invalidateQueries(['subscription']);
        

        // ShowAlert({
        //   type: 'success',
        //   className: 'Success',
        //   message: response.message,
        // });
        if (customOnSuccess) {
          customOnSuccess(response, variables, context);
        }
      },
      onError: (error, variables, context) => {
        if (customOnError) {
          customOnError(error, variables, context);
        }
      },
    },
  );
}
export function useGetCreditTransactionHistory() {
  return useQuery(['credit-transaction-history'], credit.getTransactionHistory);
}

export function useBuyCredit() {
  return useMutation(
    (payload: any) => {
      return credit.buyCredit(payload);
    },
    {
      onSuccess: (response, variables, context) => {
        //   queryClient.invalidateQueries(['helpdesk-tickets']);
      },
    },
  );
}

export function useBuySubscription() {
  return useMutation(
    (payload: any) => {
      return credit.buySubscription(payload);
    },
    {
      onSuccess: (response, variables, context) => {
        //   queryClient.invalidateQueries(['helpdesk-tickets']);
      },
    },
  );
}

export function useRestoreSubscription({ onSuccess, onError }: { 
  onSuccess?: (response: any, variables: any, context: any) => void,
  onError?: (error: any, variables: any, context: any) => void 
}) {
  return useMutation(
    (customerInfo: any) => {
      return credit.restoreSubscription({ customerInfo });
    },
    {
      onSuccess: (response: any, variables, context) => {
        queryClient.invalidateQueries(['subscription']);
        if (onSuccess) {
          onSuccess(response, variables, context);
        }
      },
      onError: (error, variables, context) => {
        if (onError) {
          onError(error, variables, context);
        }
      },
    },
  );
}