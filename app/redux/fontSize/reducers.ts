import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { FontSizeScale } from 'app/providers/FontSizeProvider'; // Adjust import based on your project

export interface FontSizeState {
  fontSizeScale: FontSizeScale;
  optimizedFontSizeScale: FontSizeScale; // For backward compatibility
}

const initialState: FontSizeState = {
  fontSizeScale: 'normal', // Default value
  optimizedFontSizeScale: 'normal',
};

const fontSizeSlice = createSlice({
  name: 'fontSize',
  initialState,
  reducers: {
    setFontSizeScale(state, action: PayloadAction<FontSizeScale>) {
      state.fontSizeScale = action.payload;
      state.optimizedFontSizeScale = action.payload; // Sync both for compatibility
    },
  },
});

export const { setFontSizeScale } = fontSizeSlice.actions;
export default fontSizeSlice.reducer;