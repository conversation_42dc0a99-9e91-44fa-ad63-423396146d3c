import { useContext } from 'react';
import { FontSizeContext } from 'app/providers/FontSizeProvider';
import type { FontSizeContextType } from 'app/providers/FontSizeProvider';

/**
 * Hook to access font size context
 * Provides font size scaling functionality throughout the app
 */
export const useFontSize = (): FontSizeContextType => {
  const context = useContext(FontSizeContext);
  
  if (context === undefined) {
    throw new Error('useFontSize must be used within a FontSizeProvider');
  }
  
  return context;
};

/**
 * Hook to get just the font size scaling function
 * Useful when you only need to scale font sizes
 */
export const useFontSizeScale = () => {
  const { scaleFontSize } = useFontSize();
  return scaleFontSize;
};

/**
 * Hook to get font size multiplier
 * Useful for custom scaling calculations
 */
export const useFontSizeMultiplier = () => {
  const { fontSizeMultiplier } = useFontSize();
  return fontSizeMultiplier;
};
