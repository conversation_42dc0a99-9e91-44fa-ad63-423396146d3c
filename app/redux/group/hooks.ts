import { group } from 'app/api';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ShowAlert } from 'app/providers/toast';
import { queryClient } from 'app/redux/user/hooks';
import { showModal, hideModal } from 'app/providers/modals';
import { useState, useEffect } from 'react';
export function useGetGroup(id: string) {
  const { data, isLoading: isQueryLoading } = useQuery(
    ['group', { id }],
    () => {
      return group.getGroup(id);
    },
    {
      enabled: id !== null,
    },
  );

  const isLoading = id !== null && isQueryLoading;

  return { data, isLoading };
}

export function useMyGroups() {
  return useQuery(['my-groups'], group.getMyGroups);
}

export function useSuggestedGroups() {
  return useQuery(['suggested-groups'], group.getSuggestedGroups);
}

export function useUpdateGroup() {
  return useMutation(
    (payload: any) => {
      return group.updateGroup(payload);
    },
    {
      onSuccess: (response, variables, context) => {
        queryClient.invalidateQueries(['group']);
        const successMessage = response.message === 'Group updated successfully'
        ? 'Group profile updated!'
        : response.message;

        showModal({
          modalVisible: true,
          title: 'Alert',
          message: successMessage,
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });

        // ShowAlert({
        //   type: 'success',
        //   className: 'Success',
        //   message: response.message,
        // });
      },
    },
  );
}

export function useUpdateGroupPicture() {
 
  return useMutation(
    (payload: any) => {
      return group.updateGroupPicture(payload);
    },
    {
      onSuccess: (response, variables, context) => {
        queryClient.invalidateQueries(['group']);

        const successMessage = response.message === 'Group Picture Updated Successfully'
        ? 'Group profile picture updated!'
        : response.message;

        showModal({
          modalVisible: true,
          title: 'Alert',
          message: successMessage,

          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        
        });

        // ShowAlert({
        //   type: 'success',
        //   className: 'Success',
        //   message: response.message,
        // });
      },
    },
  );
}


export function useCreateGroup() {
  return useMutation(
    (payload: any) => {
      return group.createGroup(payload);
    },
    {
      onSuccess: (response: any, variables, context) => {
        queryClient.invalidateQueries(['create-group']);
        
      },
    },
  );
}
export function useFetchGroups(userInfo: any){
  return useQuery(['userGroups', userInfo], ()=> group.fetchUserGroups(userInfo))
}

export const useTags = () => {
  const [tags, setTags] = useState([]);
  const [loadingTags, setLoadingTags] = useState(true);

  useEffect(() => {
    group.getTags()
      .then((response) => {
        const formatData = response.data.data.map((tag:any) => ({
          ...tag,
          label: tag.name,
          value: tag._id,
        }));
        setTags(formatData);
      })
      .catch((error) => {
        console.error('Error fetching tags:', error);
      })
      .finally(() => {
        setLoadingTags(false);
      });
  }, []);

  return { tags, loadingTags };
};

export const useIsAdmin = (group:any, myId:any) => {
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (group?.participants?.length) {
      const currentUser = group.participants.find((item:any) => item._id === myId);
      setIsAdmin(currentUser?.admin || false);
    }
  }, [group, myId]);

  return isAdmin;
};