import React from 'react';
import { main } from 'app/api';
import { useQuery } from '@tanstack/react-query';

interface SearchPayload {
  search: string;
  searchType: string;
}

export default function useSearch(payload: SearchPayload) {
  return useQuery(['search', payload], () => main.search(payload), {
    enabled: payload.search.length > 2, //enable if search has value greater than 2
    staleTime: 1000 * 60 * 5, // 5 minutes - search results can be cached
    cacheTime: 1000 * 60 * 30, // 30 minutes cache
    // Debounce search by using a longer stale time for rapid queries
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
}

// Optimized search hook with debouncing
export function useOptimizedSearch(searchTerm: string, searchType: string, debounceMs: number = 500) {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState(searchTerm);

  // Debounce the search term
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  return useQuery(
    ['search', { search: debouncedSearchTerm, searchType }],
    () => main.search({ search: debouncedSearchTerm, searchType }),
    {
      enabled: debouncedSearchTerm.length > 2,
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 30, // 30 minutes
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      // Keep previous data while fetching new results
      keepPreviousData: true,
    }
  );
}

export function useGetUnreadNotifications(enabled: boolean) {
  return useQuery(['unread-notifications'], main.unreadNotifications, {
    refetchInterval: 60000, // 1 min - keep frequent for real-time notifications
    enabled: enabled === true,
    staleTime: 1000 * 30, // 30 seconds - notifications should be fresh
    cacheTime: 1000 * 60 * 5, // 5 minutes cache
    refetchOnMount: true, // Always refetch notifications on mount
    refetchOnReconnect: true, // Refetch when network reconnects
  });
}
