import { newsletter } from 'app/api';
import { useMutation, useQuery } from '@tanstack/react-query';
import { queryClient } from 'app/redux/user/hooks';
import { ShowAlert } from 'app/providers/toast';
import { UpdateNewsletterSettingsPayload } from 'app/api/newsletter';

export function useGetNewsletterSettings() {
  return useQuery(['newsletter-settings'], newsletter.getNewsletterSettings, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useUpdateNewsletterSettings() {
  return useMutation(
    (payload: UpdateNewsletterSettingsPayload) => {
      return newsletter.updateNewsletterSettings(payload);
    },
    {
      onSuccess: (response: any, variables, context) => {
        // Invalidate and refetch newsletter settings
        queryClient.invalidateQueries(['newsletter-settings']);
        
        // Show success message only for major changes
        if (variables.enabled !== undefined) {
          const message = variables.enabled 
            ? 'Successfully subscribed to newsletter' 
            : 'Successfully unsubscribed from newsletter';
          ShowAlert({
            type: 'success',
            message,
          });
        }
      },
      onError: (error: any) => {
        console.error('Error updating newsletter settings:', error);
        ShowAlert({
          type: 'error',
          message: error?.response?.data?.message || 'Failed to update newsletter settings',
        });
      },
    },
  );
}

export function useSubscribeToNewsletter() {
  return useMutation(
    () => {
      return newsletter.subscribeToNewsletter();
    },
    {
      onSuccess: (response: any, variables, context) => {
        queryClient.invalidateQueries(['newsletter-settings']);
        ShowAlert({
          type: 'success',
          message: 'Successfully subscribed to newsletter',
        });
      },
      onError: (error: any) => {
        console.error('Error subscribing to newsletter:', error);
        ShowAlert({
          type: 'error',
          message: error?.response?.data?.message || 'Failed to subscribe to newsletter',
        });
      },
    },
  );
}

export function useUnsubscribeFromNewsletter() {
  return useMutation(
    () => {
      return newsletter.unsubscribeFromNewsletter();
    },
    {
      onSuccess: (response: any, variables, context) => {
        queryClient.invalidateQueries(['newsletter-settings']);
        ShowAlert({
          type: 'success',
          message: 'Successfully unsubscribed from newsletter',
        });
      },
      onError: (error: any) => {
        console.error('Error unsubscribing from newsletter:', error);
        ShowAlert({
          type: 'error',
          message: error?.response?.data?.message || 'Failed to unsubscribe from newsletter',
        });
      },
    },
  );
}

export function usePreviewNewsletter() {
  return useQuery(['newsletter-preview'], newsletter.previewNewsletter, {
    enabled: false, // Only fetch when explicitly called
    staleTime: 0, // Always fresh
    cacheTime: 0, // Don't cache
  });
}

export function useSendNewsletterNow() {
  return useMutation(
    () => {
      return newsletter.sendNewsletterNow();
    },
    {
      onSuccess: (response: any, variables, context) => {
        ShowAlert({
          type: 'success',
          message: 'Newsletter sent successfully',
        });
      },
      onError: (error: any) => {
        console.error('Error sending newsletter:', error);
        ShowAlert({
          type: 'error',
          message: error?.response?.data?.message || 'Failed to send newsletter',
        });
      },
    },
  );
}
