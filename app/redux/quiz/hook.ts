import { quiz } from 'app/api';
import { useMutation, useQuery } from '@tanstack/react-query';
import {queryClient} from "app/redux/user/hooks"
import { country } from 'app/redux/main/reducer'; 
import { useSelector } from 'react-redux';



  export function useGetQuestionsNew() {
    const { data, isLoading, refetch,isFetching } = useQuery(['quiz'], quiz.getQuestionsOld, {
      cacheTime: 0, // Disable caching
      refetchOnMount: false, // Do not refetch on mount
      refetchOnWindowFocus: false, // Do not refetch on window focus
    });
    return { data,isLoading, refetch,isFetching };
  }

  export function useGetQuestions() {
    const countryReal = useSelector(country)
    const { data, isLoading, refetch,isFetching } = useQuery(['quiz', country],  () => quiz.getQuestions(countryReal), {
      cacheTime: 0, // Disable caching
      refetchOnMount: false, // Do not refetch on mount
      refetchOnWindowFocus: false, // Do not refetch on window focus
    });
  
    return { data,isLoading, refetch,isFetching };
  }

  export function useChangeUserPennytots() {
    return {
      mutate: async (type: "reduce" | "increase") => {
        // Invalidate the credit query
        queryClient.invalidateQueries(['credit']);
        
        // Call your quiz function
        return await quiz.changeUserPennyTots(type);
      }
    };
  }

  export function useStakeUserPennytots() {
    return {
      mutate: async (type: "reduce" | "increase",amount:number) => {
        // Invalidate the credit query
        queryClient.invalidateQueries(['stake']);
        // Call your quiz function
        return await quiz.stakeUserPennyTots(type,amount);
      }
    };
  }


