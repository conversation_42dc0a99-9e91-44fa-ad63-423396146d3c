import { sponsoredAd } from 'app/api'; 
import { useQuery } from '@tanstack/react-query';

// This hook can be named and structured however you'd like
export function useGetSponsoredAd() {
  const { data, isLoading, isError, refetch } = useQuery(
    ['sponsoredAd'], 
    sponsoredAd.getSponsoredAd, 
    {
      // config options
      cacheTime: 0,
      staleTime: 0,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    }
  );

  
  // data structure from the API is { ad: { ...adData } }
  // you can decide to return data.ad or the entire data object
  return {
    adData: data?.ad, // or simply `data` if you prefer
    isLoading,
    isError,
    refetch,
  };
}
