import { topic } from 'app/api';
import { useMutation, useQuery } from '@tanstack/react-query';
import {queryClient} from "app/redux/user/hooks"
import { ICreateCommentDTO, ICreateSubCommentDTO } from './types';
import { AxiosResponse } from 'axios';
export function useTopics(p0?: { topicId: any; }) {
  return useQuery(['topics'], topic.getTopics, {
    staleTime: 1000 * 60 * 2, // 2 minutes - topics change frequently
    cacheTime: 1000 * 60 * 30, // 30 minutes cache
    refetchOnMount: false, // Don't refetch if data is fresh
    // Enable background refetch for topics since they're frequently updated
    refetchInterval: 1000 * 60 * 5, // Refetch every 5 minutes in background
  });
}

export function useSingleTopic(topicId: string) {
  return useQuery(['topic', topicId], () => topic.getSingleTopic(topicId), {
    staleTime: 1000 * 60 * 10, // 10 minutes - individual topics don't change as often
    cacheTime: 1000 * 60 * 60, // 1 hour cache
    enabled: !!topicId, // Only fetch if topicId exists
  });
}

// Hook for prefetching topic details
export function usePrefetchTopic() {
  return (topicId: string) => {
    queryClient.prefetchQuery(
      ['topic', topicId],
      () => topic.getSingleTopic(topicId),
      {
        staleTime: 1000 * 60 * 10, // 10 minutes
        cacheTime: 1000 * 60 * 60, // 1 hour
      }
    );
  };
}

export function useFetchTopic(userInfo: any) {
  return useQuery(['userTopics', userInfo], () => topic.fetchTopic(userInfo), {
    enabled: !!userInfo,
    staleTime: 1000 * 60 * 5, // 5 minutes - user topics change moderately
    cacheTime: 1000 * 60 * 30, // 30 minutes cache
    refetchOnMount: false,
  });
}
export function useDeleteTopic(userId: (state: any) => any) {
  return useMutation((topicId) => {
    if (typeof topicId !== 'string') {
      console.error('Invalid topicId:', topicId);
      throw new Error('Invalid topicId');
    }
    return topic.deleteTopic(topicId);
  });
}



export function useTopicComments(topicId: string) {
  return useQuery(['topic-comments', topicId], () =>
    topic.getTopicComments(topicId),
    {
      enabled: !!topicId,
      staleTime: 1000 * 60 * 5, // 5 minutes - comments change frequently
      cacheTime: 1000 * 60 * 30, // 30 minutes cache
    }
  );
}

// Hook for prefetching topic comments
export function usePrefetchTopicComments() {
  return (topicId: string) => {
    queryClient.prefetchQuery(
      ['topic-comments', topicId],
      () => topic.getTopicComments(topicId),
      {
        staleTime: 1000 * 60 * 5, // 5 minutes
        cacheTime: 1000 * 60 * 30, // 30 minutes
      }
    );
  };
}
type ConvosData = AxiosResponse<any>; // Update 'any' to your actual data type if known

export function useTopicSubComments(commentId: string) {
  const { data, isLoading: isQueryLoading } = useQuery(
    ['topic-sub-comments', commentId],
    () => topic.getTopicSubComments(commentId!),
    {
      enabled: !!commentId,
      staleTime: 1000 * 60 * 5, // 5 minutes - sub-comments change frequently
      cacheTime: 1000 * 60 * 30, // 30 minutes cache
    },
  );

  const isLoading = !!commentId && isQueryLoading;

  return { data, isLoading };
}

export function useConvos(options = {}) {
  return useQuery(['convos'], topic.getConvos, options);
}

export function useCreateComment() {
  return useMutation(
    (payload: ICreateCommentDTO) => {
      return topic.createComment(payload);
    },
    {
      onMutate: async (newComment) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries(['topic-comments', newComment.topicId]);

        // Snapshot the previous value
        const previousComments = queryClient.getQueryData(['topic-comments', newComment.topicId]);

        // Optimistically update to the new value
        queryClient.setQueryData(['topic-comments', newComment.topicId], (old: any) => {
          if (!old) return old;

          const optimisticComment = {
            _id: `temp-${Date.now()}`, // Temporary ID
            ...newComment,
            createdAt: new Date().toISOString(),
            pending: true, // Mark as pending
          };

          return [optimisticComment, ...old];
        });

        // Return a context object with the snapshotted value
        return { previousComments };
      },
      onError: (err, newComment, context) => {
        // If the mutation fails, use the context returned from onMutate to roll back
        if (context?.previousComments) {
          queryClient.setQueryData(['topic-comments', newComment.topicId], context.previousComments);
        }
      },
      onSuccess: (response: any, variables) => {
        // Update the optimistic comment with real data
        queryClient.setQueryData(['topic-comments', variables.topicId], (old: any) => {
          if (!old) return old;

          return old.map((comment: any) => {
            if (comment.pending && comment._id.startsWith('temp-')) {
              return { ...response.comment, pending: false };
            }
            return comment;
          });
        });
      },
      onSettled: (data, error, variables) => {
        // Always refetch after error or success to ensure consistency
        queryClient.invalidateQueries(['topic-comments', variables.topicId]);
      },
    },
  );
}



export function useCreateSubComment() {
  return useMutation((payload: ICreateSubCommentDTO) => {
    return topic.createSubComment(payload);
  });
}

// Optimistic like/unlike hook
export function useLikeTopic() {
  return useMutation(
    (topicId: string) => {
      return topic.likeTopic(topicId);
    },
    {
      onMutate: async (topicId) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries(['topics']);
        await queryClient.cancelQueries(['topic', topicId]);

        // Snapshot the previous values
        const previousTopics = queryClient.getQueryData(['topics']);
        const previousTopic = queryClient.getQueryData(['topic', topicId]);

        // Optimistically update topics list
        queryClient.setQueryData(['topics'], (old: any) => {
          if (!old) return old;

          return old.map((topic: any) => {
            if (topic._id === topicId) {
              const isLiked = topic.isLiked;
              return {
                ...topic,
                isLiked: !isLiked,
                likesCount: isLiked ? topic.likesCount - 1 : topic.likesCount + 1,
              };
            }
            return topic;
          });
        });

        // Optimistically update single topic
        queryClient.setQueryData(['topic', topicId], (old: any) => {
          if (!old) return old;

          const isLiked = old.isLiked;
          return {
            ...old,
            isLiked: !isLiked,
            likesCount: isLiked ? old.likesCount - 1 : old.likesCount + 1,
          };
        });

        return { previousTopics, previousTopic };
      },
      onError: (err, topicId, context) => {
        // Rollback on error
        if (context?.previousTopics) {
          queryClient.setQueryData(['topics'], context.previousTopics);
        }
        if (context?.previousTopic) {
          queryClient.setQueryData(['topic', topicId], context.previousTopic);
        }
      },
      onSettled: (data, error, topicId) => {
        // Refetch to ensure consistency
        queryClient.invalidateQueries(['topics']);
        queryClient.invalidateQueries(['topic', topicId]);
      },
    },
  );
}

export const useSearchTopics = (tabIndex: number,searchInput:string, setDataFunctions: any) => {
  
  // Map tabIndex to corresponding searchType
  const searchTypes = [
    'users',           // tabIndex 0
    'chats',           // tabIndex 1
    'groups',          // tabIndex 2
    'topics',          // tabIndex 3
    'topic-comments',  // tabIndex 4
    'topic-sub-comments', // tabIndex 5
  ];

  // Determine searchType using the tabIndex
  const searchType = searchTypes[tabIndex] || ''; // Fallback to an empty string if tabIndex is out of range

  const mutation = useMutation(
    (search: string) => topic.search({ search, searchType }), // Mutation function
    {
      onSuccess: (data) => {
        const {
          setUsers,
          setChats,
          setGroups,
          setData,
          setComments,
          setSubComments,
          setTotalResults,
        } = setDataFunctions;


        if (!searchInput.trim()) {
          // If the search input is empty, you can clear the relevant state
          switch (searchType) {
            case 'users':
              setUsers([]);
              setTotalResults(0);
              break;
            case 'chats':
              setChats([]);
              setTotalResults(0);
              break;
            case 'groups':
              setGroups([]);
              setTotalResults(0);
              break;
            case 'topics':
              setData([]);
              setTotalResults(0);
              break;
            case 'topic-comments':
              setComments([]);
              setTotalResults(0);
              break;
            case 'topic-sub-comments':
              setSubComments([]);
              setTotalResults(0);
              break;
            default:
              break;
          }
          return;
        }
        // Handle successful response
        switch (searchType) {
          case 'users':
            setUsers(data.users.docs);
            setTotalResults(data.users.totalDocs);
            break;
          case 'chats':
            setChats(data.chats);
            setTotalResults(data.chats.length);
            break;
          case 'groups':
            setGroups(data.groups.docs);
            setTotalResults(data.groups.totalDocs);
            break;
          case 'topics':
            setData(data.topics.docs);
            setTotalResults(data.topics.totalDocs);
            break;
          case 'topic-comments':
            setComments(data.topicComments.docs);
            setTotalResults(data.topicComments.totalDocs);
            break;
          case 'topic-sub-comments':
            setSubComments(data.topicComments.docs);
            setTotalResults(data.topicComments.totalDocs);
            break;
          default:
            break;
        }
      },
      onError: (error) => {
        console.error('Error fetching topics:', error);
      },
    }
  );

  return mutation;
};
