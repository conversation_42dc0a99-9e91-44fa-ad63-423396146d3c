import { clearUserAuth, setUser, updateUser } from "app/redux/user/reducer";
import { user } from "app/api";
import { store } from "app/redux";
import {
  IAccountSignInDTO,
  reportUserDTO,
  IAccountRegisterDTO,
  ChangePinParams,
} from "./types";
import { Axios } from "app/api/axios";
import { clearDeviceToken, setLoading } from "../main/reducer";
import { useMutation } from "@tanstack/react-query";
import { ShowAlert } from "app/providers/toast";
import { QueryClient } from "@tanstack/react-query";
import { showModal, hideModal } from "app/providers/modals";
import { err } from "react-native-svg";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      cacheTime: Infinity, //Always cache data
      // staleTime: ,
    },
  },
});

export const accountLogin = async ({
  phone_number,
  password,
}: IAccountSignInDTO) => {
  try {
    store.dispatch(setLoading(true));
    const response = await user.login({ phone_number, password });

    // Set Axios default headers with the token
    Axios.defaults.headers.common[
      "Authorization"
    ] = `Bearer ${response.data.token.token}`;

    // Set user in the store
    store.dispatch(
      setUser({
        userId: response.data._id,
        authInfo: response.data,
        isLoggedIn: true,
        token: response.data.token.token,
      })
    );

    console.log("I ran X 2");
  } catch (error: any) {
    console.log(error, "error");
    // Show error alert
    showModal({
      modalVisible: true,
      title: "Alert",
      message:
        error?.data?.message ||
        error?.message ||
        "Something Went Wrong Check Your Internet Connection and try again",
      setModalVisible: hideModal, // Function to close the modal
      type: "alert",
      handleConfirm: () => {
        console.log("Confirmed!");
        hideModal();
      },
      handleAlert: () => {
        console.log("Alert handled!");
        hideModal();
      },
    });
    // ShowAlert({
    //   type: 'error',
    //   className: 'Error',
    //   message: error?.data?.message || "Hello",
    // });
  } finally {
    // Ensure loading state is set to false
    store.dispatch(setLoading(false));
  }
};

export const accountRegister = async (payload: IAccountRegisterDTO) => {
  try {
    // Attempt to register the user
    store.dispatch(setLoading(true));
    const response = await user.register(payload);

    // Set authorization header with token
    Axios.defaults.headers.common[
      "Authorization"
    ] = `Bearer ${response.data.token.token}`;

    // Set user in the store
    store.dispatch(
      setUser({
        userId: response.data._id,
        authInfo: response.data,
        isLoggedIn: false,
        token: response.data.token.token,
      })
    );

    // Optionally handle notifications or other logic
    setTimeout(() => {
      // Wait a few seconds before setting notifications
      // useNotification();
    }, 1000);
    showModal({
      modalVisible: true,
      title: "Alert",
      message: "Updated successfully",
      setModalVisible: hideModal,
      type: "success-alert",
      handleConfirm: hideModal,
      handleAlert: hideModal,
    });
    return true;
  } catch (error: any) {
    console.log(error, "error");
    console.log("Error message from backend:", error?.data?.message); // Log the specific error message

    // Show error alert
    showModal({
      modalVisible: true,
      title: "Alert",
      message:
        error?.data?.message ||
        error?.message ||
        "Something Went Wrong Check Your Internet Connection and try again",

      setModalVisible: hideModal, // Function to close the modal
      type: "alert",
      handleConfirm: () => {
        console.log("Confirmed!");
        hideModal();
      },

      handleAlert: () => {
        console.log("Alert handled!");
        hideModal();
      },
    });
    return false;

    // ShowAlert({
    //   type: 'error',
    //   className: 'Error',
    //   message: error?.data?.message || "Hello",
    // });
  } finally {
    // Ensure loading state is set to false
    store.dispatch(setLoading(false));
  }

  //   return data; // Return data in case further processing is needed

  // }
  //  catch (error: unknown) {
  //   if (error instanceof Error) {
  //     return {
  //       error: error.message
  //     }

  //   }
  //    if (error && typeof error === 'object' &&  'Email already exist' in error) {
  //     showModal({
  //       modalVisible: true,
  //       title: 'Oops',
  //       message: 'email already in use',
  //       setModalVisible: hideModal, // Function to close the modal
  //       type: 'alert',
  //       handleConfirm: () => {
  //         console.log('Confirmed!');
  //         hideModal();
  //       },
  //       handleAlert: () => {
  //         console.log('Alert handled!');
  //         hideModal();
  //       },

  //     });

  //   }

  //   throw error;

  // } finally {
  //   // Always stop the loading state
  //   store.dispatch(setLoading(false));
  // }
};

export const updateUserInfo = async () => {
  const data = await user.profile();

  store.dispatch(updateUser(data));
};

export function useSendPhoneVerificationCode() {
  return useMutation(
    (payload: any) => {
      return user.sendPhoneVerification(payload);
    }
    // {
    //   onSuccess: (response, variables, context) => {
    //     // queryClient.invalidateQueries(['helpdesk-tickets']);
    //   },
    // },
  );
}

export function useValidatePhoneCode() {
  return useMutation(
    (payload: any) => {
      return user.validatePhoneNumber(payload);
    },
    {
      onSuccess: (response: any, variables, context) => {
        console.log(response, " data from response");
        store.dispatch(updateUser(response.user));
        showModal({
          modalVisible: true,
          title: "Alert",
          message: response.message,
          setModalVisible: hideModal, // Function to close the modal
          type: "success-alert",
          handleConfirm: () => {
            console.log("Confirmed!");
            hideModal();
          },
          handleAlert: () => {
            console.log("Alert handled!");
            hideModal();
          },
        });
        // ShowAlert({
        //   type: 'success',
        //   message: response.message,
        // });
      },
    }
  );
}

export function useSendEmailVerificationCode() {
  return useMutation(() => {
    return user.sendEmailVerification();
  });
}

export function useValidateEmailVerificationCode() {
  return useMutation(
    (payload: any) => {
      return user.validateEmailVerification(payload);
    },
    {
      onSuccess: (response: any, variables, context) => {
        console.log(response, " data from email verification response");
        store.dispatch(updateUser(response.user));
        showModal({
          modalVisible: true,
          title: "Success",
          message: response.message,
          setModalVisible: hideModal,
          type: "success-alert",
          handleConfirm: () => {
            console.log("Email verification confirmed!");
            hideModal();
          },
          handleAlert: () => {
            console.log("Email verification alert handled!");
            hideModal();
          },
        });
      },
    }
  );
}

export function useSendResetPasswordToken() {
  return useMutation(
    (payload: any) => {
      return user.sendResetPasswordToken(payload);
    },
    {
      onSuccess: (response: any, variables, context) => {
        showModal({
          modalVisible: true,
          title: "Alert",
          message: response.message,
          setModalVisible: hideModal, // Function to close the modal
          type: "success-alert",
          handleConfirm: () => {
            console.log("Confirmed!");
            hideModal();
          },
          handleAlert: () => {
            console.log("Alert handled!");
            hideModal();
          },
        });
        // ShowAlert({
        //   type: 'success',
        //   message: response.message,
        // });
      },
    }
  );
}

export function useReportUser() {
  return useMutation(
    (payload: reportUserDTO) => {
      return user.reportUser(payload.userId, payload.description);
    },
    {
      onSuccess: (response: any, variables, context) => {
        showModal({
          modalVisible: true,
          title: "Alert",
          message: response.message,
          setModalVisible: hideModal, // Function to close the modal
          type: "success-alert",
          handleConfirm: () => {
            console.log("Confirmed!");
            hideModal();
          },
          handleAlert: () => {
            console.log("Alert handled!");
            hideModal();
          },
        });
        // ShowAlert({
        //   type: 'success',
        //   message: response.message,
        // });
      },
    }
  );
}

export function useDeactivateAccount() {
  const mutation = useMutation(
    async () => {
      const response: any = user.deactivateAccount();

      return response.data;
    },
    {
      onSuccess: () => {
        // Perform logout after successful deactivation
        store.dispatch(clearUserAuth());
        store.dispatch(clearDeviceToken());
        setTimeout(() => {
          queryClient.invalidateQueries();
          delete Axios.defaults.headers.common["Authorization"];
        }, 1000);
      },
      onError: (error) => {
        console.error("Error deactivating account:", error);
      },
    }
  );

  return mutation;
}

export function useDeleteAccount() {
  const mutation = useMutation(
    async () => {
      const response: any = user.deleteAccount();

      return response.data;
    },
    {
      onSuccess: () => {
        // Perform logout after successful deactivation
        store.dispatch(clearUserAuth());
        store.dispatch(clearDeviceToken());
        setTimeout(() => {
          queryClient.invalidateQueries();
          delete Axios.defaults.headers.common["Authorization"];
        }, 1000);
      },
      onError: (error) => {
        console.error("Error deactivating account:", error);
      },
    }
  );

  return mutation;
}

export const logout = () => {
  //clear all saved cache for user
  store.dispatch(clearUserAuth()); // clear user auth from redux
  store.dispatch(clearDeviceToken()); //delete device token

  //wait for route switch before clearing cache to prevent api calls
  setTimeout(() => {
    queryClient.invalidateQueries();
    delete Axios.defaults.headers.common["Authorization"];
  }, 1000);
};

export function useChangePin() {
  return useMutation((params: ChangePinParams) => user.changeUserPin(params));
}
