import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  View,
  Text,
  Button,
  Animated,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import HeaderTitle from 'app/components/HeaderTitle';
import SpinWheel from 'app/components/SpinWheel';
import Ionicons from 'react-native-vector-icons/Ionicons';
import CustomModal from 'app/components/elements/Modals';
import { useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { BackHandler } from 'react-native';
import { CustomText } from 'app/components/elements';

import {
  useChangeUserPennytots,
  useStakeUserPennytots,
} from 'app/redux/quiz/hook';
import { queryClient } from 'app/redux/user/hooks';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import {
  useGetCredit,
  useGetCreditTransactionHistory,
} from 'app/redux/credit/hook';

const SpinWheelScreen = ({ navigation, route }) => {
  const { data: credits, refetch } = useGetCredit();
  const [modalVisible, setModalVisible] = useState(false);
  const [totalAmountWon, setTotalAmountWon] = useState(0);
  const [modalType, setModalType] = useState('');
  const { mutate: stakeUserPennytots } = useStakeUserPennytots();
  const { t } = useTranslation();
  const handleNavigateToCredit = async () => {
    stakeUserPennytots('increase', totalAmountWon);
    navigation.navigate('Credit'); // Ensure that 'gameWin' is passed correctly
  };

  const handleNavigateToSpinWheelIntro = async () => {
    navigation.navigate('IntroSpinWheel'); // Ensure that 'gameWin' is passed correctly
  };

  const handleSpinResult = (value) => {
    if (value === 0) {
      setModalType('lose-spin-the-wheel');
      setModalVisible(true);
    } else {
      setTotalAmountWon((prev) => prev + value);
    }
  };
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        setModalType('win-spin-the-wheel');
        setModalVisible(true);
        return true; // This will prevent the default back behavior
      };

      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress
      );

      return () => backHandler.remove();
    }, [setModalVisible]) // Dependencies can be adjusted based on your actual dependencies
  );

  const handleEndSpin = () => {
    setModalType('win-spin-the-wheel');
    setModalVisible(true);
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
      <View style={{
          flexDirection: 'row',
          justifyContent:'center',
        }}>
        <Image
          source={require('app/assets/logohorizontal.png')}
          style={{ width: 120, height: 48, marginBottom: rv(-15) }}
        />
      </View>

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'left',
          display: 'flex',

          marginVertical: 20,
        }}
      >
        <View
          style={{
            flex: 1,
            marginHorizontal: rv(10),
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <View>
            <TouchableOpacity
              style={{ display: 'flex', flexDirection: 'row' }}
              onPress={() => {
                handleEndSpin();
              }}
            >
              <Ionicons
                name='chevron-back'
                size={22}
                color='black'
                style={{ marginRight: 15, marginLeft: 15 }}
              />
              <CustomText style={{ fontSize: rv(12) }}>
                {t('end_spin_the_wheel')}
              </CustomText>
            </TouchableOpacity>
          </View>

          <View
            style={{
              flexDirection: 'row',
            }}
          >
            <Text
              style={{
                paddingRight: rv(3),
                fontSize: rv(12),
                fontWeight: '600',
                color: '#696969',
              }}
            >
              {credits?.amount}
            </Text>
            <Text
              style={{ fontSize: rv(12), fontWeight: '600', color: '#696969' }}
            >
              {t('pennytots')}
            </Text>
          </View>
        </View>
      </View>

      <SpinWheel
        onSpinResult={handleSpinResult}
        totalAmountWon={totalAmountWon}
      />
      {modalVisible && (
        <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          NavigateToCreditPage={handleNavigateToCredit}
          type={modalType}
          handleConfirm={
            modalType === 'win-spin-the-wheel'
              ? handleNavigateToCredit
              : handleNavigateToSpinWheelIntro
          }
          navigation={navigation}
        />
      )}
    </SafeAreaView>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     flex: 1, // Use flex in the parent container to cover the entire screen
//   },
//   centeredView: {
//     position: 'absolute', // Position the modal view absolutely
//     top: 0, // Align the top edge with the container
//     left: 0, // Align the left edge with the container
//     right: 0, // Align the right edge with the container
//     bottom: 0, // Align the bottom edge with the container
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
//   },
//   modalView: {
//     margin: 20,
//     backgroundColor: 'white',
//     borderRadius: 20,
//     padding: 35,
//     alignItems: 'center',
//     elevation: 5,
//   },
//   modalText: {
//     marginBottom: 15,
//     textAlign: 'center',
//   },
//   buttonContainer: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//   },
// });

export default SpinWheelScreen;
