import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  View,
  SafeAreaView,
  Text,
  Button,
  Animated,
  TouchableOpacity,
  Image,
  StyleSheet,
  ScrollView
} from 'react-native';
import HeaderTitle from 'app/components/HeaderTitle';
import SpinWheel from 'app/components/SpinWheel';
import { SpinWheelSVG } from 'app/providers/svg/loader';
import { useTranslation } from "react-i18next";
import { CustomText } from 'app/components/elements';

import { responsiveValue as rv } from 'app/providers/responsive-value';
import {
  useChangeUserPennytots,
  useStakeUserPennytots,
} from 'app/redux/quiz/hook';
import { queryClient } from 'app/redux/user/hooks';

const IntroSpinWheelScreen = ({ navigation, route }) => {
  const { mutate: stakeUserPennytots } = useStakeUserPennytots();
  const { t } = useTranslation();


  const handleNavigateToSpinWheel = async () => {
    stakeUserPennytots('reduce', 100);
    navigation.navigate('SpinWheel'); // Ensure that 'gameWin' is passed correctly
  };
  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: 'white', paddingHorizontal: 0 }}
    >
      <HeaderTitle title={t("back_to_credits")} navigation={navigation} />
      <ScrollView contentContainerStyle={{  flexGrow: 1, alignItems: 'center' }}>
        <View
          style={{
            flex: 1,
            backgroundColor: 'white',
            paddingHorizontal: 20,
            alignItems: 'center',

          }}
        >
          <CustomText
            style={{
              display: 'flex',
              textAlign: 'center',
              fontSize: 25,
              justifyContent: 'center',
              alignItems: 'center',
              fontFamily: 'semiBold',
              color: '#797978',
              marginBottom: rv(20)
            }}
          >
            {t("spin_the_wheel")}
          </CustomText>
          <SpinWheelSVG width={rv(250)} height={rv(250)}/>

          <CustomText
            style={{
              textAlign: 'center',
              fontSize: rv(13),
              fontFamily: 'regular',
              marginTop: rv(40),
              color: '#696969',
            }}
          >
           {t("with_just_100_pennytots_spin_f")}
          </CustomText>
          <CustomText
            style={{
              marginTop: rv(15),
              textAlign: 'center',
              fontSize: rv(13),
              color: '#696969',
              fontFamily: 'regular',
            }}
          >
            {t("however_you_lose_the_staked_10")}
          </CustomText>
          <CustomText
            style={{
              marginTop: rv(30),
              textAlign: 'center',
              fontSize: rv(13),
              fontFamily: 'regular',
              color: '#696969',
            }}
          >
           {t("are_you_ready_to_play")}
          </CustomText>

          <TouchableOpacity
            onPress={handleNavigateToSpinWheel}
            style={{ backgroundColor: "#FED830",marginTop: rv(32), width: rv(260),display: "flex",borderRadius: rv(32), }}
          >
            <View
            style={{
              borderRadius: rv(32),
              width: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
             <CustomText
                style={{
                  display: "flex",
                  textAlign: "center",
                  width: '100%',
                  borderRadius: rv(32),
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingVertical: rv(10),
                  width: "100%",
                  fontSize: rv(14),
                  color: '#48463E',
                  lineHeight: rv(24),
                  fontFamily: 'regular',
                }}
              >
               {t("play")}
              </CustomText>

          </View>
          
             
        
          </TouchableOpacity>
        </View>
      </ScrollView>
      
    </SafeAreaView>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     flex: 1, // Use flex in the parent container to cover the entire screen
//   },
//   centeredView: {
//     position: 'absolute', // Position the modal view absolutely
//     top: 0, // Align the top edge with the container
//     left: 0, // Align the left edge with the container
//     right: 0, // Align the right edge with the container
//     bottom: 0, // Align the bottom edge with the container
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
//   },
//   modalView: {
//     margin: 20,
//     backgroundColor: 'white',
//     borderRadius: 20,
//     padding: 35,
//     alignItems: 'center',
//     elevation: 5,
//   },
//   modalText: {
//     marginBottom: 15,
//     textAlign: 'center',
//   },
//   buttonContainer: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//   },
// });

export default IntroSpinWheelScreen;
