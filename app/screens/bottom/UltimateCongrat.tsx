import React from 'react';
import * as Animatable from 'react-native-animatable';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  Platform,
  
  ScrollView,
  TouchableOpacity
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { CustomText } from 'app/components/elements';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useNavigation } from '@react-navigation/native';
import { Modal } from 'react-native-paper';
import {
  useGetHelpDeskCategories,
  useCreateTicket,
} from 'app/redux/helpdesk/hooks';
import CustomModal from 'app/components/elements/Modals';

const UltimateCongrat = () => {
  const { t } = useTranslation()
  const navigation = useNavigation();
  const [formData, setFormData] = React.useState({
    email: '',
    
  });
  const { mutateAsync: createTicket, isLoading } = useCreateTicket();
  const [modalVisible, setModalVisible] = React.useState(false);
  const [type,setType] = React.useState<any>("alert")

  const handleChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = () => {
    console.log("Entered here")
    if (formData.email) {
      // Handle form submission with formData
      setType('win-game')
      setModalVisible(true);
      const payload = {
        title: 'Cash Out',
         message: `email: ${formData.email}`,
        category: 'Payment',
      };
      createTicket(payload);
      
    } else {
      // If any field is missing, show an error message or handle it accordingly
      setType("alert")
    }
    setModalVisible(true);
  };

  const handleNavigateToGameMode = () => {
    // You can do some validation here before navigating

    navigation.navigate('GameMode' as never); // Passing the 'text' parameter
  };

  const handleNavigateToQuizScreen = () => {
    // You can do some validation here before navigating

    navigation.navigate('QuizScreen' as never); // Passing the 'text' parameter
  };
  const handleCancel = () => {
    setModalVisible(false);
  };

  

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white', paddingHorizontal: rv(20) }}>
      
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ display: 'flex', backgroundColor: '#fff', }}
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1,}}>
      {modalVisible && (
      <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type={type} // or "stake" based on the scenario
          message="Please fill in all the input fields"
          handleConfirm = {handleNavigateToQuizScreen}
          navigation={navigation}
        />)}
        <View
          style={{
         
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            // marginTop: rv(32),
          }}
        >
          {
            <Image
              source={require('app/assets/logohorizontal.png')}
              style={{ width: rv(120), height: rv(48) }}
            />
          }
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Image
              source={require('app/assets/Pennytots_gold.png')}
              style={{  width: rv(240), height: rv(240) }}

            />
          </View>
          <CustomText
            style={{
              fontSize: rv(27),
              fontFamily: 'openSansBold',
              color: '#48463E'
            }}
          >
           {t("congratulations")}
          </CustomText>
        </View>
        <CustomText
          style={{
            fontSize: rv(14),
            fontFamily: 'medium',
            marginTop: rv(20),
            textAlign: 'center',
            marginBottom: 0,
            color: '#FFB61D'
          }}
        >
         {t("you_just_won_a_24karat_1_ounce")}
        </CustomText>
        <Text
          style={{
            fontSize: rv(12.5),
            color: '#696969',
            fontFamily: 'medium',
            marginTop: rv(20),
            textAlign: 'center',
            alignSelf: 'center'
          }}
        >
          {t("enter_email")}
        </Text>
        <View style={{ marginTop: rv(20),paddingHorizontal: rv(10)}}>
          <Text
            style={{
              textAlign: 'left',
              fontSize: rv(13),
              fontFamily: 'regular',
              color: '#696969'
            }}
          >
          {t("email")}
          </Text>
          <TextInput
            onChangeText={(text) => handleChange('email', text)}
            value={formData.email}
            style={styles.input}
            placeholder='<EMAIL>'
          />

          
        </View>
        <TouchableOpacity
          style={{
            display: 'flex',
            backgroundColor: '#FED830',
            height: rv(48),
            paddingVertical: 8,
            paddingHorizontal: rv(12),
            justifyContent: 'center', // Center the content vertically
            alignItems: 'center',
            borderRadius: rv(32),
            marginTop: rv(26),
          }
          }
          onPress={handleSubmit}
        >
          <CustomText
            style={{
              color: '#48463E',
              fontSize: rv(16),
              fontWeight: 'bold',
              lineHeight: rv(24),
              fontFamily: 'regular',
            }}
          >
            {t("send")}
          </CustomText>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
    
    </SafeAreaView> 
  );
};

const styles = StyleSheet.create({
  modalContent: {
    width: '85%',
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
  },
  closeButton: {
    width: 30,
    height: 30,
    marginLeft: 110,
  },
  completeButton: {
    width: 40,
    height: 40,
  },
  modalView: {
    width: '50%',
    marginLeft: 130,
    display: 'flex',
    flexDirection: 'row',
  },
  input: {
    marginTop: rv(8),
    backgroundColor: '#F4F4F4',
    width: '100%',
    height: rv(40),
    color: '#8F8F8F',
    fontWeight: '500',
    fontSize: rv(12),
    fontFamily: 'medium',
    borderRadius: 8,
    paddingHorizontal: 24,
  },

  centeredView: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // centeredView: {
  //   position: 'absolute',
  //   top: 0,
  //   left: 0,
  //   right: 0,
  //   bottom: 0,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
  // },

  modalViews: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 15,
    alignItems: 'center',
  },

  modalText: {
    fontWeight: '600',
    color: '#4F4F4F',
    fontSize: rv(13),
    fontFamily: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  modalTexts: {
    textAlign: 'center',
    fontWeight: '600',
    fontFamily: 'regular',
    color: '#4F4F4F',
    fontSize: rv(12),
  },
});

export default UltimateCongrat;
