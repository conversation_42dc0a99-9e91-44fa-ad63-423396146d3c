import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ScrollView,
  Modal,
  Button,
} from "react-native";
import { useTranslation } from "react-i18next";
import { CustomText } from "app/components/elements";
import { SafeAreaView } from "react-native-safe-area-context";
import { useGetCredit } from "app/redux/credit/hook";
import { processFontFamily } from "expo-font";
import useFonts from "app/hooks/useFont.js"; // Corrected import name
import { useNavigation } from "@react-navigation/native";
import { responsiveValue as rv } from "app/providers/responsive-value";
import CustomModal from "app/components/elements/Modals";
import Ionicons from "@expo/vector-icons/Ionicons";
import UltimateChallenge from "./ultimateChallenge";
import {
  BulbSVG,
  HelpdeskSVG,
  NextArrowSVG,
  GameModeSVG,
  BuyCreditSVG,
} from "app/providers/svg/loader";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { useTheme, createThemedStyles } from 'app/theme';

interface ChallengeModeProps {
  onChangeText: (text: string) => void;
  navigation: any; // Adjust the type according to your navigation prop type
  // Define the type of the 'onChangeText' prop
}

// Presentation component props
interface ChallengeModePresentationProps {
  theme: any;
  styles: any;
  credits: any;
  modalVisible: boolean;
  stake: number;
  t: any;
  handleIncrement: () => void;
  handleDecrement: () => void;
  handleNavigateToGameMode: () => void;
  handleNavigateToChallengeMode: () => void;
  handleNavigateToUltimateChallenge: () => void;
  handleEndChallenge: () => void;
  handleNavigateToCreditScreen: () => void;
  setModalVisible: (visible: boolean) => void;
  navigation: any;
}

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    paddingHorizontal: rv(6),
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: theme.colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    alignItems: "center",
  },
  mainContainer: {
    flexDirection: "column",
    justifyContent: "space-between",
    width: "90%",
    marginTop: rv(10),
  },
}));

// Container Component (handles logic, state, theme)
const ChallengeModeContainer: React.FC<ChallengeModeProps> = ({ route }: any) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const { data: credits } = useGetCredit();
  const navigation = useNavigation(); // Get navigation object
  const [modalVisible, setModalVisible] = useState(false);
  const [stake, setStake] = React.useState(500);
  const { t } = useTranslation();

  const handleIncrement = () => {
    if (stake < 5000) {
      // Check if stake is less than the maximum value
      setStake(stake + 500);
    }
  };

  const handleDecrement = () => {
    if (stake > 500) {
      // Check if stake is greater than the minimum value
      setStake(stake - 500);
    }
  };

  const handleNavigateToUltimateChallenge = () => {
    navigation.navigate("UltimateChallenge" as never);
  };

  const handleNavigateToGameMode = () => {
    // You can do some validation here before navigating
    if (credits?.amount < stake) {
      // If not enough credits, show the modal and do not navigate
      setModalVisible(true);
    } else {
      navigation.navigate("GameMode", { text: stake }); // Passing the 'text' parameter
    }
  };

  const handleNavigateToChallengeMode = () => {
    // You can do some validation here before navigating
    navigation.navigate("QuizScreen" as never); // Passing the 'text' parameter
  };

  const handleNavigateToCreditScreen = () => {
    // You can do some validation here before navigating
    setModalVisible(false);
    navigation.navigate("Credit" as never); // Passing the 'text' parameter
  };

  const handleEndChallenge = () => {
    navigation.navigate("QuizScreen" as never);
  };

  return (
    <ChallengeModePresentation
      theme={theme}
      styles={styles}
      credits={credits}
      modalVisible={modalVisible}
      stake={stake}
      t={t}
      handleIncrement={handleIncrement}
      handleDecrement={handleDecrement}
      handleNavigateToGameMode={handleNavigateToGameMode}
      handleNavigateToChallengeMode={handleNavigateToChallengeMode}
      handleNavigateToUltimateChallenge={handleNavigateToUltimateChallenge}
      handleEndChallenge={handleEndChallenge}
      handleNavigateToCreditScreen={handleNavigateToCreditScreen}
      setModalVisible={setModalVisible}
      navigation={navigation}
    />
  );
};
// Presentational Component (pure UI)
const ChallengeModePresentation = ({
  theme,
  styles,
  credits,
  modalVisible,
  stake,
  t,
  handleIncrement,
  handleDecrement,
  handleNavigateToGameMode,
  handleNavigateToChallengeMode,
  handleNavigateToUltimateChallenge,
  handleEndChallenge,
  handleNavigateToCreditScreen,
  setModalVisible,
  navigation,
}: ChallengeModePresentationProps) => {
  return (
    <SafeAreaView style={styles.container}>
      {modalVisible && (
        <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type="credit"
          handleConfirm={handleEndChallenge}
          NavigateToCreditPage={handleNavigateToCreditScreen}
          navigation={navigation}
        />
      )}
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.mainContainer}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <TouchableOpacity
              style={{ flexDirection: "row", alignItems: "center" }}
              onPress={handleNavigateToChallengeMode}
            >
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Ionicons name="chevron-back" size={22} color={theme.colors.text} />
                <CustomText
                  style={{
                    fontSize: rv(13),
                    fontFamily: "semiBold",
                    fontWeight: "100",
                    paddingLeft: rv(5.5),
                    color: theme.colors.textSecondary,
                  }}
                >
                  {t("back_to_game_mode")}
                </CustomText>
              </View>
            </TouchableOpacity>

            <View style={{ flexDirection: "row" }}>
              <Text
                style={{
                  color: theme.colors.textSecondary,
                  fontSize: rv(13),
                  fontFamily: "semiBold",
                  fontWeight: "100",
                  paddingLeft: rv(5.5),
                }}
              >
                {credits?.amount}
              </Text>
              <Text
                style={{
                  color: theme.colors.textSecondary,
                  fontSize: rv(13),
                  fontFamily: "semiBold",
                  fontWeight: "100",
                  paddingLeft: rv(5.5),
                }}
              >
                {t("pennytots")}
              </Text>
            </View>
          </View>

          <View style={{ alignItems: "center" }}>
            <CustomText
              style={{
                marginTop: rv(22),
                fontFamily: "semiBold",
                fontSize: rv(20),
                color: theme.colors.info,
              }}
            >
              {t("double_up")}
            </CustomText>
            <Image
              style={{ marginTop: rv(5), width: rv(160), height: rv(160) }}
              source={require(`app/assets/Pennytots_gold.png`)}
            />
          </View>
        </View>
        <CustomText
          style={{
            fontSize: rv(12),
            fontFamily: "medium",
            width: "85%",
            marginTop: rv(5),
            alignItems: "center",
            textAlign: "center",
            color: theme.colors.textSecondary,
          }}
        >
          {t("build_up_your_credit_here_for_")}
        </CustomText>
        <CustomText
          style={{
            fontFamily: "medium",
            fontSize: rv(12),
            alignItems: "center",
            textAlign: "center",
            marginTop: rv(12),
            color: "#FFB61D",
          }}
        >
          {t("24karat_1_ounce_custommade_pur")}
        </CustomText>
        <CustomText
          style={{
            fontSize: rv(12),
            marginTop: rv(12),
            fontFamily: "medium",
            width: "85%",
            alignItems: "center",
            textAlign: "center",
            color: theme.colors.textSecondary,
          }}
        >
         {t("answer_5_questions_correctly_b")}
        </CustomText>
        <CustomText
          style={{
            fontSize: rv(12),
            fontFamily: "medium",
            marginTop: rv(27),
            textAlign: "center",
            color: theme.colors.textSecondary,
          }}
        >
          {t("you_need_to_have_at_least_500_")}
        </CustomText>
        <View style={{ marginTop: rv(26) }}>
          <Text style={{
            textAlign: "center",
            fontSize: rv(14),
            color: theme.colors.info,
            fontFamily: "semiBold",
          }}>Stake</Text>
          <View style={{
            marginTop: rv(8),
            height: rv(30),
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
          }}>
            <TouchableOpacity
              onPress={handleDecrement}
              disabled={stake <= 500}
              style={[{
                backgroundColor: "#FFC085",
                borderWidth: 1,
                borderColor: "transparent",
                borderRadius: rv(8),
                display: "flex",
                height: "100%",
                width: rv(30),
                fontSize: rv(16),
                marginRight: rv(8),
                alignItems: "center",
                justifyContent: "center",
              }, stake <= 500 && { opacity: 0.5 }]}
            >
              <Text>-</Text>
            </TouchableOpacity>
            <TextInput
              style={{
                textAlign: "center",
                paddingVertical: rv(5),
                width: rv(115),
                borderRadius: 1.5,
                backgroundColor: "rgba(254, 216, 48, 0.13)",
                fontFamily: "medium",
                fontSize: rv(14),
                color: theme.colors.info,
                fontWeight: "500",
              }}
              onChangeText={(text) => setStake(Number(text))}
              value={String(stake)}
              editable={false}
              placeholder=""
              keyboardType="numeric"
            />
            <TouchableOpacity
              onPress={handleIncrement}
              disabled={stake >= 5000}
              style={[{
                backgroundColor: "#FFC085",
                borderWidth: 1,
                borderColor: "transparent",
                borderRadius: rv(8),
                display: "flex",
                height: "100%",
                width: rv(30),
                fontSize: rv(16),
                alignItems: "center",
                justifyContent: "center",
                marginLeft: rv(8),
              }, stake >= 5000 && { opacity: 0.5 }]}
            >
              <Text>+</Text>
            </TouchableOpacity>
          </View>
        </View>
        <TouchableOpacity
          style={{
            backgroundColor: theme.colors.accent,
            width: "85%",
            height: rv(48),
            paddingVertical: 8,
            paddingHorizontal: rv(12),
            justifyContent: "center",
            alignItems: "center",
            borderRadius: rv(32),
            marginTop: rv(41),
          }}
          onPress={handleNavigateToGameMode}
        >
          <CustomText style={{
            color: "#48463E",
            fontSize: rv(16),
            fontWeight: "bold",
            lineHeight: rv(24),
            fontFamily: "semiBold",
            marginBottom: 0,
          }}>{t("play")}</CustomText>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

// Export container as default
export default ChallengeModeContainer;
