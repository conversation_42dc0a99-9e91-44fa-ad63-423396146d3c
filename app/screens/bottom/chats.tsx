import React, { useEffect } from 'react';
import {
  Text,
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import ChatTextSVG from '@/app/assets/svg/Chats_text.svg';
import BlankChatSVG from '@/app/assets/svg/empty-pages.svg';
import useMyChat from 'app/redux/chat/hooks';
import { ChatCard } from 'app/components/cards/chats';
import { useTheme, createOptimizedThemedStyles, FONT_SIZES } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

const Loading = require('../../assets/loading.gif');

// Presentation component props
interface ChatPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  data: any[];
  isFetching: boolean;
  onRefresh: () => void;
}

// Create themed styles with optimized font sizes
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  emptyContainer: {
    alignSelf: 'center',
    marginTop: '40%',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
}));

// Container Component (handles logic, state, theme)
const ChatContainer = ({ navigation }: any) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const { data, isFetching, refetch, isLoading } = useMyChat();

  // Log data when updated (optional)
  useEffect(() => {
    console.log('Chat data:', data);
  }, [data]);

  // Remove automatic refetch on focus since we have background refetching
  // This reduces unnecessary network calls and improves performance
  // useFocusEffect(
  //   React.useCallback(() => {
  //     refetch();
  //   }, [refetch])
  // );

  const onRefresh = async () => {
    await refetch();
  };

  return (
    <ChatPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      data={data}
      isFetching={isFetching}
      onRefresh={onRefresh}
    />
  );
};

// Presentational Component (pure UI)
const ChatPresentation = ({
  theme,
  styles,
  navigation,
  data,
  isFetching,
  onRefresh,
}: ChatPresentationProps) => {
  return (
    <View style={styles.container}>
      {/* Render the FlatList with the chat data */}
      <FlatList
        data={data}
        keyExtractor={(item) => item._id}
        refreshing={isFetching}
        onRefresh={onRefresh}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <BlankChatSVG style={{ alignSelf: 'center' }} />
            <ChatTextSVG style={{ marginTop: rv(20) }} />
          </View>
        )}
        renderItem={({ item }) => (
          <ChatCard item={item} navigation={navigation} refreshFunction={onRefresh} />
        )}
      />
    </View>
  );
};

// Export container as default
export default ChatContainer;
