import { View, TouchableOpacity, Dimensions } from 'react-native';
import React, { FunctionComponent, useEffect, useState } from 'react';
import ConvoNotificationIcon from 'app/components/convo-icon';
import { useGetUnreadNotifications } from 'app/redux/main/hooks';
import { useIsFocused } from '@react-navigation/native';
import { useTheme, createOptimizedThemedStyles, FONT_SIZES } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';


type HeaderTitleProps = {
  navigation: any;
};

// Container Component
const ConvoNotificationContainer: FunctionComponent<HeaderTitleProps> = ({
  navigation,
}) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const isVisible = useIsFocused();
  const { data, isFetching } = useGetUnreadNotifications(isVisible);

  return (
    <ConvoNotificationPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      count={data?.convos.length}
    />
  );
};

// Presentation Component
interface ConvoNotificationPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  count: number;
}

const ConvoNotificationPresentation: React.FC<ConvoNotificationPresentationProps> = ({
  theme,
  styles,
  navigation,
  count,
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={() => navigation.navigate('convos', { count })}
        style={styles.touchable}
      >
        <ConvoNotificationIcon count={count} />
      </TouchableOpacity>
    </View>
  );
};

// Themed Styles with optimized font sizes
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    backgroundColor: theme.colors.background,
  },
  touchable: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
}));

export default ConvoNotificationContainer;
