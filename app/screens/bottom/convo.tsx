import { useFocusEffect } from '@react-navigation/native';
import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  StatusBar,
  FlatList,
  TouchableOpacity,

  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TopicCard } from '../../components/cards/topics';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import ConvoNotificationIcon from 'app/components/convo-icon';
import { ITopic } from 'app/redux/topic/types';
import { useConvos } from 'app/redux/topic/hooks';
import { useGetUnreadNotifications } from 'app/redux/main/hooks';
import { main } from 'app/api/main';
import Ionicons from '@expo/vector-icons/Ionicons';
import { CustomText } from 'app/components/elements';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme, createOptimizedThemedStyles, FONT_SIZES } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';


const Loading = require('../../assets/loading.gif');

interface IConvoScreen {
  navigation: any;
  route: any;
}

interface IConvos {
  _id: string;
  topicId: ITopic;
}

// Container Component
const ConvoScreenContainer: React.FC<IConvoScreen> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const { t } = useTranslation();
  const { data, isFetching, refetch, isLoading } = useConvos();
  const { data: unread } = useGetUnreadNotifications(false);
  const { count = 0 } = route.params || {};

  useEffect(() => {
    if (unread && unread.convos.length > 0) {
      main.clearUnReadNotifications({ source: 'convos' }).then((response) => {
        console.log(response);
      });
    }
  }, [unread, data]);

  return (
    <ConvoScreenPresentation
      theme={theme}
      styles={styles}
      data={data}
      isLoading={isLoading}
      isFetching={isFetching}
      refetch={refetch}
      navigation={navigation}
      t={t}
    />
  );
};

// Presentation Component
interface ConvoScreenPresentationProps {
  theme: any;
  styles: any;
  data: any;
  isLoading: boolean;
  isFetching: boolean;
  refetch: () => void;
  navigation: any;
  t: any;
}

const ConvoScreenPresentation: React.FC<ConvoScreenPresentationProps> = ({
  theme,
  styles,
  data,
  isLoading,
  isFetching,
  refetch,
  navigation,
  t,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
        >
          <Ionicons
            name='chevron-back'
            size={22}
            color={theme.colors.text}
            style={styles.backIcon}
          />
        </TouchableOpacity>
        <CustomText style={styles.headerText}>
          {t("Convos")}
        </CustomText>
      </View>
      {isLoading && (
        <View style={styles.loadingContainer}>
          <Image source={Loading} style={styles.loadingImage} />
        </View>
      )}
      {!isLoading && (
        <FlatList
          data={data}
          keyExtractor={(item: IConvos) => item._id}
          refreshing={isFetching}
          onRefresh={() => refetch()}
          renderItem={({ item }) => (
            <TopicCard
              item={item.topicId}
              navigation={navigation}
              convo={true}
              refreshFunction={() => refetch()}
            />
          )}
          contentContainerStyle={styles.flatListContent}
        />
      )}
    </SafeAreaView>
  );
};

// Themed Styles with optimized font sizes
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flexDirection: 'column',
    backgroundColor: theme.colors.background,
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  backButton: {
    marginLeft: 15,
    padding: 10,
  },
  backIcon: {
    marginLeft: 5,
  },
  headerText: {
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    marginRight: 3,
    paddingTop: 3,
    color: theme.colors.text,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingImage: {
    width: rv(50),
    height: rv(50),
  },
  flatListContent: {
    paddingBottom: 20,
  },
}));

export default ConvoScreenContainer;
