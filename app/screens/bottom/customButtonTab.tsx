import React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { responsiveValue as rv } from "app/providers/responsive-value";
import { useNavigation } from "@react-navigation/native";
import { CustomText } from "app/components/elements";
import { useTranslation } from "react-i18next";
import {
  BulbSVG,
  HelpdeskSVG,
  NextArrowSVG,
  GameModeSVG,
  BuyCreditSVG,
  SettingSVG,
} from "app/providers/svg/loader";
import { SafeAreaView } from "react-native-safe-area-context";
import { createOptimizedThemedStyles, createScalableThemedStyles } from "app/theme";
import { useTheme } from 'app/hooks/useTheme';
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";

const CustomBottomTab = (props: any) => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const handleNavigateToCreditScreen = () => {
    // You can do some validation here before navigating

    navigation.navigate("Credit" as never); // Passing the 'text' parameter
  };
  const handleNavigateToHelpDesk = () => {
    // You can do some validation here before navigating

    navigation.navigate("Helpdesk" as never); // Passing the 'text' parameter
  };
  const handleNavigateToSettingsStackNavigator = () => {
    // You can do some validation here before navigating

    navigation.navigate("SettingsStackNavigator" as never); // Passing the 'text' parameter
  };
  return (
    <SafeAreaView
      style={{
        justifyContent: "center",
        display: "flex",
        flexDirection: "row",
        backgroundColor: "#fff",
        width: "100%",
      }}
    >
      <View
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          width: "95%",
          marginBottom: 36,
        }}
      >
        <TouchableOpacity
          {...props}
          onPress={handleNavigateToHelpDesk}
          style={{
            display: "flex",
            flexWrap: "wrap",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-end",
          }}
        >
          <HelpdeskSVG
            width={rv(18)}
            height={rv(20)}
            style={{ marginRight: rv(10) }}
          />
          <CustomText
            style={styles.helpDesk}
          >
            {t("HelpDesk_")}
          </CustomText>
        </TouchableOpacity>
        <Image
          style={{ width: rv(1), height: rv(47) }}
          source={require(`app/assets/Rectangleline.png`)}
        />
        <TouchableOpacity
          {...props}
          onPress={handleNavigateToSettingsStackNavigator}
          style={{
            display: "flex",
            flexWrap: "wrap",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          <SettingSVG
            width={rv(20)}
            height={rv(15)}
            style={{ marginLeft: rv(20) }}
          />
          <CustomText
            style={styles.settings}
          >
            {t("settings")}
          </CustomText>
        </TouchableOpacity>
        <Image
          style={{ width: rv(1), height: rv(47) }}
          source={require(`app/assets/Rectangleline.png`)}
        />
        <TouchableOpacity
          {...props}
          onPress={handleNavigateToCreditScreen}
          style={{
            display: "flex",
            flexWrap: "wrap",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          <BuyCreditSVG
            width={rv(20)}
            height={rv(15)}
            style={{ marginLeft: rv(20) }}
          />
          <CustomText style={styles.credit}
          >
            {t("credit")}
          </CustomText>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  settings:{
    marginRight: rv(20),
    fontWeight: "400",
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
  },
  credit: {
    marginLeft: rv(10),
    fontWeight: "400",
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
  },
  helpDesk:{
    marginRight: rv(20),
    fontWeight: "400",
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    flexWrap: "wrap",
  }
}));

export default CustomBottomTab;
