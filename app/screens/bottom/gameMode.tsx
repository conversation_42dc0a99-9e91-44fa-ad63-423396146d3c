import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  View,
  Text,
  Button,
  TouchableOpacity,
  Image,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';
import { useTranslation } from "react-i18next";
import { CustomText } from 'app/components/elements';
import axios from 'axios';
import * as Animatable from 'react-native-animatable';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  useGetCredit,
  useGetCreditTransactionHistory,
} from 'app/redux/credit/hook';
import { SafeAreaView } from 'react-native-safe-area-context';
import FloatingAddSVG from 'app/assets/svg/next.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Animation from 'app/components/animation';
import {
  useGetQuestions,
  useChangeUserPennytots,
  useStakeUserPennytots,
} from 'app/redux/quiz/hook';
import { AppState } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { BackHandler } from 'react-native';
import CustomModal from 'app/components/elements/Modals';
import {
  BulbSVG,
  HelpdeskSVG,
  NextArrowSVG,
  GameModeSVG,
  BuyCreditSVG,
} from 'app/providers/svg/loader';
import { G } from 'react-native-svg';
import { useTheme, createThemedStyles } from 'app/theme';
// import { Date } from "expo-contacts";

const Correct = require('../../assets/gifs/correct1.gif');
const Wrong1 = require('app/assets/gifs/times-up.gif');
const Wrong2 = require('app/assets/gifs/wrong2.gif');
const Wrong3 = require('../../assets/gifs/angry-furious.gif');
const Wrong4 = require('../../assets/gifs/bad-hair.gif');
const Wrong5 = require('../../assets/gifs/bernie-mac-nervous.gif');
const Wrong6 = require('../../assets/gifs/well-damn.gif');
const Wrong7 = require('../../assets/gifs/martin.gif');
const Wrong8 = require('../../assets/gifs/no-chris-rock.gif');
const Wrong9 = require('../../assets/gifs/no-machaizelli-kahey.gif');
const Wrong10 = require('../../assets/gifs/wow-amazing.gif');
const Wrong11 = require('../../assets/gifs/no-nope-nope-nope.gif');
const Wrong12 = require('../../assets/gifs/no-richard-lane.gif');
const Wrong13 = require('../../assets/gifs/nope-kenan-thompson.gif');
const Wrong14 = require('../../assets/gifs/nope-terry-jeffords.gif');
const Wrong15 = require('../../assets/gifs/oh-come-on-captain-ray-holt.gif');
const Wrong16 = require('../../assets/gifs/oh-no-jeremy.gif');
const Wrong17 = require('../../assets/gifs/orange-is-the-new-black-oitnb.gif');

const RandomImageAnimation = ({ images }: { images: any[] }) => {
  // If 'images' prop is falsy or empty, return null to not render anything
  if (!images || images.length === 0) {
    return null;
  }

  // Select a random image from the array
  const randomIndex = Math.floor(Math.random() * images.length);
  const selectedImage = images[randomIndex];
};

const mockQuizData = {
  question: 'Who is known as the pioneer of Afrobeat music in Nigeria??',
  options: ['Ebenezer Obey', 'Fela Kuti', 'Ebenezer Obey', 'Ebenezer Obey'],
  correctAnswer: 'Fela Kuti',
};

const OptionButton = ({
  option,
  onPress,
  isCorrect,
  isSelected,
  correctAnswer, // New prop to control when to show the correct answer
  gameOver,
  style,
}: any) => {
  const colors = [
    {
      background: '#FFC085',
      backgroundColor: '#FED830',
    },
    {
      background: '#FFFFFF',
      backgroundColor: '#000000',
    },
    // Add more color combinations here
  ];

  const [currentColor, setCurrentColor] = useState(colors[0]);
  let randomIndex = 0;
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (randomIndex === 0) {
        randomIndex = 1;
      } else {
        randomIndex = 0;
      }
      setCurrentColor(colors[randomIndex]);
    }, 500);

    return () => clearInterval(intervalId);
  }, [randomIndex]);
  return (
    <TouchableOpacity
      onPress={() => {
        // Call the onPress function provided in the props
        onPress();
      }}
      style={{
        backgroundColor:
          isCorrect && isSelected // Show correct answer color if the option is correct and either selected or showAnswer is true
            ? currentColor.background
            : isSelected
            ? '#48463E'
            : isCorrect !== null && option === correctAnswer
            ? currentColor.background
            : '#FED830',
        height: rv(50),
        marginTop: rv(15),
        borderRadius: rv(24),
        padding: rv(5),
        paddingHorizontal: rv(30),
        alignItems: 'center',
        justifyContent: 'center',
        margin: rv(3),
        marginRight: rv(4),
        opacity:
          isCorrect === null
            ? 1
            : isSelected
            ? 1
            : isCorrect !== null && option === correctAnswer
            ? 1
            : 0.5,
        ...style,
      }}
      disabled={isCorrect !== null || gameOver}
    >
      <Text
        style={{
          color:
            isCorrect && isSelected
              ? '#48463E'
              : isSelected
              ? 'white'
              : '#48463E',
          textAlign: 'center',
          fontSize: rv(11),
          fontWeight: '600',
        }}
      >
        {option}
      </Text>
    </TouchableOpacity>
  );
};

const GameMode = () => {
  const { t } = useTranslation();
  const [optionSelected, setOptionSelected] = useState(false);

  // const handleOptionPress = () => {
  //   setOptionSelected('');
  // };
  const [questionNumber, setQuestionNumber] = useState(1);
  const route = useRoute(); // Using useRoute hook to access route prop
  const { params } = route;
  const stakedAmount: any = params && 'text' in params ? params.text : ''; // Check if 'text' property exists

  // Now you can use stakedAmount in your component
  
  const {
    data: quizData,
    isLoading,
    isFetching,
    refetch: getNextQuestion,
  } = useGetQuestions();
  const { mutate: changeUserPennytots } = useChangeUserPennytots();
  const Loading = require('../../assets/loading.gif');
  const { mutate: stakeUserPennytots } = useStakeUserPennytots();
  const [selectedOption, setSelectedOption] = useState(null);
  const [goToNext, setGoToNext] = useState(false);
  const [score, setScore] = useState(0);
  const [isCorrect, setIsCorrect] = useState<any>(null);
  const { data: credits, refetch } = useGetCredit();
  const [isCurrentSelectionCorrect, setIsCurrentSelectionCorrect] =
    useState<any>(null);
  const [showScoreMessage, setShowScoreMessage] = useState(false);
  const [image, setImage] = useState(null);
  const { question, options, answer: correctAnswer } = quizData || {};
  const [end, setEnd] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [appState, setAppState] = useState(AppState.currentState);
  const [gameOver, setGameOver] = useState(false);
  const [gameWin, setGameWin] = useState(false);
  const [questionCount, setQuestionCount] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [isButtonClicked, setButtonClicked] = useState(false);

  const getRandomWrongImage = () => {
    const images = [
      Wrong2,
      Wrong3,
      Wrong4,
      Wrong5,
      Wrong6,
      Wrong7,
      Wrong8,
      Wrong9,
      Wrong10,
      Wrong11,
      Wrong12,
      Wrong13,
      Wrong14,
      Wrong15,
      Wrong16,
      Wrong17,
    ];
    const randomIndex = Math.floor(Math.random() * images.length);
    return images[randomIndex];
  };

  useEffect(() => {
    if (timeLeft === 0) {
      setGameOver(true);
      stakeUserPennytots('reduce', stakedAmount);
    }
  }, [timeLeft]);

  useEffect(() => {
    if (isCurrentSelectionCorrect === false) {
      setGameOver(true);
      stakeUserPennytots('reduce', stakedAmount);
    }
  }, [isCurrentSelectionCorrect]);

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        setModalVisible(true);
        return true; // This will prevent the default back behavior
      };

      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress
      );

      return () => backHandler.remove();
    }, [setModalVisible]) // Dependencies can be adjusted based on your actual dependencies
  );

  useEffect(() => {
    setStartTime(Date.now());
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime = Date.now();
      const timePassedInSeconds = Math.floor((currentTime - startTime) / 1000);
      const newTimeLeft = Math.max(60 - timePassedInSeconds, 0);
      setTimeLeft(newTimeLeft);

      if (newTimeLeft === 0 || gameOver || gameWin) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [gameOver, gameWin, startTime]);

  // Effect to navigate when time is up
  useEffect(() => {
    if (timeLeft === 0) {
      setImage(Wrong1); // Assuming Wrong1 is the image you want to display
      setShowScoreMessage(true);
    }
  }, [timeLeft]);

  useFocusEffect(
    React.useCallback(() => {
      getNextQuestion();

      return () => {
        // Optional: Any cleanup code goes here
      };
    }, [])
  );

  const formatTimeLeft = () => {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  const navigation = useNavigation();
  const handleNavigateToQuiz = () => {
    if (gameOver) {
      return; // Exit the function early if gameOver is true
    }
    setModalVisible(true);
  };
  const handleNavigateChallenge = () => {
    navigation.navigate('QuizScreen' as never); // Ensure that 'gameWin' is passed correctly
  };

  useEffect(() => {
    if (questionCount >= 5) {
      setGameWin(true);
    }
  }, [questionCount, isCurrentSelectionCorrect]);

  useEffect(() => {
    if (gameWin) {
      stakeUserPennytots('increase', stakedAmount * 5);
      const winnings = stakedAmount * 5;
      navigation.navigate('WinScreen', { stakedAmount: winnings });
    }
  }, [gameWin, stakedAmount, navigation]);

  const handleNavigateToWinMode = () => {
    // You can do some validation here before navigating
    navigation.navigate('WinScreen', { stakedAmount: 500 });
  };

  // useEffect(() => {
  //   const updateCredits = async () => {

  // }, [gameWin, credits, refetch]);

  const handleOptionSelect = async (option: any) => {
    // getNextQuestion(); // Fetch the next question
    // if (timeLeft === 0) {
    //   return;
    // }
    setOptionSelected(true); // Indicate that an option has been selected

    setSelectedOption(option);
    const isCorrect = option === correctAnswer;

    // If the selected option is correct, increase the question number by 1
    // if (isCorrect) {
    //   setQuestionNumber((prevQuestionNumber) => prevQuestionNumber + 1);
    // }
    // Submit the selected option to the backend
    try {
      const isAnswerCorrect = option === correctAnswer;
      setIsCorrect(isAnswerCorrect);
      setEnd(true);
      setShowScoreMessage(true);
      setTimeout(() => {
        setShowScoreMessage(false);
      }, 2500);
      if (isAnswerCorrect) {
        setQuestionCount((prevCount) => prevCount + 1);
        setGoToNext(true);
        setImage(Correct);
        setIsCurrentSelectionCorrect(true);
      } else {
        setIsCurrentSelectionCorrect(false);
        setImage(getRandomWrongImage());
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      setIsCurrentSelectionCorrect(true);
    }
  };
  const handleImageError = (error: any) => {
    console.error('Error loading image:', error);
    // Handle the error, such as displaying a placeholder image or showing an error message
  };

  const handleNextQuestion = () => {
    if (questionCount > 4 && isCurrentSelectionCorrect) {
      return;
    }
    setQuestionNumber((prevQuestionNumber) => prevQuestionNumber + 1);

    if (timeLeft === 0) {
      return;
    }
    setGoToNext(false);
    getNextQuestion(); // Refetch the mock data, replace it with your API call
    setEnd(false);
    setSelectedOption(null);
    setIsCorrect(null);
    setIsCurrentSelectionCorrect(null);
  };
  const handleEndChallenge = async () => {
    if (isButtonClicked) return; // Prevent function if already clicked
    setButtonClicked(true); // Set the flag to true on the first click
    try {
      await stakeUserPennytots('reduce', stakedAmount); // Assuming stakeUserPennytots is an async function
      navigation.navigate('QuizScreen' as never);
    } catch (error) {
      console.error('Error reducing Pennytots:', error);
      // Handle any errors, perhaps set an error state or show an alert
    }
  };

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: 'white', paddingHorizontal: 20 }}
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
        {modalVisible && (
          <CustomModal
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
            type='stake'
            handleConfirm={handleEndChallenge}
            navigation={navigation}
          />
        )}

        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'space-between',
            marginTop: rv(20),
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            <TouchableOpacity
              onPress={handleNavigateToQuiz}
              style={{ flexDirection: 'row', alignItems: 'center' }}
            >
              <GameModeSVG
                width={rv(22)}
                height={rv(23)}
                style={{ marginRight: rv(6) }}
              />
              <CustomText
                style={{
                  fontSize: rv(13),
                  fontWeight: '600',
                  color: '#4F4F4F',
                  paddingLeft: rv(4),
                }}
              >
                {t("exit_double_up_challenge")}
              </CustomText>
            </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
         
                <Text
                  style={{
                    fontSize: rv(13),
                    fontWeight: '600',
                    color: '#696969',
                  }}
                >
                  {stakedAmount} {t("staked")}
                </Text>
          
            </View>
          </View>
          <View style={{ flexDirection: 'column' }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: rv(30),
              }}
            >
              <Text
                style={{
                  fontSize: rv(14),
                  fontWeight: 'bold',
                  lineHeight: rv(20),
                }}
              >
               {t("question")}{questionNumber}
              </Text>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Image
                  source={require('app/assets/timer.png')}
                  style={{ width: rv(19), height: rv(19), marginRight: 8 }}
                />
                <Text
                  style={{
                    fontWeight: 'bold',
                    fontSize: rv(14),
                    color: '#FED830',
                  }}
                >
                  {formatTimeLeft()}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* {questionCount <= 29 && (
      <View>
        {isLoading && <Text>Loading...</Text>}
        {!isLoading && <Text style={{fontSize:rv(11.5),marginTop: rv(30), fontStyle:'normal', fontWeight: '700', color: '#696969'}}>{quizData?.question}</Text>}
      </View>)
      } */}

        {isFetching && questionCount <= 5 && (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignContent: 'center',
            }}
          >
            {isFetching && (
              <Image
                source={Loading}
                style={{ width: rv(50), height: rv(50) }}
              />
            )}
            {/* Your content here */}
          </View>
        )}
        {!isFetching && (
          <View style={{ flex: 1 }}>
            <Text
              style={{
                marginTop: rv(30),
                fontSize: rv(11.5),
                fontWeight: '700',
                fontFamily: 'semiBold',
                color: '#696969',
              }}
            >
              {quizData?.question}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                justifyContent: 'space-between',
                marginTop: rv(30),
              }}
            >
              {options?.map((option, index) => (
                <OptionButton
                  key={index}
                  option={option}
                  onPress={() => handleOptionSelect(option)}
                  isSelected={selectedOption === option}
                  isCorrect={isCurrentSelectionCorrect}
                  correctAnswer={correctAnswer}
                  gameOver={gameOver}
                  style={{ width: '45%' }} // Adjust the width as needed
                />
              ))}
            </View>
          </View>
        )}
        {/* Show correct gif or game over message based on the game state */}
        {isCurrentSelectionCorrect && !gameOver ? (
          <View style={styles.container}>
            <Image
              source={Correct}
              style={{ width: rv(200), height: rv(200) }}
            />
          </View>
        ) : null}

        {gameOver ? (
          <View style={{ alignItems: 'center', justifyContent: 'center' }}>
            <View style={styles.container2}>
              <Image
                source={image}
                style={{ width: rv(200), height: rv(200) }}
              />
            </View>
            <CustomText
              style={{
                fontSize: rv(22),
                fontWeight: 'bold',
                color: '#797978',
                lineHeight: rv(26),
                marginTop: rv(22),
              }}
            >
             {t("challenge_over")}
            </CustomText>
            {/* Render a button to continue */}
            <TouchableOpacity
              onPress={handleNavigateChallenge}
              style={{ marginTop: rv(32) }}
            >
              <View
                style={{
                  backgroundColor: '#FED830',
                  borderRadius: 32,
                  width: rv(267),
                  paddingVertical: rv(12),
                  paddingHorizontal: rv(8),
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <CustomText
                  style={{
                    fontSize: rv(14),
                    color: '#48463E',
                    lineHeight: rv(24),
                    fontFamily: 'regular',
                  }}
                >
                  {t("back_to_game_mode")}
                </CustomText>
              </View>
            </TouchableOpacity>
          </View>
        ) : null}
        {/* Remove the Animation component */}
        {!gameOver && !gameWin && goToNext && (
          <TouchableOpacity
            style={{
              position: 'absolute',
              bottom: rv(20),
              right: rv(20),
            }}
            onPress={handleNextQuestion}
          >
            <View
              style={{
                padding: rv(18),
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#FED830',
                width: rv(112),
                height: rv(54),
                borderRadius: 32,
              }}
            >
              <Image
                source={require('app/assets/arrow.png')}
                style={{ width: rv(30), height: rv(22), marginTop: rv(10) }}
              />
              <Text style={{ fontFamily: 'semiBold', fontSize: rv(13) }}>
                {t("next")}
              </Text>
            </View>
          </TouchableOpacity>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  container2: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageStyle: {
    marginTop: rv(47),
    width: rv(210), // Set width as needed
    height: rv(156), // Set height as needed
    // Add more styles as needed
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
});

export default GameMode;
