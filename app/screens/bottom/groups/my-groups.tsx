import { useIsFocused, useFocusEffect } from '@react-navigation/native';

import React, {
  Component,
  useState,
  useCallback,
  useEffect,
  useContext,
  useRef,
} from 'react';
import {
  Text,
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ListRenderItemInfo,
} from 'react-native';
import { useMyGroups, usePrefetchGroup } from 'app/redux/group/hooks';
import { Axios } from 'app/api/axios';
// import { showMessage } from 'app/reuseables/toast-message';
import Group from 'app/components/cards/group';
import { useRefetchOnFocus } from 'app/custom-hooks/usefetchonfocus';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import  BlankMyGroupSVG  from 'app/assets/svg/empty-pages.svg';
import ChatTextSVG from '@/app/assets/svg/Mygroups_text.svg'
import { useTheme, createOptimizedThemedStyles, FONT_SIZES } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
const Loading = require('../../../assets/loading.gif');

// Container Component
const MyGroupsContainer = ({ navigation }: any) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const { data, error, isFetching, refetch, isLoading } = useMyGroups();
  const prefetchGroup = usePrefetchGroup();
  // Removed useRefetchOnFocus for better performance - background refetch handles updates

  // Prefetch group details when user scrolls
  const handleGroupVisible = (groupId: string) => {
    prefetchGroup(groupId);
  };

  return (
    <MyGroupsPresentation
      theme={theme}
      styles={styles}
      data={data}
      isLoading={isLoading}
      isFetching={isFetching}
      refetch={refetch}
      navigation={navigation}
      onGroupVisible={handleGroupVisible}
    />
  );
};

// Presentation Component
interface MyGroupsPresentationProps {
  theme: any;
  styles: any;
  data: any;
  isLoading: boolean;
  isFetching: boolean;
  refetch: () => void;
  navigation: any;
  onGroupVisible?: (groupId: string) => void;
}

const MyGroupsPresentation: React.FC<MyGroupsPresentationProps> = ({
  theme,
  styles,
  data,
  isLoading,
  isFetching,
  refetch,
  navigation,
  onGroupVisible,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.spacer}></View>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <Image source={Loading} style={styles.loadingImage} />
        </View>
      ) : (
        <>
          {data && data.length > 0 ? (
            <FlatList
              data={data}
              refreshing={isFetching}
              style={styles.flatList}
              keyExtractor={(item: GroupProps) => item._id}
              onViewableItemsChanged={({ viewableItems }) => {
                // Prefetch data for visible groups
                viewableItems.forEach((viewableItem) => {
                  if (viewableItem.item && onGroupVisible) {
                    onGroupVisible(viewableItem.item._id);
                  }
                });
              }}
              viewabilityConfig={{
                itemVisiblePercentThreshold: 50, // Trigger when 50% of item is visible
              }}
              renderItem={({ item }: ListRenderItemInfo<GroupProps>) => (
                <Group
                  item={item}
                  navigation={navigation}
                  refreshFunction={() => refetch()}
                />
              )}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <BlankMyGroupSVG style={styles.emptyIcon} />
              <ChatTextSVG style={styles.emptyText} />
            </View>
          )}
        </>
      )}
    </View>
  );
};

// Themed Styles with optimized font sizes
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  spacer: {
    alignItems: 'center',
    marginVertical: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingImage: {
    width: rv(50),
    height: rv(50),
  },
  flatList: {
    paddingHorizontal: 20,
  },
  emptyContainer: {
    alignContent: 'center',
    marginTop: rv(20),
  },
  emptyIcon: {
    alignSelf: 'center',
  },
  emptyText: {
    alignSelf: 'center',
    marginTop: rv(20),
  },
}));

export default MyGroupsContainer;
