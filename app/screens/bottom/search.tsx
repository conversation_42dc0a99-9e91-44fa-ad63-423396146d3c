import { useFocusEffect } from "@react-navigation/native";

import { BackHandler } from "react-native";
import React, { useState, useEffect, FunctionComponent } from "react";
import {
  TextInput,
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Share,
  FlatList,
} from "react-native";
// import AppIntroSlider from 'react-native-app-intro-slider';
// import AsyncStorage from '@react-native-community/async-storage';
// import { SearchBar, BottomSheet } from 'react-native-elements';
// import ActionButton from 'react-native-action-button';
// import PhoneInput from 'react-native-phone-number-input';
import Loader from "app/components/elements/Loader";
import { ActivityIndicator } from "react-native";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import SegmentedControlTab from "react-native-segmented-control-tab";
import { CustomText } from "app/components/elements";
import { default as themeFont } from "app/assets/themes/fonts.json";
import { TopicCard } from "app/components/cards/topics";
import Group from "app/components/cards/group";
import {
  Menu,
  MenuProvider,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from "react-native-popup-menu";
import moment from "moment";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import ReportDialog from "app/components/dialogs/report";
//import EmptySearchScreen from 'app/co/EmptySearchScreen';
import SelectDropdown from "react-native-select-dropdown";
import { useSelector } from "react-redux";
import { Axios } from "app/api/axios";
import { userId, userToken } from "app/redux/user/reducer";
import { SearchCommentCard } from "../../components/cards/search-comments";
import MenuSVG from "app/assets/svg/menu.svg";
import { useTranslation } from "react-i18next";
import { responsiveValue as rv } from "app/providers/responsive-value";
import CommentSVG from "app/assets/svg/comment.svg";
import ThumbSVG from "app/assets/svg/thumb.svg";
import Audio from "app/components/attachments/Audio";
import Video from "app/components/attachments/Video";
import { ShowAlert } from "app/providers/toast";
import { useSearchTopics } from "app/redux/topic/hooks";
import { showModal, hideModal } from "app/providers/modals";
import BlankSearchSVG from "app/assets/svg/empty-pages.svg";
import SearchTextSVG from "@/app/assets/svg/search-text.svg";
import { createOptimizedThemedStyles, useTheme, FONT_SIZES } from "app/theme";
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";

type SearchItemProps = {
  item?: any;
  placeholder?: any;
  navigation: any;
  recommended?: boolean;
  menu?: boolean;
  refreshFunction?: () => void;
};
const Search: FunctionComponent<SearchItemProps> = ({
  item,
  navigation,
  recommended,
  refreshFunction,
  menu = true,
}) => {
  const myId = useSelector(userId);
  const Loading = require("../../assets/loading.gif");
  const { SlideInMenu } = renderers;
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  // const { ContextMenu, SlideInMenu, Popover } = renderers;
  const onShare = async (message: string, image: string) => {
    try {
      const result = await Share.share({
        message: message,
        url: image,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error) {
      //  alert(error.message);
    }
  };

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [chats, setChats] = useState([]);
  const [users, setUsers] = useState([]);
  const [groups, setGroups] = useState([]);
  const [comments, setComments] = useState([]);
  const [subComments, setSubComments] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [tabIndex, setTabIndex] = useState(0);

  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});
  const [totalResults, setTotalResults] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const [numLikes, setNumLikes] = useState(0);
  const { t } = useTranslation();
  const {
    mutate: searchTopics,
    isLoading,
    isError,
    error,
  } = useSearchTopics(tabIndex, searchInput, {
    setUsers,
    setChats,
    setGroups,
    setData,
    setComments,
    setSubComments,
    setTotalResults,
  });

  const filterTypes = [
    t("Search_users"),
    t("Search_chats"),
    t("Search_groups"),
    "Posts",
    t("Search_comments"),
    // t('Search_subComments'),
  ];
  const wait = (timeout: number) => {
    return new Promise((resolve) => setTimeout(resolve, timeout));
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    wait(1000).then(() => setRefreshing(false));
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      function reloadPage() {
        LoadTopics2(true);
      }

      reloadPage();
    }, [])
  );

  function muteNotification() {
    Axios({
      method: "post",
      url: "/app/mute-notifications/",
      data: {
        contentId: item._id,
        type: "group",
        action: item.muted ? "unmute" : "mute",
      },
    }).then((response: any) => {
      refreshFunction!();
      showModal({
        modalVisible: true,
        title: "Alert",
        message: "Notifications has been muted successfully",
        setModalVisible: hideModal, // Function to close the modal
        type: "success-alert",
        handleConfirm: () => {
          console.log("Confirmed!");
          hideModal();
        },
        handleAlert: () => {
          console.log("Alert handled!");
          hideModal();
        },
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Notifications has been muted successfully',
      // });
    });
  }

  function LoadTopics(loader: boolean) {
    if (searchInput !== "") {
      setLoading(true); // Start the loading state
      let searchType = "";
      if (tabIndex === 0) {
        searchType = "users";
      } else if (tabIndex === 1) {
        searchType = "chats";
      } else if (tabIndex === 2) {
        searchType = "groups";
      } else if (tabIndex === 3) {
        searchType = "topics";
      } else if (tabIndex === 4) {
        searchType = "topic-comments";
      } else if (tabIndex === 5) {
        searchType = "topic-sub-comments";
      }

      Axios({
        method: "POST",
        url: "/app/search?page=1&limit=50",
        data: {
          search: searchInput,
          searchType: searchType,
        },
      })
        .then((response: any) => {
          if (tabIndex === 0) {
            setUsers(response.data.users.docs);
            setTotalResults(response.data.users.totalDocs);
          } else if (tabIndex === 1) {
            setChats(response.data.chats.docs);
            setTotalResults(response.data.length);
          } else if (tabIndex === 2) {
            setGroups(response.data.groups.docs);
            setTotalResults(response.data.groups.totalDocs);
          } else if (tabIndex === 3) {
            setData(response.data.topics.docs);
            setTotalResults(response.data.topics.totalDocs);
          } else if (tabIndex === 4) {
            setComments(response.data.topicComments.docs);
            setTotalResults(response.data.topicComments.totalDocs);
          } else if (tabIndex === 5) {
            setSubComments(response.data.topicComments.docs);
            setTotalResults(response.data.topicComments.totalDocs);
          }
        })
        .finally(() => {
          setLoading(false); // End the loading state
        });
    }
  }

  function LoadTopics2(loader: boolean) {
    if (searchInput !== "") {
      searchTopics(searchInput);
    }
  }

  //DISPLAY POSTS
  useEffect(() => {
    LoadTopics2(true);
    if (searchInput === "") {
      setChats([]);
    }
  }, [refreshing, tabIndex, searchInput]);

  function openReportDetails(item: any, type: string) {
    setReportDetails({ data: { _id: item }, type });
    setShowReportDialog(true);
  }

  console.log("datazz", chats, "datazz");
  return (
    <View
      style={{
        paddingHorizontal: 10,
        backgroundColor: theme.colors.background,
        width: "100%",
        flex: 1,
      }}
    >
      <ReportDialog
        show={showReportDialog}
        setShow={setShowReportDialog}
        reportDetails={reportDetails}
      />

      <View
        style={{
          paddingHorizontal: 4,
        }}
      >
        <View
          style={{
            marginVertical: 10,
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            width: "100%",
            paddingBottom: 5,
          }}
        >
          <View style={styles.searchSection}>
            <FontAwesome
              style={styles.searchIcon}
              name="search"
              size={20}
              color={theme.colors.textSecondary}
            />
            <TextInput
              style={[styles.input, { color: theme.colors.text }]}
              placeholder={t("Search_searchText")}
              placeholderTextColor={theme.colors.textPlaceholder}
              onChangeText={(text) => {
                setSearchInput(text);
              }}
              value={searchInput}
              underlineColorAndroid="transparent"
            />
          </View>

          <SelectDropdown
            data={filterTypes}
            onSelect={(selectedItem: any, index: number) => {
              setTabIndex(index);
            }}
            renderDropdownIcon={(isOpened: boolean) => {
              return (
                <FontAwesome
                  name="sliders"
                  size={20}
                  color={theme.colors.textSecondary}
                />
              );
            }}
            dropdownIconPosition={"left"}
            buttonStyle={{
              width: "30%",
              borderRadius: 2,
              height: "100%",
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border,
              borderWidth: 0.2,
            }}
            buttonTextStyle={
              [
                styles.font_thirteen,{
              fontFamily: "medium",
              marginTop: 4,
              color: theme.colors.textSecondary,
            }]}
            rowTextStyle={[
              styles.font_thirteen,{
              fontFamily: "medium",
              color: theme.colors.text,
            }]}
            defaultValueByIndex={0}
            defaultButtonText="Filter"
            buttonTextAfterSelection={(selectedItem: any, index: number) => {
              // text represented after item is selected
              // if data array is an array of objects then return selectedItem.property to render after item is selected
              return selectedItem;
            }}
            dropdownStyle={{
              backgroundColor: theme.colors.surface,
              borderRadius: 3,
            }}
            rowStyle={{
              height: 37,
              borderBottomColor: theme.colors.surface,
            }}
            rowTextForSelection={(item: any, index: number) => {
              // text represented for each item in dropdown
              // if data array is an array of objects then return item.property to represent item in dropdown
              return item;
            }}
          />
        </View>
        {loading && (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
          </View>
        )}

        {isLoading ? (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "center",
              alignContent: "center",
            }}
          >
            {isLoading && (
              <Image
                source={Loading}
                style={{ width: rv(50), height: rv(50) }}
              />
            )}
            {/* Your content here */}
          </View>
        ) : searchInput.trim() ? (
          <View
            style={{
              paddingHorizontal: 5,
              flexDirection: "row",
              paddingBottom: 8,
            }}
          >
            <CustomText
              style={[
                styles.font_thirteen,{
                color: theme.colors.text,
                fontFamily: "bold",
              }]}
              // textType='bold'
            >
              {totalResults} {totalResults > 1 ? "Results" : "Result"}
            </CustomText>
            <CustomText
              style={[
                styles.font_thirteen,{
                marginLeft: 5,
                color: theme.colors.textSecondary,
                fontFamily: "bold",
              }]}
              // textType='bold'
            >
              in {filterTypes[tabIndex]}
            </CustomText>
          </View>
        ) : (
          <View
            style={{
              alignContent: "center",
              marginTop: "30%",
            }}
          >
            <BlankSearchSVG style={{ alignSelf: "center" }} />

            <SearchTextSVG style={{ alignSelf: "center", marginTop: rv(30) }} />
          </View>
        )}

        {!isLoading && (
          <FlatList
            data={
              tabIndex === 0
                ? users
                : tabIndex === 1
                ? chats
                : tabIndex === 2
                ? groups
                : tabIndex === 3
                ? data
                : tabIndex === 4
                ? comments
                : tabIndex === 5
                ? subComments
                : null
            }
            keyExtractor={(item: any, index: number) => {
              return `${tabIndex}-${item._id || index}`;
            }}
            refreshing={refreshing}
            onRefresh={() => LoadTopics2(true)}
            renderItem={({ item }) => {
              return (
                <View
                  style={{
                    flex: 1,
                  }}
                >
                  {tabIndex === 0 ? (
                    <TouchableOpacity
                      onPress={() => {
                        if (item._id != myId) {
                          navigation.navigate("Common", {
                            screen: "private-chat",
                            params: {
                              postid: item.first_name + " " + item.last_name,
                              chatuser: item._id,
                              loggedUser: myId,
                              userDetails: item,
                            },
                          });
                        }
                      }}
                      style={{
                        ...styles.topicStyle,
                        padding: 6,
                        // justifyContent: 'center',

                        alignItems: "flex-start",
                      }}
                    >
                      <View>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              item.profile_picture &&
                              item.profile_picture.length > 0
                            ) {
                              navigation.push("Common", {
                                screen: "fullscreen-image",
                                params: {
                                  image: item.profile_picture,
                                },
                              });
                            }
                          }}
                        >
                          <Image
                            //defaultSource= {require('../assets/defaultProfilePicture.png')}
                            source={
                              item.profile_picture &&
                              item.profile_picture.length > 0
                                ? { uri: item.profile_picture }
                                : require("app/assets/connectify-thumbnail.png")
                            }
                            style={{
                              marginRight: 15,
                              width: 50,
                              height: 50,
                              borderRadius: wp("7%"),
                              borderColor: theme.colors.border,
                              borderWidth: wp("0.6%"),
                            }}
                          />
                        </TouchableOpacity>
                      </View>
                      <View style={{ width: "80%" }}>
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "center",
                            alignItems: "center",
                            marginBottom: 10,
                          }}
                        >
                          <View style={{ width: "100%" }}>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                marginBottom: 4,
                              }}
                            >
                              <View
                                style={{
                                  flexDirection: "column",
                                }}
                              >
                                <CustomText
                                  // textType='bold'
                                  style={[
                                    styles.font_thirteen,{
                                    color: theme.colors.text,
                                    fontFamily: "bold",
                                    
                                  }]}
                                >
                                  {item.last_name.length +
                                    item.first_name.length >
                                  13 ? (
                                    <CustomText
                                      style={[
                                        styles.font_thirteen,{
                                        color: theme.colors.text,
                                        fontFamily: "bold",
                                  
                                      }]}
                                    >
                                      {item.first_name +
                                        " " +
                                        item.last_name.slice(0, 13)}
                                      ...
                                    </CustomText>
                                  ) : (
                                    <CustomText
                                      style={[
                                        styles.font_thirteen,{
                                        color: theme.colors.text,
                                        fontFamily: "bold",
                                      
                                      }]}
                                    >
                                      {item.first_name + " " + item.last_name}
                                    </CustomText>
                                  )}
                                </CustomText>
                                <CustomText
                                  adjustsFontSizeToFit={false}
                                  numberOfLines={2}
                                  style={[
                                    styles.font_eleven,{
                                    // flexDirection: 'row',

                                    // width: '80%',
                                    color: theme.colors.textSecondary,
                                    fontFamily: "medium",
                                  }]}
                                >
                                  {item.company_position && item.company
                                    ? `${
                                        item?.company_position?.replace(
                                          /(\r\n|\n|\r)/gm,
                                          ""
                                        ) || "company"
                                      } at ${item.company}`
                                    : "Position at company"}
                                </CustomText>
                              </View>
                              <Menu
                                renderer={SlideInMenu}
                                // onSelect={(value:any) => onMenuClicked(value)}
                              >
                                <MenuTrigger>
                                  <View
                                    style={{
                                      alignItems: "flex-end",
                                      flexDirection: "row",
                                      padding: rv(3),
                                    }}
                                  >
                                    <MenuSVG width={15} height={15} />
                                  </View>
                                </MenuTrigger>

                                <MenuOptions
                                  customStyles={{
                                    optionText: [styles.text],
                                    optionsContainer: [
                                      {
                                        borderRadius: 15,
                                      },
                                    ],
                                  }}
                                >
                                  <View
                                    style={{
                                      margin: 5,
                                      flexDirection: "column",
                                      marginVertical: 10,
                                      padding: 15,
                                    }}
                                  >
                                    <MenuOption
                                      onSelect={() => {
                                        openReportDetails(item._id, "user");
                                      }}
                                    >
                                      <View
                                        style={{
                                          flexDirection: "row",
                                          width: "100%",
                                          alignItems: "center",
                                        }}
                                      >
                                        <View
                                          style={{
                                            marginRight: 10,
                                          }}
                                        >
                                          <FontAwesome name="flag" size={20} />
                                        </View>
                                        <CustomText
                                          style={[
                                            styles.font_thirteen,
                                            {
                                            color: theme.colors.text,
                                            fontFamily: "medium",
                                          }]}
                                        >
                                          {t("Chats_reportUser")}
                                        </CustomText>
                                      </View>
                                    </MenuOption>
                                  </View>
                                </MenuOptions>
                              </Menu>
                            </View>

                            {item.bio ? (
                              <CustomText
                                adjustsFontSizeToFit={false}
                                numberOfLines={1}
                                style={[
                                  styles.font_thirteen,
                                  {
                                    fontFamily: "medium",
                                    color: theme.colors.text,
                                  },
                                ]}
                              >
                                {t("EditProfile_Bio")}:{" "}
                                {item.bio.length > 28
                                  ? item.bio.substring(0, 28) + "..."
                                  : item.bio}
                              </CustomText>
                            ) : null}

                            {/* {item.company_position ? (
                           <CustomText>
                             Company:{' '}
                             {item.company_position?.length > 18
                               ? item.company_position?.substring(0, 18) + '...'
                               : item.company_position}
                           </CustomText>
                         ) : null} */}
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ) : tabIndex === 1 ? (
                    <TouchableOpacity
                      onPress={() => {
                        if (item.user?._id != myId) {
                          navigation.navigate("Common", {
                            screen: "private-chat",
                            params: {
                              postid:
                                item.user.first_name +
                                " " +
                                item.user.last_name,
                              chatuser: item.user._id,
                              loggedUser: myId,
                              userDetails: {
                                _id: item.user._id,
                                first_name: item.user.first_name,
                                last_name: item.user.last_name,
                                profile_picture: item.user?.profile_picture,
                              },
                            },
                          });
                        }
                      }}
                      style={{
                        ...styles.topicStyle,
                        padding: 6,
                        // justifyContent: 'center',
                        alignItems: "flex-start",
                        // Remove alignItems: 'center'
                        flexDirection: "row", // ensure image and text are side by side
                      }}
                    >
                      <View>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              item.user.profile_picture &&
                              item.user.profile_picture.length > 0
                            ) {
                              navigation.push("Common", {
                                screen: "fullscreen-image",
                                params: {
                                  image: item.user.profile_picture,
                                },
                              });
                            }
                          }}
                        >
                          <Image
                            source={
                              item.user.profile_picture &&
                              item.user.profile_picture.length > 0
                                ? { uri: item.user.profile_picture }
                                : require("app/assets/connectify-thumbnail.png")
                            }
                            style={{
                              marginRight: 10,
                              width: 50,
                              height: 50,
                              borderRadius: wp("7%"),
                              borderColor: theme.colors.border,
                              borderWidth: wp("0.6%"),
                            }}
                          />
                        </TouchableOpacity>
                      </View>
                      <View style={{ width: "80%" }}>
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "center",
                            alignItems: "center",
                            marginBottom: 10,
                          }}
                        >
                          <View style={{ width: "100%" }}>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                marginBottom: 4,
                              }}
                            >
                              <View
                                style={{
                                  flexDirection: "column",
                                }}
                              >
                                <CustomText
                                  style={[
                                    styles.font_thirteen,
                                    {
                                      color: theme.colors.text,

                                      fontFamily: "bold",
                                    },
                                  ]}
                                >
                                  {item.user.last_name.length +
                                    item.user.first_name.length >
                                  13 ? (
                                    <CustomText
                                      style={[
                                        styles.font_thirteen,
                                        {
                                          color: theme.colors.text,

                                          fontFamily: "medium",
                                        },
                                      ]}
                                    >
                                      {item.user.first_name +
                                        " " +
                                        item.user.last_name.slice(0, 13)}
                                      ...
                                    </CustomText>
                                  ) : (
                                    <CustomText
                                      style={[
                                        styles.font_thirteen,
                                        {
                                          color: theme.colors.text,
                                          fontFamily: "medium",
                                        },
                                      ]}
                                    >
                                      {item.user.first_name +
                                        " " +
                                        item.user.last_name}
                                    </CustomText>
                                  )}
                                </CustomText>
                                <CustomText
                                  adjustsFontSizeToFit={false}
                                  numberOfLines={2}
                                  ellipsizeMode="tail"
                                  style={[
                                    styles.font_eleven,
                                    {
                                      color: theme.colors.textSecondary,
                                      fontFamily: "medium",
                                    },
                                  ]}
                                >
                                  {item.user.company_position &&
                                  item.user.company
                                    ? `${
                                        item?.user.company_position?.replace(
                                          /(\r\n|\n|\r)/gm,
                                          ""
                                        ) || "Position"
                                      } at ${item.user.company}`
                                    : "Position at company"}
                                </CustomText>
                              </View>
                              <Menu renderer={SlideInMenu}>
                                <MenuTrigger>
                                  <View
                                    style={{
                                      alignItems: "flex-end",
                                      flexDirection: "row",
                                      padding: rv(3),
                                    }}
                                  >
                                    <MenuSVG width={15} height={15} />
                                  </View>
                                </MenuTrigger>
                                <MenuOptions
                                  customStyles={{
                                    optionText: [styles.text],
                                    optionsContainer: [
                                      {
                                        borderRadius: 15,
                                      },
                                    ],
                                  }}
                                >
                                  <View
                                    style={{
                                      margin: 5,
                                      flexDirection: "column",
                                      marginVertical: 10,
                                      padding: 15,
                                    }}
                                  >
                                    <MenuOption
                                      onSelect={() => {
                                        openReportDetails(item._id, "user");
                                      }}
                                    >
                                      <View
                                        style={{
                                          flexDirection: "row",
                                          width: "100%",
                                          alignItems: "center",
                                        }}
                                      >
                                        <View style={{ marginRight: 10 }}>
                                          <FontAwesome name="flag" size={20} />
                                        </View>
                                        <CustomText
                                          style={[
                                            styles.font_thirteen,
                                            {
                                              color: theme.colors.text,
                                              fontFamily: "medium",
                                            },
                                          ]}
                                        >
                                          {t("Chats_reportUser")}
                                        </CustomText>
                                      </View>
                                    </MenuOption>
                                  </View>
                                </MenuOptions>
                              </Menu>
                            </View>
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ) : tabIndex === 2 ? (
                    <Group item={item} navigation={navigation} />
                  ) : tabIndex === 3 ? (
                    <TopicCard item={item} navigation={navigation} />
                  ) : tabIndex === 4 ? (
                    <SearchCommentCard item={item} navigation={navigation} />
                  ) : tabIndex === 5 ? (
                    <View
                      style={{
                        flex: 1,
                      }}
                    >
                      <SearchCommentCard item={item} navigation={navigation} />
                    </View>
                  ) : null}
                </View>
              );
            }}
          />
        )}
      </View>

      {/* <TouchableOpacity
        activeOpacity={0.7}
        onPress={() =>
          navigation.push('Home', { screen: 'PostTopic' })
        }
        style={styles.touchableOpacityStyle}
      >

        <Image
          source={require('app/assets/floating.png')}
          style={styles.floatingButtonStyle}
        />
      </TouchableOpacity> */}
    </View>
  );
};
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    marginHorizontal: 16,
  },
  title: {
    textAlign: "center",
    marginVertical: 8,
  },
  searchSection: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: theme.colors.surfaceVariant,
    padding: 5,
    marginHorizontal: 5,
    borderRadius: 7,
  },
  searchIcon: {
    padding: 10,
  },
  input: {
    flex: 1,
    paddingVertical: 3,
    paddingRight: 10,
    paddingLeft: 0,
    backgroundColor: "transparent",
    color: theme.colors.text,
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    fontFamily: "medium",
  },
  fixToText: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  separator: {
    marginVertical: 8,
    borderBottomColor: theme.colors.border,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  signup: {
    height: 60,
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: 40,
    paddingLeft: 20,
    backgroundColor: theme.colors.surface,
    marginBottom: 15,
    borderStyle: "solid",
    marginTop: 10,
  },
  replycomment: {
    flex: 1,
    flexDirection: "row",
    height: 45,
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: 40,
    paddingLeft: 20,
    backgroundColor: theme.colors.surfaceVariant,
    marginBottom: 15,
    borderStyle: "solid",
    marginTop: 10,
    position: "relative",
  },
  reply: {
    flexDirection: "row",
    width: "90%",
    justifyContent: "space-around",
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: 10,
    paddingLeft: 20,
    backgroundColor: theme.colors.surface,
    marginBottom: 15,
    borderStyle: "solid",
    marginTop: 10,
  },
  medicalinput: {
    height: 50,
    borderColor: theme.colors.border,
    borderWidth: 2,
    marginBottom: 20,
    borderRadius: 33,
    paddingLeft: 20,
    backgroundColor: theme.colors.surfaceVariant,
  },
  medicalinputarea: {
    height: 120,
    borderColor: theme.colors.border,
    borderWidth: 2,
    marginBottom: 20,
    borderRadius: 33,
    paddingLeft: 20,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: "flex-start",
  },
  icontext: {
    paddingTop: 30,
    color: theme.colors.textSecondary,
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    fontWeight: "200",
    textAlign: "center",
    fontFamily: "regular",
  },
  profilename: {
    fontFamily: "regular",
    fontStyle: "normal",
    fontWeight: "600",
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    lineHeight: 27,
  },
  touchbtn: {
    backgroundColor: "purple",
    height: 50,
    width: 150,
    borderColor: "purple",
    borderWidth: 2,
    borderRadius: 33,
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 10,
  },
  consultview: {
    flex: 1,
    width: 500,
    flexDirection: "row",
    alignItems: "stretch",
    borderStyle: "solid",
    borderBottomColor: theme.colors.border,
    borderBottomWidth: 2,
    paddingTop: 10,
    marginLeft: 16,
    paddingBottom: 10,
  },
  medicalview: {
    flex: 1,
    flexDirection: "column",
    borderStyle: "solid",
    paddingTop: 10,
    marginLeft: 16,
    marginRight: 17,
  },
  scheduleview: {
    flex: 1,
    flexDirection: "column",
    alignContent: "stretch",
    marginLeft: 16,
    marginRight: 17,
  },
  slide: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "blue",
  },
  image: {
    width: 400,
    height: 481,
    // marginVertical: 12,
  },
  text: {
    color: theme.colors.textSecondary,
    // textAlign: 'center',
    fontSize: wp("4%"),
    width: "87%",
    fontFamily: "regular",
    marginLeft: wp("4%"),
  },
  wrapperText: {
    color: theme.colors.textSecondary,
    // textAlign: 'center',
    fontSize: wp("4%"),
    width: "87%",
    fontFamily: "regular",
    // marginVertical: hp('1%')
    // marginLeft: wp('1%')
  },
  splashbtn: {
    alignContent: "center",
    alignSelf: "center",
    alignItems: "center",
    backgroundColor: "black",
    height: 60,
    width: 300,
    borderColor: "black",
    borderWidth: 2,
    marginTop: 1,
    marginBottom: 20,
    borderRadius: 30,
    paddingTop: 25,
    fontStyle: "normal",
    fontFamily: "bold",
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    lineHeight: 14,
    textAlign: "center",
    color: theme.colors.primary,
    display: "flex",
    marginLeft: 20,
    marginRight: 20,
  },
  errorTextStyle: {
    color: "red",
    textAlign: "center",
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
  },
  tabStyle: {},
  scrollStyle: {
    backgroundColor: theme.colors.background,
    paddingLeft: 65,
    paddingRight: 65,
    // justifyContent: 'center',
  },
  tabBarTextStyle: {
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    fontWeight: "normal",
    fontFamily: "medium",
  },
  underlineStyle: {
    height: 3,
    backgroundColor: "red",
    borderRadius: 3,
    width: 15,
  },
  topbar: {
    flexDirection: "row",
    backgroundColor: "dimgray",
    paddingTop: 15,
  },
  trigger: {
    padding: 5,
    margin: 5,
  },
  triggerText: {
    color: "white",
  },
  disabled: {
    color: theme.colors.textDisabled,
  },
  divider: {
    marginVertical: 5,
    marginHorizontal: 2,
    borderBottomWidth: 1,
    borderColor: theme.colors.border,
  },
  topicStyle: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    backgroundColor: theme.colors.surface,

    padding: 20,
  },
  touchableOpacityStyle: {
    position: "absolute",
    width: 50,
    height: 50,
    alignItems: "center",
    justifyContent: "center",
    right: 30,
    bottom: 30,
  },
  floatingButtonStyle: {
    resizeMode: "contain",
    width: "125%",
    height: "125%",
  },
  attachment: {
    marginVertical: 5,
    width: wp("68%"),
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  font_eleven: {
    fontSize: fs('SM'), // rvf(11) -> fs('SM') (11)
  },
  font_thirteen: {
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
  },
}));

export default Search;
