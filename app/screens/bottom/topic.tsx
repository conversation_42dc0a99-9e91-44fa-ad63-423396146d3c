import { useTopics } from 'app/redux/topic/hooks';
import React from 'react';

import {
  View,
  StatusBar,
  FlatList,
  Text, TouchableOpacity, StyleSheet, Image
} from 'react-native';

import { TopicCard } from 'app/components/cards';
import FloatingAddSVG from 'app/assets/svg/floating-add.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme, createThemedStyles } from 'app/theme';
const Loading = require('../../assets/loading.gif');

// Presentation component props
interface TopicPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  topicId: any;
  data: any[];
  isLoading: boolean;
  isFetching: boolean;
  onRefresh: () => void;
  onNavigateToPost: () => void;
}

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flexDirection: 'column',
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchableOpacityStyle: {
    position: 'absolute',
    width: rv(50),
    height: rv(50),
    alignItems: 'center',
    justifyContent: 'center',
    right: rv(30),
    bottom: rv(30),
  },
}));

// Container Component (handles logic, state, theme)
const TopicContainer = ({ navigation, topicId }: any) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const { status, data, error, isFetching, refetch, isLoading } = useTopics({topicId});

  console.log('topicid', topicId);

  const handleRefresh = () => {
    refetch();
  };

  const handleNavigateToPost = () => {
    navigation.navigate('PostTopic');
  };

  return (
    <TopicPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      topicId={topicId}
      data={data}
      isLoading={isLoading}
      isFetching={isFetching}
      onRefresh={handleRefresh}
      onNavigateToPost={handleNavigateToPost}
    />
  );
};

// Presentational Component (pure UI)
const TopicPresentation = ({
  theme,
  styles,
  navigation,
  topicId,
  data,
  isLoading,
  isFetching,
  onRefresh,
  onNavigateToPost,
}: TopicPresentationProps) => {
  return (
    <View style={styles.container}>
      <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <Image
            source={Loading}
            style={{
              width: rv(50),
              height: rv(50),
            }}
          />
        </View>
      ) : (
        <FlatList
          data={data}
          keyExtractor={(item: any, index) => item._id}
          refreshing={isFetching}
          onRefresh={onRefresh}
          renderItem={({ item }) => {
            return <TopicCard
              item={item}
              navigation={navigation}
              convo={false}
              refreshFunction={onRefresh}
              onToggleMuted={function (): void {
                throw new Error('Function not implemented.');
              }}
              muted={false}
            />;
          }}
        />
      )}

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={onNavigateToPost}
        style={styles.touchableOpacityStyle}
      >
        <FloatingAddSVG
          width={rv(45)}
          height={rv(45)}
        />
      </TouchableOpacity>
    </View>
  );
};

// Export container as default
export default TopicContainer;
