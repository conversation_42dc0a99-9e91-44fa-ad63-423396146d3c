import { useTopics, usePrefetchTopic, usePrefetchTopicComments } from 'app/redux/topic/hooks';
import React from 'react';

import {
  View,
  StatusBar,
  FlatList,
  Text, TouchableOpacity, StyleSheet, Image
} from 'react-native';

import { TopicCard } from 'app/components/cards';
import FloatingAddSVG from 'app/assets/svg/floating-add.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme, createThemedStyles } from 'app/theme';
const Loading = require('../../assets/loading.gif');

// Presentation component props
interface TopicPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  topicId: any;
  data: any[];
  isLoading: boolean;
  isFetching: boolean;
  onRefresh: () => void;
  onNavigateToPost: () => void;
  onTopicVisible?: (topicId: string) => void;
}

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flexDirection: 'column',
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchableOpacityStyle: {
    position: 'absolute',
    width: rv(50),
    height: rv(50),
    alignItems: 'center',
    justifyContent: 'center',
    right: rv(30),
    bottom: rv(30),
  },
}));

// Container Component (handles logic, state, theme)
const TopicContainer = ({ navigation, topicId }: any) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const { status, data, error, isFetching, refetch, isLoading } = useTopics({topicId});
  const prefetchTopic = usePrefetchTopic();
  const prefetchTopicComments = usePrefetchTopicComments();

  console.log('topicid', topicId);

  const handleRefresh = () => {
    refetch();
  };

  const handleNavigateToPost = () => {
    navigation.navigate('PostTopic');
  };

  // Prefetch topic details and comments when user scrolls
  const handleTopicVisible = (topicId: string) => {
    // Prefetch topic details and comments for better UX
    prefetchTopic(topicId);
    prefetchTopicComments(topicId);
  };

  return (
    <TopicPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      topicId={topicId}
      data={data}
      isLoading={isLoading}
      isFetching={isFetching}
      onRefresh={handleRefresh}
      onNavigateToPost={handleNavigateToPost}
      onTopicVisible={handleTopicVisible}
    />
  );
};

// Presentational Component (pure UI)
const TopicPresentation = ({
  theme,
  styles,
  navigation,
  topicId,
  data,
  isLoading,
  isFetching,
  onRefresh,
  onNavigateToPost,
  onTopicVisible,
}: TopicPresentationProps) => {
  return (
    <View style={styles.container}>
      <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <Image
            source={Loading}
            style={{
              width: rv(50),
              height: rv(50),
            }}
          />
        </View>
      ) : (
        <FlatList
          data={data}
          keyExtractor={(item: any, index) => item._id}
          refreshing={isFetching}
          onRefresh={onRefresh}
          onViewableItemsChanged={({ viewableItems }) => {
            // Prefetch data for visible topics
            viewableItems.forEach((viewableItem) => {
              if (viewableItem.item && onTopicVisible) {
                onTopicVisible(viewableItem.item._id);
              }
            });
          }}
          viewabilityConfig={{
            itemVisiblePercentThreshold: 50, // Trigger when 50% of item is visible
          }}
          renderItem={({ item }) => {
            return <TopicCard
              item={item}
              navigation={navigation}
              convo={false}
              refreshFunction={onRefresh}
              onToggleMuted={function (): void {
                throw new Error('Function not implemented.');
              }}
              muted={false}
            />;
          }}
        />
      )}

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={onNavigateToPost}
        style={styles.touchableOpacityStyle}
      >
        <FloatingAddSVG
          width={rv(45)}
          height={rv(45)}
        />
      </TouchableOpacity>
    </View>
  );
};

// Export container as default
export default TopicContainer;
