import React, { useState } from 'react';
import {
  View,
  Text,
 
  TouchableOpacity,
  Image,
  StyleSheet,
  TextInput,
  Platform,
  ScrollView,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { CustomText } from 'app/components/elements';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useGetCredit } from 'app/redux/credit/hook';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import UltimateGameMode from './ultimateGame';
import { useNavigation } from '@react-navigation/native';
import {
  useGetQuestions,
  useChangeUserPennytots,
  useStakeUserPennytots,
} from 'app/redux/quiz/hook';
import {
  BulbSVG,
  HelpdeskSVG,
  NextArrowSVG,
  GameModeSVG,
  BuyCreditSVG,
} from 'app/providers/svg/loader';
import CustomModal from 'app/components/elements/Modals';
import Ionicons from '@expo/vector-icons/Ionicons';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { styles } from 'app/components/Button';

const UltimateChallenge = () => {
  const { data: credits } = useGetCredit();
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const { mutate: stakeUserPennytots } = useStakeUserPennytots();
  const { t } = useTranslation()

  const handleNavigateToUltimateGame = () => {
    if (credits?.amount < 10000) {
      // If not enough credits, show the modal and do not navigate
      setModalVisible(true);
      return;
    }
    navigation.navigate('UltimateGameMode' as never, { text: 10000 });
  };

  const NavigateToCreditPage = () => {
    setModalVisible(!modalVisible);
    navigation.navigate('Credit' as never);
  };

  const handleNavigateToChallengeMode = () => {
    // You can do some validation here before navigating
    navigation.navigate('QuizScreen' as never); // Passing the 'text' parameter
  };

  const handleEndChallenge = async () => {
    try {
      await stakeUserPennytots('reduce', 10000);
      navigation.navigate('QuizScreen' as never);
    } catch (error) {
      console.error('Error reducing Pennytots:', error);
      // Handle any errors, perhaps set an error state or show an alert
    }
  };
  return (
    <SafeAreaView
      style={{
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        paddingHorizontal: rv(6)
      }}
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1, alignItems: 'center' }}>
        {modalVisible && (
          <CustomModal
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
            NavigateToCreditPage={NavigateToCreditPage}
            type='ultimate' // or "stake" based on the scenario
            handleConfirm={NavigateToCreditPage}
            navigation={navigation}
          />
        )}
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'space-between',
            width: '90%',
            marginTop: Platform.OS === 'android' ? rv(30) : rv(5),
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <TouchableOpacity
              style={{ flexDirection: 'row', alignItems: 'center' }}
              onPress={handleNavigateToChallengeMode}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }} // Expands the touchable area
            >
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: rv(20),
                }}
              >
                <Ionicons name='chevron-back' size={22} color='#696969' />
                <CustomText
                  style={{
                    fontSize: rv(13),
                    fontFamily: 'semiBold',
                    fontWeight: '100',
                    paddingLeft: rv(5.5),
                    color: '#696969',
                  }}
                >
                  {t("back_to_game_mode")}
                </CustomText>
              </View>
            </TouchableOpacity>

            <View style={{ flexDirection: 'row', marginTop: rv(20) }}>
              <Text
                style={{
                  color: '#696969',
                  fontSize: rv(13),
                  fontFamily: 'semiBold',
                  fontWeight: '100',
                  paddingLeft: rv(5.5),
                }}
              >
                {credits?.amount}
              </Text>
              <Text
                style={{
                  color: '#696969',
                  fontSize: rv(13),
                  fontFamily: 'semiBold',
                  fontWeight: '100',
                  paddingLeft: rv(5.5),
                }}
              >
              {t("pennytots")}
              </Text>
            </View>
          </View>
        </View>

        <View style={{ alignItems: 'center' }}>
          <Text
            style={{
              marginTop: rv(27),
              fontFamily: 'semiBold',
              fontSize: rv(21),
              color: '#797978',
            }}
          >
           {t("ultimate_challenge")}
          </Text>
          <Image 
            style={{ marginTop: rv(3),}}
            source={require(`app/assets/Pennytots_gold.png`)}
          />
        </View>
        <CustomText
          style={{
            fontSize: rv(13),
            color: '#696969',
            fontFamily: 'medium',
            
          }}
        >
          {t("stake_10000_pennytots_for_a_ch")}
        </CustomText>
        {/* <Text
          style={{
            fontSize: rv(18),
            // fontWeight: 'bold',
            fontFamily: 'semiBold',

            marginTop: rv(12),
            alignItems: 'center',
            textAlign: 'center',
            color: '#F19C4A',
          }}
        >
          1 Million Naira
        </Text> */}
        <CustomText
          style={{
            fontSize: rv(13),
            width: '95%',
            color: '#FFB61D',
            fontFamily: 'medium',
            marginTop: rv(15),
            textAlign: 'center'
          }}
        >
        {t("24karat_1_ounce_custommade_pur")}
        </CustomText>
        <CustomText
          style={{
            width: '95%',
            textAlign: 'center',
            fontSize: rv(13),
            fontFamily: 'medium',
            marginTop: rv(17),
            color: '#696969',
          }}
        >
         {t("challenge_instructions")}{' '}
        </CustomText>
        <Text
          style={{
            color: '#696969',
            marginTop: rv(24),
            fontFamily: 'medium',
            fontSize: rv(13),
          }}
        >
         {t("ready_to_play")}
        </Text>
        <TouchableOpacity
          onPress={handleNavigateToUltimateGame}
          style={{
            backgroundColor: '#FED830',
            width: '85%',
            height: rv(48),
            paddingVertical: rv(8),
            paddingHorizontal: rv(12),
            justifyContent: 'center', // Center the content vertically
            alignItems: 'center',
            borderRadius: rv(32),
            marginTop: rv(41),
          }}
        >
          <CustomText
            style={{
              color: '#48463E',
              fontSize: rv(16),
              fontWeight: 'bold',
              lineHeight: rv(24),
              fontFamily: 'semiBold',
            }}
          >
           {t("play")}
          </CustomText>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

// const styles = StyleSheet.create({
//   text:{
//     fontSize: 14,
//     color: '#F6CF20',
//     fontWeight: 'bold',
//     marginTop: rv(22)
//   }
// })

export default UltimateChallenge;
