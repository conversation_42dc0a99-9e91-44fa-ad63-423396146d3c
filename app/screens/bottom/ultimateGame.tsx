import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  Button,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';
import axios from 'axios';
import * as Animatable from 'react-native-animatable';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslation } from "react-i18next";

import {
  useGetCredit,
  useGetCreditTransactionHistory,
} from 'app/redux/credit/hook';
import { SafeAreaView } from 'react-native-safe-area-context';
import FloatingAddSVG from 'app/assets/svg/next.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Animation from 'app/components/animation';
import {
  useGetQuestions,
  useChangeUserPennytots,
  useStakeUserPennytots,
} from 'app/redux/quiz/hook';
import { AppState } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { BackHandler } from 'react-native';
import CustomModal from 'app/components/elements/Modals';
import { CustomText } from 'app/components/elements';
// import { Date } from "expo-contacts";

const Correct = require('../../assets/gifs/correct1.gif');
// const Wrong1 = require('app/assets/times-up.png');
const Wrong2 = require('app/assets/gifs/wrong2.gif');
const Wrong3 = require('../../assets/gifs/angry-furious.gif');
const Wrong4 = require('../../assets/gifs/bad-hair.gif');
const Wrong5 = require('../../assets/gifs/bernie-mac-nervous.gif');
const Wrong6 = require('../../assets/gifs/well-damn.gif');
const Wrong7 = require('../../assets/gifs/martin.gif');
const Wrong8 = require('../../assets/gifs/no-chris-rock.gif');
const Wrong9 = require('../../assets/gifs/no-machaizelli-kahey.gif');
const Wrong10 = require('../../assets/gifs/wow-amazing.gif');
const Wrong11 = require('../../assets/gifs/no-nope-nope-nope.gif');
const Wrong12 = require('../../assets/gifs/no-richard-lane.gif');
const Wrong13 = require('../../assets/gifs/nope-kenan-thompson.gif');
const Wrong14 = require('../../assets/gifs/nope-terry-jeffords.gif');
const Wrong15 = require('../../assets/gifs/oh-come-on-captain-ray-holt.gif');
const Wrong16 = require('../../assets/gifs/oh-no-jeremy.gif');
const Wrong17 = require('../../assets/gifs/orange-is-the-new-black-oitnb.gif');

const RandomImageAnimation = ({ images }: { images: any[] }) => {

  // If 'images' prop is falsy or empty, return null to not render anything
  if (!images || images.length === 0) {
    return null;
  }

  // Select a random image from the array
  const randomIndex = Math.floor(Math.random() * images.length);
  const selectedImage = images[randomIndex];
};

const mockQuizData = {
  question: 'Who is known as the pioneer of Afrobeat music in Nigeria??',
  options: ['Ebenezer Obey', 'Fela Kuti', 'Ebenezer Obey', 'Ebenezer Obey'],
  correctAnswer: 'Fela Kuti',
};

const OptionButton = ({
  option,
  onPress,
  isCorrect,
  isSelected,
  correctAnswer,
  canClick, // New prop to control when to show the correct answer
  gameOver,
  style,
}: any) => {
  const colors = [
    {
      background: '#FFC085',
      backgroundColor: '#FED830',
    },
    {
      background: '#FFFFFF',
      backgroundColor: '#000000',
    },
    // Add more color combinations here
  ];


  const [currentColor, setCurrentColor] = useState(colors[0]);
  let randomIndex = 0;
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (randomIndex === 0) {
        randomIndex = 1;
      } else {
        randomIndex = 0;
      }
      setCurrentColor(colors[randomIndex]);
    }, 500);

    return () => clearInterval(intervalId);
  }, [randomIndex]);
  return (
    <TouchableOpacity
      onPress={() => {
        // Call the onPress function provided in the props
        onPress();
      }}
      style={{
        backgroundColor:
          isCorrect && isSelected // Show correct answer color if the option is correct and either selected or showAnswer is true
            ? currentColor.background
            : isSelected
            ? '#48463E'
            : isCorrect !== null && option === correctAnswer
            ? currentColor.background
            : '#FED830',
        height: rv(50),
        borderRadius: rv(24),
        padding: rv(5),
        paddingHorizontal: rv(30),
        alignItems: 'center',
        justifyContent: 'center',
        margin: rv(3),
        marginBottom:rv(20),
        marginRight: rv(4),
        opacity: gameOver ? 0.5 : isCorrect === null ? 1 : isSelected ? 1 : isCorrect !== null && option===correctAnswer ? 1: 0.5,
        ...style,
      }}
      disabled={isCorrect !== null || gameOver}
    >
      <Text
        style={{
          color:
            (isCorrect && (isSelected))
              ? '#48463E'
              : isSelected
              ? 'white'
              : '#48463E',
          textAlign: 'center',
          fontSize: rv(11),
          fontWeight: '600',
        }}
      >
        {option}
      </Text>
    </TouchableOpacity>
  );
};

const UltimateGame = () => {
  const { t } = useTranslation();
  const [questionNumber, setQuestionNumber] = useState(1);
  const route = useRoute();
  // Access the 'text' parameter from the route params
  const { text: stakedAmount } = route.params;
  const {
    data: quizData,
    isLoading,
    isFetching,
    refetch: getNextQuestion,
  } = useGetQuestions();
  const { mutate: changeUserPennytots } = useChangeUserPennytots();
  const Loading = require('../../assets/loading.gif');
  const { mutate: stakeUserPennytots } = useStakeUserPennytots();
  const [selectedOption, setSelectedOption] = useState(null);
  const [goToNext, setGoToNext] = useState(false);
  const [score, setScore] = useState(0);
  const [isCorrect, setIsCorrect] = useState<any>(null);
  const { data: credits, refetch } = useGetCredit();
  const [isCurrentSelectionCorrect, setIsCurrentSelectionCorrect] =
    useState<any>(null);
  const [showScoreMessage, setShowScoreMessage] = useState(false);
  const [image, setImage] = useState(null);
  const { question, options, answer: correctAnswer } = quizData || {};
  const [end, setEnd] = useState(false);
  const [timeLeft, setTimeLeft] = useState(600);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [appState, setAppState] = useState(AppState.currentState);
  const [gameOver, setGameOver] = useState(false);
  const [gameWin, setGameWin] = useState(false);
  const [questionCount, setQuestionCount] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const [isButtonClicked, setButtonClicked] = useState(false);

  const getRandomWrongImage = () => {
    const images = [
      Wrong3,
      Wrong4,
      Wrong5,
      Wrong6,
      Wrong7,
      Wrong8,
      Wrong9,
      Wrong10,
      Wrong11,
      Wrong12,
      Wrong13,
      Wrong14,
      Wrong15,
      Wrong16,
      Wrong17,
    ];
    const randomIndex = Math.floor(Math.random() * images.length);
    return images[randomIndex];
  };

  useEffect(() => {
    if (timeLeft === 0) {
      setGameOver(true);
      stakeUserPennytots('reduce', stakedAmount);
    }
  }, [timeLeft]);

  useEffect(() => {
    if (isCurrentSelectionCorrect === false) {
      setGameOver(true);
      stakeUserPennytots('reduce', stakedAmount);
    }
  }, [isCurrentSelectionCorrect]);

  // useEffect(() => {
  //   const timer = setInterval(() => {
  //     setTimeLeft((prevTime) => {
  //       if (prevTime === 0 || gameOver || gameWin) {
  //         clearInterval(timer); // Stop the timer when time reaches 0 or gameOver is true
  //         return prevTime;
  //       }
  //       return prevTime - 1;
  //     });
  //   }, 1000);

  //   // Cleanup function to clear the interval when the component unmounts or when the game is over
  //   return () => clearInterval(timer);
  // }, [gameOver, gameWin]); // Include gameOver in the dependency array

  // useEffect(() => {
  //   setStartTime(Date.now());
  //   // const subscription = AppState.addEventListener("change", nextAppState => {
  //   //   if (appState.match(/inactive|background/) && nextAppState === "active") {
  //   //     // App has come to the foreground, recalculate time left
  //   //     // if (startTime) {
  //   //     //   const currentTime = Date.now();
  //   //     //   const timePassedInSeconds = Math.floor((currentTime - startTime) / 1000);
  //   //     //   const newTimeLeft = Math.max(120 - timePassedInSeconds, 0);
  //   //     //   setTimeLeft(newTimeLeft);
  //   //     //   if (newTimeLeft <= 0) {
  //   //     //     // Handle game over due to timeout
  //   //     //   }
  //   //     // }
  //   //   }
  //   //   // setAppState(nextAppState);
  //   // });

  //   // return () => {
  //   //   subscription.remove();
  //   // };
  // }, [appState, startTime]);

  useEffect(() => {
    setStartTime(Date.now());
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime = Date.now();
      // Ensure startTime is defined and not null before calculating timePassedInSeconds
      if (startTime) {
        const timePassedInSeconds = Math.floor(
          (currentTime - startTime) / 1000
        );
        const newTimeLeft = Math.max(600 - timePassedInSeconds, 0); // Adjusted to 1500 seconds (25 minutes)
        setTimeLeft(newTimeLeft);
      }

      if (timeLeft === 0 || gameOver || gameWin) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [gameOver, gameWin, startTime]);

  // Effect to navigate when time is up
  useEffect(() => {
    if (timeLeft === 0) {
      setImage(Wrong2); // Assuming Wrong1 is the image you want to display
      setShowScoreMessage(true);
    }
  }, [timeLeft]);

  useFocusEffect(
    React.useCallback(() => {
      getNextQuestion();

      return () => {
        // Optional: Any cleanup code goes here
      };
    }, [])
  );

  const formatTimeLeft = () => {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  const navigation = useNavigation();
  const handleNavigateToQuiz = () => {
    if (gameOver) {
      return; // Exit the function early if gameOver is true
    }
    setModalVisible(true);
  };
  const handleNavigateChallenge = () => {
    navigation.navigate('QuizScreen' as never); // Ensure that 'gameWin' is passed correctly
  };
  // function to get random image
  const getRandomImage = () => {
    const images = [Wrong2];
  };

  useEffect(() => {
    if (questionCount >= 49) {
      setGameWin(true);
    }
  }, [questionCount, isCurrentSelectionCorrect]);

  useEffect(() => {
    if (gameWin) {
      // stakeUserPennytots('increase', stakedAmount);
      navigation.navigate('UltimateCongrats' as never);
    }
  }, [gameWin]);

  // useEffect(() => {
  //   const updateCredits = async () => {

  // }, [gameWin, credits, refetch]);

  const handleOptionSelect = async (option: any) => {
    // getNextQuestion(); // Fetch the next question
    // if (timeLeft === 0) {
    //   return;
    // }
    setSelectedOption(option);
    const isCorrect = option === correctAnswer;

    // If the selected option is correct, increase the question number by 1
    // if (isCorrect) {
    //   setQuestionNumber((prevQuestionNumber) => prevQuestionNumber + 1);
    // }
    // Submit the selected option to the backend
    try {
      const isAnswerCorrect = option === correctAnswer;
      setIsCorrect(isAnswerCorrect);

      setEnd(true);
      setShowScoreMessage(true);
      setTimeout(() => {
        setShowScoreMessage(false);
      }, 2500);
      if (isAnswerCorrect) {
        setGoToNext(true);
        setImage(Correct);
        setQuestionCount((prevCount) => prevCount + 1);
        setIsCurrentSelectionCorrect(true);
      } else {
        setIsCurrentSelectionCorrect(false);
        setImage(getRandomWrongImage());
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      setIsCurrentSelectionCorrect(true);
    }
  };
  const handleImageError = (error: any) => {
    console.error('Error loading image:', error);
    // Handle the error, such as displaying a placeholder image or showing an error message
  };

  const handleNextQuestion = () => {
    if (questionCount > 49 && isCurrentSelectionCorrect) {
      return;
    }
    if (timeLeft === 0) {
      return;
    }
    setQuestionNumber((prevQuestionNumber) => prevQuestionNumber + 1);
    
    setGoToNext(false);
    getNextQuestion(); // Refetch the mock data, replace it with your API call
    setEnd(false);
    setSelectedOption(null);
    setIsCorrect(null);
    setIsCurrentSelectionCorrect(null);
  };
  const handleEndChallenge = async () => {
    if (isButtonClicked) return; // Prevent function if already clicked
    setButtonClicked(true); // Set the flag to true on the first click
    try {
      await stakeUserPennytots('reduce', stakedAmount); // Assuming stakeUserPennytots is an async function
      navigation.navigate('Credit' as never);
    } catch (error) {
      console.error('Error reducing Pennytots:', error);
      // Handle any errors, perhaps set an error state or show an alert
    }
  };
  const handleNavigateToUltimateCongrat = () => {
    navigation.navigate('UltimateCongrats' as never);
  };

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: 'white', paddingHorizontal: rv(20) }}
    >
      {modalVisible && (
        <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type='stake'
          handleConfirm={handleEndChallenge}
          navigation
        />
      )}

      <View
        style={{
          flexDirection: 'column',
          justifyContent: 'space-between',
          marginTop: rv(20),
        }}
      >
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <TouchableOpacity
            onPress={handleNavigateToQuiz}
            style={{ flexDirection: 'row', alignItems: 'center' }}
          >
            <Image
              source={require('app/assets/Vector.png')}
              style={{ width: rv(24), height: rv(24), marginRight: rv(6) }}
            />
            <CustomText
              style={{
                fontSize: rv(13),
                fontWeight: '600',
                color: '#4F4F4F',
                paddingLeft: rv(4),
              }}
            >
            {t("end_challenge")}
            </CustomText>
          </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text
                style={{
                  fontSize: rv(13),
                  fontWeight: '600',
                  color: '#696969',
                }}
              >
                {stakedAmount} {t("staked")}
              </Text>
            </View>
        </View>
        <View style={{ flexDirection: 'column' }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginTop: rv(30),
            }}
          >
            <Text
              style={{
                fontSize: rv(14),
                fontWeight: 'bold',
                lineHeight: rv(20),
              }}
            >
             {t("question")} {questionNumber}
            </Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Image
                source={require('app/assets/timer.png')}
                style={{ width: rv(19), height: rv(19), marginRight: 8 }}
              />
              <Text
                style={{
                  fontWeight: 'bold',
                  fontSize: rv(14),
                  color: '#FED830',
                }}
              >
                {formatTimeLeft()}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* {questionCount <= 29 && (
      <View>
        {isLoading && <Text>Loading...</Text>}
        {!isLoading && <Text style={{fontSize:rv(11.5),marginTop: rv(30), fontStyle:'normal', fontWeight: '700', color: '#696969'}}>{quizData?.question}</Text>}
      </View>)
      } */}

      {isFetching && questionCount <= 50 && (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignContent: 'center',
          }}
        >
          {isFetching && (
            <Image source={Loading} style={{ width: rv(50), height: rv(50) }} />
          )}
          {/* Your content here */}
        </View>
      )}
      {!isFetching && (
        <View style={{ }}>
          <Text
            style={{
              marginTop: rv(25),
              fontSize: rv(11.5),
              fontWeight: '700',
              fontFamily: 'semiBold',
              color: '#696969',
            }}
          >
            {quizData?.question}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'space-between',
              marginTop: rv(20),
            }}
          >
            {options?.map((option, index) => (
              <OptionButton
                key={index}
                option={option}
                onPress={() => handleOptionSelect(option)}
                isSelected={selectedOption === option}
                isCorrect={isCurrentSelectionCorrect}
                correctAnswer={correctAnswer}
                style={{ width: '45%' }} // Adjust the width as needed
              />
            ))}
          </View>
        </View>
      )}
      {isCurrentSelectionCorrect && !gameOver ? (
        <View style={styles.container}>
          <Image source={Correct} style={{ width: rv(200), height: rv(200) }} />
        </View>
      ) : null}
      {gameOver ? (
        <View style={{ alignItems: 'center', justifyContent: 'center' }}>
          <CustomText
            style={{
              fontSize:rv(16),
              fontFamily:'regular',
              marginTop:rv(40)
            }}
          >
           { t ("uh_oh") }
          </CustomText>
          <CustomText
            style={{
              marginTop:rv(10),
              fontFamily:'regular',
              fontSize:rv(14),
              alignItems: 'center',
              textAlign:'center',
              color:'#4F4F4F'
            }}
          >
           {t("dont_worry_every_pennytots_cha")}
          </CustomText>
          <CustomText
            style={{
              fontSize: rv(16),
              fontFamily: 'semiBold',
              color: '#000',
              lineHeight: rv(26),
              marginTop: rv(15),
              textAlign:'center'
            }}
          >
            
            {t("the_ultimate_challenge_awaits_")}
          </CustomText>
          {/* Render a button to continue */}
          <TouchableOpacity
            onPress={handleNavigateChallenge}
            style={{ marginTop: rv(32) }}
          >
            <View
              style={{
                backgroundColor: '#FED830',
                borderRadius: 32,
                width: rv(267),
                paddingVertical: rv(12),
                paddingHorizontal: rv(8),
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <CustomText
                style={{
                  fontSize: rv(14),
                  color: '#48463E',
                  lineHeight: rv(24),
                  fontFamily: 'regular',
                }}
              >
                {t("back_to_game_mode")}
              </CustomText>
            </View>
          </TouchableOpacity>
        </View>
      ) : null}
      {/* Remove the Animation component */}
      {!gameWin && goToNext && (
        <TouchableOpacity
          style={{
            position: 'absolute',
            bottom: rv(20),
            right: rv(20),
          }}
          onPress={handleNextQuestion}
        >
          <View
            style={{
              padding: rv(18),
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#FED830',
              width: rv(112),
              height: rv(54),
              borderRadius: 32,
            }}
          >
            <Image
              source={require('app/assets/arrow.png')}
              style={{ width: rv(30), height: rv(22), marginTop: rv(10) }}
            />
            <Text style={{ fontFamily: 'semiBold', fontSize: rv(13) }}>
              {t("next")}
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    marginTop: rv(25)
  },
  container2: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageStyle: {
    marginTop: rv(47),
    width: rv(210), // Set width as needed
    height: rv(156), // Set height as needed
    // Add more styles as needed
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
});

export default UltimateGame;
