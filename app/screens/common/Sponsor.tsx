// SponsorAdScreen.js
import { useActivity } from 'app/providers/ActivityContext';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import React, { useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ThumbSVG from 'app/assets/svg/Container.svg';
import { createOptimizedThemedStyles } from 'app/theme';
import { useTheme } from 'app/hooks/useTheme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

// Import the sample image from assets/images
const sampleAdImage = require('app/assets/images/sponsored.jpg');

const SponsorAdScreen = ({ navigation, route }: any) => {
    const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const { sponsoredAd, isAdLoading, resetModalVisibility } = useActivity();
  console.log(sponsoredAd, "ibkzzz");

  // Reset modal visibility when the screen unmounts
  useEffect(() => {
    return () => {
      resetModalVisibility('sponsor');
    };
  }, []);

  if (isAdLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Loading Ad...</Text>
      </SafeAreaView>
    );
  }

  // Use sponsoredAd if available; otherwise, use sampleAdImage
  const adSource =
    sponsoredAd && sponsoredAd.image
      ? { uri: sponsoredAd.image }
      : sampleAdImage;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with hashtag and X button */}
      <View style={styles.header}>
        <Text style={styles.hashtag}>#Sponsored post</Text>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ThumbSVG width={14} height={14} />
        </TouchableOpacity>
      </View>

      {/* Content Container: Contains the ad image and the close button with equal spacers */}
      <View style={styles.contentContainer}>
        
        <Image source={adSource} style={styles.adImage} resizeMode="contain" />

        {/* Spacer above button */}
        <View style={styles.spacer} />

        {/* Close Ad Button */}
        <TouchableOpacity
          style={styles.closeAdButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.closeAdButtonText}>Close Ad</Text>
        </TouchableOpacity>

        {/* Spacer below button */}
        <View style={styles.spacer} />
      </View>
    </SafeAreaView>
  );
};

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '90%',
    alignSelf: 'center',
    marginVertical: rv(10),
  },
  hashtag: {
    fontSize: fs("LG"),
    fontFamily: 'semiBold',
    color: '#000',
  },
  // contentContainer fills the remaining space below the header
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: '2.5%', // ensures the image is nearly full width (95% width)
  },
  // Set the ad image to 95% of the container's width and a fixed height
  adImage: {
    marginTop: rv(100),
    width: '95%',
    height: rv(400), // adjust as needed
  },
  // spacer view with flex: 1 will take equal space above and belPow the button
  spacer: {
    flex: 1,
  },
  closeAdButton: {
    backgroundColor: '#163E23',
    paddingVertical: 10,
    paddingHorizontal: 30,
    borderRadius: 5,
    alignSelf: 'center',
  },
  closeAdButtonText: {
    fontSize: fs("BASE"),
    color: '#fff',
    fontFamily: 'medium',
  },
}));

export default SponsorAdScreen;
