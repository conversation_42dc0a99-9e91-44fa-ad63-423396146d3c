import React, { useState, useEffect, useContext } from 'react';
import {
  Text,
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  StyleSheet,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Ionicons from '@expo/vector-icons/Ionicons';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import { CustomText, CustomButton } from 'app/components/elements';
import { CommonStyles } from 'app/assets/styles';
import HeaderTitle from 'app/components/HeaderTitle';
import {
  useGetHelpDeskCategories,
  useCreateTicket,
} from 'app/redux/helpdesk/hooks';
import SelectDropdown from 'react-native-select-dropdown';
import { useTranslation } from 'react-i18next';
import { quiz } from 'app/api';
import CustomModal from 'app/components/elements/Modals';
import {
  useGetQuestions,
  useChangeUserPennytots,
  useStakeUserPennytots,
} from 'app/redux/quiz/hook';
import { responsiveValue as rv } from 'app/providers/responsive-value';

type HelpDeskScreenProps = {
  navigation: any;
};

const CashOutForm: React.FC<HelpDeskScreenProps> = ({ navigation }) => {
  const [category, setCategory] = useState('Payment');
  const [title, setTitle] = useState('Cash Out Pennytot');
  const [content, setContent] = useState('');
  const { data: categories } = useGetHelpDeskCategories();
  const { mutateAsync: createTicket, isLoading } = useCreateTicket();
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);


 
  
  async function submit() {
    // Update the filledForm state with the current values of the input fields
    await quiz.stakeUserPennyTots('reduce', 1000);
    setModalVisible(true)
    let payload = {
      title,
      category,
      message: content,
    };
    createTicket(payload).then((response: any) => {
      setContent('');
      setTitle('');
      navigation.goBack();

      // Navigate back to the previous screen
    });
  }
  


  return (
    <SafeAreaView style={{ backgroundColor: '#fff' }}>
      <ScrollView style={{ backgroundColor: '#ffffff' }}>
        <StatusBar barStyle='dark-content' backgroundColor='white' />
        <HeaderTitle title='Cash Out Form' navigation={navigation} />
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
            {modalVisible && (
        <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type="cashout"
          navigation
        />
      )}
          <View style={{ width: '87%' }}>
            <CustomText
              style={{
                fontSize: 15,
                fontFamily: 'medium'
              }}
            >
              {t("You'll be given the cash equivalent of 50,000 pennytots")}
            </CustomText>

            <CustomText
              style={{
                marginTop: 20,
                fontSize: 15,
                fontFamily: 'semiBold'
              }}
              // textType='semi-bold'
            >
              {t('HelpDesk_category')}
            </CustomText>

            <View
              style={{
                ...CommonStyles.inputField,
                width: '100%',
                justifyContent: 'center',
                padding: 10, // Adjust padding as needed
              }}
            >
              <Text
                style={{
                  fontFamily: 'regular',
                                    fontSize: 14,
                  color: '#696969',
                }}
              >
               {t("Payment")}
              </Text>
            </View>
     
            {/* <CustomText
              style={{
                marginTop: 10,
                fontSize: 15,
              }}
              textType='semi-bold'
            >
              {t('HelpDesk_messageTitle')}
            </CustomText> */}
            <Text  style={{
                marginTop: 10,
                fontSize: 15,
                fontFamily: 'semiBold'
                              }}>{t("Topic")}</Text>
            <Text style={{...styles.signup}}>{t("Cash Out Pennytot")}</Text>  

            <CustomText
              style={{
                marginTop: rv(10),
                fontSize: rv(15),
                fontFamily: 'semiBold'
              }}
              // textType='semi-bold'
            >
              {t('HelpDesk_details')}
            </CustomText>

            <TextInput
              multiline={true}
              numberOfLines={10}
              placeholder='Kindly enter your account name, account number and bank name here'
              placeholderTextColor='grey'
              // value={content!}
              style={{
                backgroundColor: '#F5F5F5',
                height: hp('35%'),
                textAlignVertical: 'top',
                fontFamily: 'regular',
                fontSize: rv(14),
                color: '#636363',
                lineHeight: 20,
                fontWeight: '500',
                paddingHorizontal: 10,
                paddingVertical: 24,
                borderRadius: 10,
              }}
              underlineColorAndroid='transparent'
              onChangeText={(contents) => setContent(contents)}
              maxLength={200}
            />

            <View
              style={{
                width: wp('90'),
                marginTop: 30,
              }}
            >
              {/* <CustomButton
              style={{backgroundColor: '', color: '#48463E'}}
                label={t('HelpDesk_sendBtn')}
                onPress={() => submit()}
                loading={isLoading}
                buttonTheme='secondary'
              /> */}
              <TouchableOpacity
              style={{backgroundColor: '#163E23', borderRadius: 24, height: 48, alignItems: 'center', justifyContent:'center', width: '97%'}}
              onPress={submit}>
                
                <Text style={{color: '#48463E', fontFamily: 'medium', fontSize: 16}}>{t("send")}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  signup: {
    height: 60,
    paddingLeft: 20,
    paddingTop: 20,
    backgroundColor: '#f4f4f4',
    marginBottom: 15,
    borderStyle: 'solid',
    borderRadius: 14,
    marginTop: 10,
    fontWeight: '500',
    fontSize: 14,
    color: '#696969',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signupamount:{
    height: 60,
    paddingLeft: 20,
    backgroundColor: '#f4f4f4',
    marginBottom: 15,
    borderStyle: 'solid',
    borderRadius: 14,
    marginTop: 10,
    fontWeight: '500',
    fontSize: 14,
    color: '#696969',
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default CashOutForm;
