import { View, Text, ImageBackground, Image } from 'react-native';
import React, { useEffect, useState, useCallback } from 'react';
import HeaderTitle from 'app/components/HeaderTitle';
import { TouchableOpacity } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { useFocusEffect } from '@react-navigation/native';
import { UploadPhotoSVG } from 'app/providers/svg/loader';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { CustomButton, CustomText } from 'app/components/elements';
import { TextInput } from 'react-native';
import { CommonStyles } from 'app/assets/styles';
import { CreateGroupSVG, WarnInfoSVG } from 'app/providers/svg/loader';
import SelectInterests from 'app/components/elements/SelectInterests';
import { useUpdateGroup, useUpdateGroupPicture } from 'app/redux/group/hooks';
import { ScrollView } from 'react-native';
import SelectImageDialog from 'app/components/dialogs/select-image';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';
import { hideModal, showModal } from 'app/providers/modals';
import { useTheme } from 'app/hooks/useTheme';
import { createScalableThemedStyles } from 'app/utils/scalableResponsiveValue';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
import { createOptimizedThemedStyles } from 'app/theme';
const Loading = require('../../assets/loading.gif');

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
    flexDirection: 'column',
    paddingHorizontal: wp('8%'),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    height: rv(150),
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageBackground: {
    justifyContent: 'center',
    alignItems: 'center',
    width: rv(120),
    height: rv(120),
  },
  imageStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    height: rv(120),
    width: rv(120),
    borderRadius: rv(100),
  },
  imageOverlay: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
    borderRadius: rv(100),
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  inputSection: {
    marginTop: 40,
    flexDirection: 'column',
  },
  inputSectionNoMargin: {
    flexDirection: 'column',
  },
  labelText: {
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    color: theme.colors.textSecondary,
    marginBottom: 5,
    fontFamily: 'medium'
  },
  tagSection: {
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagText: {
    fontSize: 14,
    marginRight: 10,
    marginTop: 5,
    color: theme.colors.textSecondary,
    fontFamily: 'bold'
  },
  buttonSection: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 50,
  },
}));

const EditGroupScreen = ({ navigation, route }: any) => {
  const { group } = route.params;
  const [showSelectGroupImage, setShowSelectGroupImage] = useState(false);
  const [groupDesc, setGroupDesc] = useState('');
  const [groupName, setGroupName] = useState('');
  const [interest, setInterest] = useState('');
  const { mutateAsync: update, isLoading: loading } = useUpdateGroup();
  const [imageFile, setImageFile] = useState<any>(null);
  const { mutateAsync: updateProfilePicture, isLoading: imageLoading } =
    useUpdateGroupPicture();
  const { t } = useTranslation();
    const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);


  useEffect(() => {
    if (group) {
      setGroupDesc(group.description);
      setGroupName(group.name);
      setInterest(group.tag._id);
    }
  }, [group]);

  async function updateGroup() {
    let payload = {
      id: group._id,
      data: {
        name: groupName,
        description: groupDesc,
        tag: interest,
      },
    };

    update(payload).then(() => {
      navigation.goBack();
    });
    if (interest.length === 0) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'You need to select a tag for this post',
        setModalVisible: hideModal, // Function to close the modal
        type: 'alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },

      });
    }
  }

  useEffect(() => {

    if (imageFile && imageFile.uri) {

      const pic = new FormData();

      pic.append('file', imageFile);
      let payload = {
        id: group._id,
        data: pic,
      };

      updateProfilePicture(payload).then(() => {
        // navigation.goBack();
      });
    }
  }, [imageFile]);

  if (!group) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <SelectImageDialog
        show={showSelectGroupImage}
        setShow={setShowSelectGroupImage}
        setImage={setImageFile}
      />

      <HeaderTitle title={t('Edit Group')} navigation={navigation} />
      <ScrollView style={styles.scrollView}>
        {imageLoading && (
          <View style={styles.loadingContainer}>
            <Image
              source={Loading}
              style={{
                width: rv(50),
                height: rv(50),
              }}
            />
          </View>
        )}

        <View style={styles.imageContainer}>
          <ImageBackground
            source={
              group.image
                ? { uri: group.image }
                : require('app/assets/connectify-thumbnail.png')
            }
            style={styles.imageBackground}
            imageStyle={styles.imageStyle}
          >
            <TouchableOpacity
              onPress={() => setShowSelectGroupImage(true)}
              style={styles.imageOverlay}
            >
              <UploadPhotoSVG width={wp('12')} height={wp('12')} />
            </TouchableOpacity>
          </ImageBackground>
        </View>


        <View style={styles.inputSection}>
          <CustomText style={styles.labelText}>
            {t('CreateGroup_groupName')}{' '}
          </CustomText>

          <TextInput
            style={CommonStyles.inputField}
            maxLength={20}
            onChangeText={(text) => {
              setGroupName(text);
            }}
            placeholder={t('CreateGroup_groupName')}
            inlineImagePadding={20}
            value={groupName}
          />
        </View>

        <View style={styles.inputSectionNoMargin}>
          <CustomText style={styles.labelText}>
            {t('CreateGroup_Description')}{' '}
          </CustomText>

          <TextInput
            style={CommonStyles.inputField}
            maxLength={70}
            onChangeText={(text) => {
              setGroupDesc(text);
            }}
            placeholder={t('GroupDesc_description')}
            inlineImagePadding={20}
            value={groupDesc}
          />
        </View>

        <View style={styles.tagSection}>
          <CustomText style={styles.tagText}>
            Select a tag relevant to this group
          </CustomText>

          <WarnInfoSVG />
        </View>
        <SelectInterests interest={interest} setInterest={setInterest} />

        <View style={styles.buttonSection}>
          <CustomButton
            label={'Update Group'}
            onPress={() => updateGroup()}
            buttonTheme='primary'
            loading={loading}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default EditGroupScreen;
