import React, { useContext, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  TextInput,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  FlatList,
  Text,
  Modal,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { responsiveValue as rv } from "app/providers/responsive-value";
import { CustomButton, CustomText } from "app/components/elements";
import Loader from "app/components/elements/Loader";
import { UploadPhotoSVG } from "app/providers/svg/loader";
import SelectImageDialog from "app/components/dialogs/select-image";
import { createCommonStyles, useCommonStyles } from "app/assets/styles";
import { Axios } from "app/api/axios";
import { ShowAlert } from "app/providers/toast";
import { updateUserInfo } from "app/redux/user/hooks";
import { useSelector } from "react-redux";
import { userAuthInfo } from "app/redux/user/reducer";
import { userConstants } from "../../constants/user";
import AgeSVG from "app/assets/svg/edit-ageIcon.svg";
import UserNameSVG from "app/assets/svg/user.svg";
import UserSVG from "app/assets/svg/positionCompany.svg";
import LocationSVG from "app/assets/svg/townState.svg";
import LinkSVG from "app/assets/svg/link.svg";
import HeaderTitle from "app/components/HeaderTitle";
import { useTranslation } from "react-i18next";
import { responsiveValue } from "app/providers/responsive-value";
import { hideModal, showModal } from "app/providers/modals";
import countries from "world-countries";
import CountryPicker, { Country } from "react-native-country-picker-modal";
import { Country as CountryType } from "../../types/types";
import Select from "app/components/elements/Select";
import SelectListMap from "app/components/elements/SelectList";
import CountrySVG from "app/assets/svg/countryIcon.svg";
import StatusSVG from "app/assets/svg/status.svg";
import GraduationSVG from "app/assets/svg/graduationyearIcon.svg";
import AlumniSVG from "@/app/assets/svg/alumnus-icon.svg";
import StudentSVG from "@/app/assets/svg/student-icon.svg";
import StaffSVG from "@/app/assets/svg/staff-icon.svg";
import DropDownPicker from "react-native-dropdown-picker";
import Arrow from "@/app/assets/svg/lucosa-arrow.svg";
import DropdownSelect from "app/components/elements/DropdownSelect";
import {
  createOptimizedThemedStyles,
  createScalableThemedStyles,
  useTheme,
} from "app/theme";
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";

const CustomDropdown = ({ options, onSelect, theme }) => {
  const styles = createStyles(theme);
  return (
    <ScrollView style={styles.dropdownContainer}>
      {options.map((item) => (
        <TouchableOpacity
          key={item.id}
          style={styles.dropdownItem}
          onPress={() => onSelect(item.id)}
        >
          {item.icon}
          <Text style={styles.dropdownText}>{item.name}</Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

export type AccountProfileScreenProps = {
  navigation?: any;
  route: any;
};

const EditProfile: React.FC<AccountProfileScreenProps> = ({ navigation }) => {
  const profile = useSelector(userAuthInfo);
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const { t } = useTranslation();
  const CommonStyles = createCommonStyles(theme, fs);

  // Loading & Modal
  const [loading, setLoading] = useState(false);

  // Basic Info
  const [FirstName, setFirstName] = useState("");
  const [LastName, setLastName] = useState("");
  const [bio, setBio] = useState("");
  const [company, setCompany] = useState("");
  const [CompanyPosition, setCompanyPosition] = useState("");
  const [gender, setGender] = useState("");
  const [age, setAge] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [selectedCountry, setSelectedCountry] = useState("");
  const [selectStatus, setSelectedStatus] = useState("");
  const [graduationYear, setGraduatYear] = useState("");
  const [isDropdownVisible, d] = useState(false);
  const [open, setOpen] = useState(false); // Controls dropdown open/close state
  const [value, setValue] = useState(null); // Holds the selected value
  const [items, setItems] = useState(
    Array.from({ length: 81 }, (_, i) => ({
      label: (1950 + i).toString(), // Displayed text
      value: (1950 + i).toString(), // Unique identifier
    }))
  );

  // const years = Array.from({ length: 20 }, (_, i) => {
  //   const year = (new Date().getFullYear() - i).toString();
  //   return { label: year, value: year };
  // });

  console.log(selectStatus, "selected");
  const [showDropdown, setShowDropdown] = useState(false);

  const [isCountryPickerVisible, setCountryPickerVisible] = useState(false);

  // Public Links
  const [website, setWebsite] = useState("");
  const [facebook, setFacebook] = useState("");
  const [linkedin, setLinkedin] = useState("");

  // Profile/ Header Image
  const [showSelectProfileImage, setShowSelectProfileImage] =
    useState<boolean>(false);
  const [showSelectHeaderImage, setShowSelectHeaderImage] =
    useState<boolean>(false);
  const [profileImage, setProfileImage] = useState<Blob | null>(null);
  const [headerImage, setHeaderImage] = useState<Blob | null>(null);
  const countryList = countries
    .map((country) => ({
      label: country.name.common,
      value: country.name.common,
      flag: country.flag,
    }))
    .sort((a, b) => a.label.localeCompare(b.label)); // Sort alphabetically

  const statusOptions = [
    { id: "Student", name: "Student", icon: <StudentSVG /> },
    { id: "Alumnus", name: "Alumnus", icon: <AlumniSVG /> },
    { id: "Staff", name: "Staff", icon: <StaffSVG /> },
  ];

  const dropdownData = statusOptions.map((item) => ({
    label: item.name,
    value: item.id,
    icon: item.icon,
  }));

  const years = Array.from({ length: 81 }, (_, i) => ({
    id: (1950 + i).toString(),
    name: (1950 + i).toString(),
  }));
  // const handleStatusSelect = (selected : any) => {
  //  setSelectedStatus(selected)
  //  d(false)
  // }

  // const renderDropdownItem =({item}:any) => {

  // <TouchableOpacity
  //         key={item.id}
  //         style={styles.dropdownItem}
  //         onPress={() => handleStatusSelect(item.id)}
  //     >
  //         {item.icon}
  //         <Text style={{ fontSize: rv(12), marginLeft: rv(10), fontFamily: 'medium' }}>
  //             {item.name}
  //         </Text>
  //     </TouchableOpacity>
  // }
  // const handleSelectCountry = (country) => {
  //   setSelectedCountry(country);
  //   d(false); // Hide dropdown after selection
  // };
  // ─────────────────────────────────────────────────────────────────────────────
  //  HOOKS: Load Profile
  // ─────────────────────────────────────────────────────────────────────────────
  useEffect(() => {
    if (profile) {
      setFirstName(profile.first_name || "");
      setLastName(profile.last_name || "");
      setBio(profile.bio || "");
      setWebsite(profile.website || "");
      setFacebook(profile.facebook || "");
      setLinkedin(profile.linkedin || "");
      setCompany(profile.company || "");
      setCompanyPosition(profile.company_position || "");
      setSelectedCountry(profile.country);
      setGender(profile.gender || "");
      setAge(profile.age || "");
      setCity(profile.city || "");
      setState(profile.state || "");
      setSelectedStatus(profile.graduation_status || "");
      setGraduatYear(profile.graduation_year || "");
    }
  }, [profile]);
  console.log(selectedCountry);
  // ─────────────────────────────────────────────────────────────────────────────
  //  HOOKS: Upload Profile Image
  // ─────────────────────────────────────────────────────────────────────────────
  useEffect(() => {
    if (profileImage) {
      const pic = new FormData();
      pic.append("file", profileImage!);

      setLoading(true);
      Axios({
        method: "PATCH",
        url: "/user/update-profile-picture",
        data: pic,
        headers: { "Content-Type": "multipart/form-data" },
      })
        .then((response: any) => {
          console.log(
            "[Upload Profile Picture] Server response:",
            response.data
          );
          updateUserInfo();
          showModal({
            modalVisible: true,
            title: "Alert",
            message: "Updated successfully",
            setModalVisible: hideModal,
            type: "success-alert",
            handleConfirm: () => {
              hideModal();
            },
            handleAlert: () => {
              hideModal();
            },
          });
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [profileImage]);

  // ─────────────────────────────────────────────────────────────────────────────
  //  HOOKS: Upload Header Image
  // ─────────────────────────────────────────────────────────────────────────────
  useEffect(() => {
    if (headerImage) {
      const pic = new FormData();
      pic.append("file", headerImage!);

      setLoading(true);
      Axios({
        method: "PATCH",
        url: "/user/update-header-image",
        data: pic,
        headers: { "Content-Type": "multipart/form-data" },
      })
        .then((response: any) => {
          console.log(
            "[Upload Profile Picture] Server response:",
            response.data
          );
          updateUserInfo();
          showModal({
            modalVisible: true,
            title: "Alert",
            message: response.data.message,
            setModalVisible: hideModal,
            type: "success-alert",
            handleConfirm: () => {
              hideModal();
            },
            handleAlert: () => {
              hideModal();
            },
          });
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [headerImage]);

  // ─────────────────────────────────────────────────────────────────────────────
  //  VALIDATIONS
  // ─────────────────────────────────────────────────────────────────────────────
  const validateURL = (link: string) => {
    const regex = new RegExp(
      "(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?"
    );
    return regex.test(link);
  };

  // ─────────────────────────────────────────────────────────────────────────────
  //  UPDATE PROFILE
  // ─────────────────────────────────────────────────────────────────────────────
  const updateProfile = () => {
    // If any required field is missing, show alert
    const requiredFields = {
      FirstName,
      LastName,
      age,
      city,
      company,
      CompanyPosition,
      gender,
      bio,
      state,
      country: selectedCountry,
      website,
      facebook,
      linkedin,
      selectStatus,
    };

    for (const [fieldName, value] of Object.entries(requiredFields)) {
      if (!value) {
        // Convert the camelCase/PascalCase field name to spaced words
        const formattedName = fieldName
          // Add space between lowercase followed by uppercase
          .replace(/([a-z])([A-Z])/g, "$1 $2")
          // Handle FirstName -> First Name
          .replace(/([A-Z])([A-Z][a-z])/g, "$1 $2");

        showModal({
          modalVisible: true,
          title: "Alert",
          message: `${formattedName} cannot be empty.`,
          setModalVisible: hideModal,
          type: "alert",
          handleConfirm: hideModal,
          handleAlert: hideModal,
          navigation,
        });
        return;
      }
    }
    // Validate URL fields
    const urlFields: Record<string, string> = {
      website,
      facebook,
      linkedin,
    };
    for (const [fieldName, url] of Object.entries(urlFields)) {
      if (!validateURL(url)) {
        showModal({
          modalVisible: true,
          title: "Alert",
          message: `Invalid ${fieldName} URL`,
          setModalVisible: hideModal,
          type: "alert",
          handleConfirm: hideModal,
          handleAlert: hideModal,
          navigation,
        });
        return;
      }
    }

    // Show Loader
    setLoading(true);

    // Proceed with API call
    Axios({
      method: "POST",
      url: "/user/update-profile",
      data: {
        first_name: FirstName,
        last_name: LastName,
        gender,
        age,
        bio,
        website,
        company,
        CompanyPosition,
        facebook,
        linkedin,
        state,
        city,
        country: selectedCountry,
        graduation_status: selectStatus,
        graduation_year: graduationYear,
      },
    })
      .then((response) => {
        const message =
          response.data.message === "Profile updated successfully"
            ? "Updated successfully"
            : response.data.message;

        if (response.data) {
          updateUserInfo();
          showModal({
            modalVisible: true,
            title: "Alert",
            message,
            setModalVisible: hideModal,
            type: "success-alert",
            handleConfirm: hideModal,
            handleAlert: () => {
              navigation.goBack();
              hideModal();
            },
            navigation,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // ─────────────────────────────────────────────────────────────────────────────
  //  RENDER
  // ─────────────────────────────────────────────────────────────────────────────
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <HeaderTitle title={t("PersonalProfile_edit")} navigation={navigation} />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1, backgroundColor: theme.colors.background }}
      >
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: "center",
            alignItems: "center",
          }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="always"
          scrollEnabled={!open}
        >
          <Loader loading={loading} />

          {profile && (
            <View style={{ flexDirection: "column", paddingHorizontal: 25 }}>
              {/* Image dialogs */}
              <SelectImageDialog
                show={showSelectProfileImage}
                setShow={setShowSelectProfileImage}
                setImage={setProfileImage}
              />
              <SelectImageDialog
                show={showSelectHeaderImage}
                setShow={setShowSelectHeaderImage}
                setImage={setHeaderImage}
              />

              {/* Header & Profile Picture */}
              <View style={{ flexDirection: "column", alignItems: "center" }}>
                <View style={{ alignItems: "flex-start", width: "100%" }}>
                  <ImageBackground
                    source={
                      profile.header_image
                        ? { uri: profile.header_image }
                        : require("app/assets/default_profile_img.png")
                    }
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      width: "100%",
                      height: hp("25%"),
                    }}
                    imageStyle={styles.headerImageContainer}
                  >
                    <TouchableOpacity
                      onPress={() => setShowSelectHeaderImage(true)}
                      style={{
                        ...styles.headerImageContainer,
                        backgroundColor: theme.colors.overlayBackground,
                      }}
                    >
                      <UploadPhotoSVG width={wp("15")} height={wp("15")} />
                    </TouchableOpacity>
                  </ImageBackground>

                  <ImageBackground
                    source={
                      profile.profile_picture
                        ? { uri: profile.profile_picture }
                        : require("app/assets/connectify-thumbnail.png")
                    }
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      marginTop: -hp("10.6%"),
                      alignItems: "center",
                    }}
                    imageStyle={styles.profileImageContainer}
                  >
                    <TouchableOpacity
                      onPress={() => setShowSelectProfileImage(true)}
                      style={{
                        ...styles.profileImageContainer,
                        backgroundColor: theme.colors.overlayBackground,
                      }}
                    >
                      <UploadPhotoSVG width={wp("8")} height={wp("8")} />
                    </TouchableOpacity>
                  </ImageBackground>
                </View>
              </View>

              {/* Profile Form Fields */}
              <View
                style={{ flex: 1, marginVertical: 20, flexDirection: "column" }}
              >
                {/* First/Last Name */}
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  {/* First Name */}
                  <View
                    style={{
                      marginTop: rv(8),
                      flexDirection: "row",
                      width: "45%",
                    }}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        paddingRight: 7,
                        width: "15%",
                      }}
                    >
                      <UserNameSVG />
                    </View>
                    <View style={{ flexDirection: "column", width: "85%" }}>
                      <CustomText style={styles.title}>First Name</CustomText>
                      <TextInput
                        style={[
                          styles.font_thirteen,
                          {
                            ...CommonStyles.editInputField,
                            ...CommonStyles.editInputColor,
                          },
                        ]}
                        onChangeText={setFirstName}
                        value={FirstName}
                      />
                    </View>
                  </View>

                  {/* Last Name */}
                  <View
                    style={{
                      marginTop: rv(8),
                      flexDirection: "row",
                      width: "45%",
                    }}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        paddingRight: 7,
                        width: "15%",
                      }}
                    >
                      <UserNameSVG />
                    </View>
                    <View style={{ flexDirection: "column", width: "85%" }}>
                      <CustomText style={styles.title}>Last Name</CustomText>
                      <TextInput
                        style={[
                          styles.font_thirteen,
                          {
                            ...CommonStyles.editInputField,
                            ...CommonStyles.editInputColor,
                          },
                        ]}
                        onChangeText={setLastName}
                        value={LastName}
                      />
                    </View>
                  </View>
                </View>

                {/* Status Dropdown */}
                <View style={{ marginTop: 10, flexDirection: "row" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      paddingRight: 7,
                      width: "8%",
                    }}
                  >
                    <StatusSVG />
                  </View>
                  <View style={{ flexDirection: "column", width: "92%" }}>
                    <CustomText style={styles.title}>I am a(n)</CustomText>
                    {/* Using a consistent dropdown container */}
                    <DropdownSelect
                      data={dropdownData}
                      selectedValue={selectStatus}
                      onSelect={setSelectedStatus}
                      placeholder="Select Status"
                      containerStyle={styles.selectedStatus} // use the same style as other dropdowns
                      textStyle={[
                        styles.font_thirteen,
                        {
                          fontFamily: "semiBold",
                          color: theme.colors.textSecondary,
                        },
                      ]}
                    />
                  </View>
                </View>

                {/* Graduation Year Dropdown */}
                <View style={{ marginTop: 10, flexDirection: "row" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      paddingRight: 7,
                      width: "8%",
                    }}
                  >
                    <GraduationSVG />
                  </View>
                  <View
                    style={{
                      flexDirection: "column",
                      width: "92%",
                      marginLeft: 7,
                    }}
                  >
                    <CustomText style={styles.title}>
                      My graduation year is
                    </CustomText>

                    <View style={styles.container}>
                      <DropDownPicker
                        open={open}
                        value={graduationYear}
                        items={items}
                        setOpen={setOpen}
                        setValue={setGraduatYear}
                        setItems={setItems}
                        placeholder="Select a year"
                        style={styles.dropdown}
                        dropDownContainerStyle={styles.dropdownContainer}
                        arrowIconStyle={styles.arrowIcon}
                        listItemLabelStyle={styles.listItem}
                        textStyle={styles.textStyle}
                        searchable={true}
                        placeholderStyle={styles.placeholder}
                        searchPlaceholder="Enter a year"
                        searchTextInputStyle={styles.searchTextInput}
                        onChangeValue={(selectedValue) =>
                          console.log("Selected Year:", selectedValue)
                        }
                      />
                    </View>
                  </View>
                </View>

                {/* Company / Position */}
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    width: "100%",
                  }}
                >
                  {/* Company */}
                  <View
                    style={{ flex: 1, marginTop: rv(8), flexDirection: "row" }}
                  >
                    <View style={{ flexDirection: "row", width: "15%" }}>
                      <UserSVG />
                    </View>
                    <View
                      style={{
                        flexDirection: "column",
                        width: "85%",
                        marginLeft: 7,
                      }}
                    >
                      <CustomText style={styles.title}>
                        {t("profile_company")}:
                      </CustomText>
                      <TextInput
                        style={[
                          styles.font_thirteen,
                          {
                            ...CommonStyles.editInputField,
                            ...CommonStyles.editInputColor,
                          },
                        ]}
                        placeholder={t("EditProfile_Placeholder_company")}
                        onChangeText={setCompany}
                        value={company}
                        multiline
                      />
                    </View>
                  </View>

                  {/* Company Position */}
                  <View
                    style={{
                      flex: 1,
                      marginTop: rv(8),
                      flexDirection: "row",
                      marginLeft: rv(30),
                    }}
                  >
                    <View style={{ flexDirection: "row", width: "15%" }}>
                      <UserSVG />
                    </View>
                    <View
                      style={{
                        flexDirection: "column",
                        width: "85%",
                        marginLeft: 7,
                      }}
                    >
                      <CustomText style={styles.title}>
                        {t("profile_position")}:
                      </CustomText>
                      <TextInput
                        style={[
                          styles.font_thirteen,
                          {
                            ...CommonStyles.editInputField,
                            ...CommonStyles.editInputColor,
                          },
                        ]}
                        placeholder={t("EditProfile_Placeholder_company")}
                        onChangeText={setCompanyPosition}
                        value={CompanyPosition}
                        multiline
                      />
                    </View>
                  </View>
                </View>

                {/* Bio */}
                <View style={{ marginTop: 10, flexDirection: "row" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      paddingRight: 7,
                      width: "8%",
                    }}
                  >
                    <UserNameSVG />
                  </View>
                  <View style={{ flexDirection: "column", width: "92%" }}>
                    <CustomText style={styles.title}>Bio:</CustomText>
                    <TextInput
                      style={[
                        styles.font_thirteen,
                        {
                          ...CommonStyles.editInputField,
                          ...CommonStyles.editInputColor,
                        },
                      ]}
                      placeholder={t("profile_bio")}
                      onChangeText={setBio}
                      value={bio}
                      multiline
                    />
                  </View>
                </View>

                {/* Country of Residence Dropdown */}
                <View style={{ marginTop: 10, flexDirection: "row" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      paddingRight: 7,
                      width: "8%",
                    }}
                  >
                    <CountrySVG />
                  </View>
                  <View style={{ flexDirection: "column", width: "92%" }}>
                    <CustomText style={styles.title}>
                      Country of Residence:
                    </CustomText>
                    <SelectListMap
                      data={countryList}
                      value={
                        countryList.find((c) => c.value === selectedCountry) ||
                        null
                      }
                      onSelect={(selected) =>
                        setSelectedCountry(selected.value)
                      }
                      containerStyle={styles.selectedStatus} // reusing same dropdown style
                      textStyle={[
                        styles.font_twelve,
                        {
                          fontFamily: "medium",
                          color: theme.colors.textSecondary,
                        },
                      ]}
                    />
                  </View>
                </View>

                {/* City / State */}
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginTop: rv(10),
                  }}
                >
                  {/* City */}
                  <View style={{ flexDirection: "row", width: "45%" }}>
                    <View
                      style={{
                        flexDirection: "row",
                        paddingRight: 7,
                        width: "15%",
                      }}
                    >
                      <LocationSVG />
                    </View>
                    <View style={{ flexDirection: "column", width: "85%" }}>
                      <CustomText style={styles.title}>
                        {t("EditProfile_town")}
                      </CustomText>
                      <TextInput
                        style={[
                          styles.font_thirteen,
                          {
                            ...CommonStyles.editInputField,
                            ...CommonStyles.editInputColor,
                          },
                        ]}
                        onChangeText={setCity}
                        value={city}
                      />
                    </View>
                  </View>

                  {/* State */}
                  <View style={{ flexDirection: "row", width: "45%" }}>
                    <View
                      style={{
                        flexDirection: "row",
                        paddingRight: 7,
                        width: "15%",
                      }}
                    >
                      <LocationSVG />
                    </View>
                    <View style={{ flexDirection: "column", width: "85%" }}>
                      <CustomText style={styles.title}>
                        {t("EditProfile_State")}
                      </CustomText>
                      <TextInput
                        style={[
                          styles.font_thirteen,
                          {
                            ...CommonStyles.editInputField,
                            ...CommonStyles.editInputColor,
                          },
                        ]}
                        onChangeText={setState}
                        value={state}
                      />
                    </View>
                  </View>
                </View>

                {/* Age Dropdown */}
                <View style={{ marginTop: 10, flexDirection: "row" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      paddingRight: 7,
                      width: "8%",
                    }}
                  >
                    <AgeSVG />
                  </View>
                  <View style={{ flexDirection: "column", width: "92%" }}>
                    <CustomText style={styles.title}>
                      {t("Signup_age")}:
                    </CustomText>
                    <DropdownSelect
                      data={userConstants.ageRange}
                      selectedValue={age}
                      onSelect={setAge}
                      placeholder="Select Age"
                      containerStyle={styles.selectedStatus}
                      textStyle={[
                        styles.font_thirteen,
                        {
                          fontFamily: "semiBold",
                          color: theme.colors.textSecondary,
                        },
                      ]}
                    />
                  </View>
                </View>

                {/* Gender Dropdown */}
                <View style={{ marginTop: 10, flexDirection: "row" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      paddingRight: 7,
                      width: "8%",
                    }}
                  >
                    <UserNameSVG />
                  </View>
                  <View style={{ flexDirection: "column", width: "92%" }}>
                    <CustomText style={styles.title}>
                      {t("Signup_gender")}:
                    </CustomText>
                    <DropdownSelect
                      data={userConstants.genders}
                      selectedValue={gender}
                      onSelect={setGender}
                      placeholder="Select Gender"
                      containerStyle={styles.selectedStatus}
                      textStyle={[
                        styles.font_thirteen,
                        {
                          fontFamily: "semiBold",
                          color: theme.colors.textSecondary,
                        },
                      ]}
                    />
                  </View>
                </View>

                {/* Public Links Section */}
                <View style={{ marginTop: rv(8) }}>
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      marginBottom: rv(5),
                    }}
                  >
                    <LinkSVG width={wp("6")} height={wp("6")} />
                    <CustomText style={[styles.title, { marginLeft: rv(5) }]}>
                      Public Links
                    </CustomText>
                  </View>
                  {/* Website */}
                  <TextInput
                    style={[
                      styles.font_thirteen,
                      {
                        ...CommonStyles.editInputField,
                        ...CommonStyles.editInputColor,

                        marginBottom: rv(6),
                        color: theme.colors.info,
                      },
                    ]}
                    placeholder="e.g. personal or company website or blog"
                    onChangeText={setWebsite}
                    value={website}
                  />
                  {/* Facebook */}
                  <TextInput
                    style={[
                      styles.font_thirteen,
                      {
                        ...CommonStyles.editInputField,
                        ...CommonStyles.editInputColor,

                        marginBottom: rv(6),
                        color: theme.colors.info,
                      },
                    ]}
                    placeholder="e.g. Facebook, Instagram or X link,"
                    onChangeText={setFacebook}
                    value={facebook}
                  />
                  {/* LinkedIn */}
                  <TextInput
                    style={[
                      ,
                      styles.font_thirteen,
                      {
                        ...CommonStyles.editInputField,
                        ...CommonStyles.editInputColor,
                        color: theme.colors.info,
                      },
                    ]}
                    placeholder="e.g Linkedin profile link"
                    onChangeText={setLinkedin}
                    value={linkedin}
                  />
                </View>

                {/* Update Button */}
                <CustomButton
                  label={t("EditProfile_Btn")}
                  onPress={updateProfile}
                  loading={loading}
                />
              </View>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EditProfile;

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  title: {
    fontSize: fs("MD"), // rvf(13) -> fs('MD') (13)
    fontFamily: "semiBold",
    color: theme.colors.textSecondary,
  },
  headerImageContainer: {
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
    width: "100%",
    borderTopLeftRadius: wp("4%"),
    borderTopRightRadius: wp("4%"),
    borderBottomRightRadius: wp("4%"),
    borderBottomLeftRadius: wp("13%"),
  },
  profileImageContainer: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("18%"),
    height: wp("18%"),
    borderRadius: wp("18%"),
    borderColor: theme.colors.surface,
    borderWidth: wp("0.7%"),
  },
  // Reusable dropdown trigger style used by custom DropdownSelect and SelectListMap components.
  selectedStatus: {
    paddingHorizontal: 20,
    backgroundColor: "transparent",
    borderWidth: 2,
    borderColor: theme.colors.border,
    height: rv(40),
    alignItems: "center",
    justifyContent: "space-between",
    flexDirection: "row",
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: rv(10),
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },

  dropdownContainer: {
    maxHeight: 300, // Set a maximum height for the dropdown
    backgroundColor: theme.colors.surfaceVariant,
    fontFamily: "medium",
    fontSize: fs("BASE"), // rvf(12) -> fs('BASE') (12)
    borderWidth: 2,
    borderColor: theme.colors.border,
    color: theme.colors.text,
  },
  dropdownText: {
    fontSize: fs("BASE"), // rvf(12) -> fs('BASE') (12)
    fontFamily: "medium",
    color: theme.colors.text,
    marginLeft: rv(10),
  },
  container: {
    flex: 1,
    marginLeft: -7,
    marginRight: 7,
    // backgroundColor: "red",
  },
  label: {
    fontSize: 16,
    fontFamily: "medium",
    marginBottom: 10,
  },
  // Styles for the graduation year dropdown (using DropDownPicker)
  dropdown: {
    paddingHorizontal: 20,
    backgroundColor: "transparent",
    marginBottom: 5,
    borderWidth: 2,
    borderColor: theme.colors.border,
    color: theme.colors.textSecondary,
    width: "100%",
    height: rv(42),
    borderRadius: 5,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  font_thirteen: {
    fontSize: fs("MD"), // rvf(13) -> fs('MD') (13)
  },
  font_twelve: {
    fontSize: fs("BASE"), // rvf(12) -> fs('BASE') (12)
  },
  placeholder: {},
  searchTextInput: {
    color: theme.colors.textSecondary,
  },
  textStyle: {
    color: theme.colors.textSecondary,
       fontSize: fs("BASE"), 
  },
  listItem: {
    color: theme.colors.textSecondary,
    backgroundColor: theme.colors.surfaceVariant,
       fontSize: fs("BASE"), 
  },
  arrowIcon: {
    width: 20, // Adjust width
    height: 20, // Adjust height
    tintColor: theme.colors.textSecondary,
    // For web/RN, use:
    color:  theme.colors.textSecondary,
  },
}));
