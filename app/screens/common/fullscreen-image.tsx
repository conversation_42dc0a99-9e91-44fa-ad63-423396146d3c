import { ShowAlert } from 'app/providers/toast';
import React, { useState, useEffect } from 'react';
import * as MediaLibrary from 'expo-media-library';
import { Text, View, Image, TouchableOpacity, PermissionsAndroid,SafeAreaView,Platform, ActivityIndicator } from 'react-native';
import { Provider, Menu } from 'react-native-paper';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Ionicons from '@expo/vector-icons/Ionicons';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { showModal, hideModal } from 'app/providers/modals';

function FullScreenImage({ navigation, route }: any) {
  const [image, setImage] = useState('');
  const [showMenu, setShowMenu] = useState(false);
  const [loading, setLoading] = useState(false);


  const openMenu = () => setShowMenu(true);
  const closeMenu = () => setShowMenu(false);


  useEffect(() => {
    setImage(route.params.image);
  }, []);

  // useEffect(() => {
  //   function removeImage() {
  //     setImage('');
  //   }
  //   return () => removeImage;
  // }, []);

  const callback = (downloadProgress:any) => {
    const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
    // Update your UI with the download progress if needed
  };

  const getFileExtension = (fileUrl:any) => {
    return fileUrl ? fileUrl.split('.').pop() : '';
  };
  
  async function downloadAttachment(fileUrl: any) {
    console.log(fileUrl, "fileUrl");
    console.log("Starting download...");
  
    // Close any open menus or modals if necessary
    closeMenu && closeMenu();
  
    // Start the loading indicator
    setLoading(true);
  
    try {
      // Check and request permissions
      let { status } = await MediaLibrary.getPermissionsAsync();
      if (status !== 'granted') {
        const permission = await MediaLibrary.requestPermissionsAsync();
        status = permission.status;
      }
  
      if (status !== 'granted') {
        setLoading(false);
        showModal({
          modalVisible: true,
          title: 'Permission Required',
          message: 'Please grant storage permissions to download the file.',
          setModalVisible: hideModal,
          type: 'error-alert',
          handleConfirm: () => {
            console.log('Permission Alert Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Permission Alert Handled!');
            hideModal();
          },
        });
        return;
      }
  
      // Get the file name from the URL
      const rawFileName = fileUrl.split('/').pop();
      const decodedFileName = rawFileName ? decodeURIComponent(rawFileName) : 'downloadedFile';
      const fileName = decodedFileName.split('/').pop(); // in case there is still a slash

      // Now build the destination path:
      const directory = FileSystem.cacheDirectory + 'chats/images/';
            // Create the directory if it doesn't exist:
      const dirInfo = await FileSystem.getInfoAsync(directory);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(directory, { intermediates: true });
      }
      const tempFileUri = directory + fileName;
  
      let fileUri = '';
  
      if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
        // Remote file: download it
        console.log("ibkkk")
        const downloadedFile = await FileSystem.downloadAsync(fileUrl, tempFileUri, {}, callback);
        fileUri = downloadedFile.uri;
      } else if (fileUrl.startsWith('file://')) {
        console.log("ibkkk2")
        // Local file: copy it
        await FileSystem.copyAsync({
          from: fileUrl,
          to: tempFileUri,
        });
        fileUri = tempFileUri;
      } else {
        throw new Error('Invalid file URL scheme');
      }
  
      if (Platform.OS === 'android') {
        // Save the file to the Downloads album
        const asset = await MediaLibrary.createAssetAsync(fileUri);
        const albumName = 'Download';
        let album = await MediaLibrary.getAlbumAsync(albumName);
  
        if (!album) {
          await MediaLibrary.createAlbumAsync(albumName, asset, false);
        } else {
          await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
        }
  
        // Show success message
        showModal({
          modalVisible: true,
          title: 'Success',
          message: 'File downloaded successfully to your Downloads folder.',
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: () => {
            console.log('Download confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        });
      } else if (Platform.OS === 'ios') {
        // Use Sharing to allow the user to save the file
        await Sharing.shareAsync(fileUri);
  
        // Show success message
        showModal({
          modalVisible: true,
          title: 'Success',
          message: 'File downloaded successfully. Please choose where to save it.',
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: () => {
            console.log('Download confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        });
      }
    } catch (error) {
      console.error('Download Error:', error);
  
      // Show an error modal
      showModal({
        modalVisible: true,
        title: 'Download Failed',
        message: 'An error occurred while downloading the file.',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: () => {
          console.log('Error Alert Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Error Alert Handled!');
          hideModal();
        },
      });
    } finally {
      // Stop the loading indicator
      setLoading(false);
    }
  }
  

  return (
    <Provider>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: 'black',
        }}
      >
        <View
          style={{
            flexDirection: 'column',
          }}
        >
          <View
            style={{

              margin: 13,
              marginTop: 20,
              marginBottom: hp('0.5%'),
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            <TouchableOpacity
              onPress={() => {
                navigation.goBack(null);
              }}
              style={{
                width: 25,
                height: 25,
              }}
            >
              <Ionicons name="arrow-back" size={20} color='gold' />
            </TouchableOpacity>

            <Menu
              visible={showMenu}
              onDismiss={closeMenu}
              anchor={
                <TouchableOpacity
                  onPress={() => {
                    openMenu()
                  }}
                >
                  <Image
                    source={require('app/assets/moredots.png')}
                    style={{
                      width: 4,
                      height: 16,
                      marginTop: 5,
                      marginLeft: 10,
                      marginRight: wp('4%'),
                    }}
                  />
                </TouchableOpacity>
              }
            >

              <Menu.Item onPress={()=>downloadAttachment(image)} title='Download' />
            </Menu>
          </View>
        </View>
        {image !== '' ? (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          {loading ? (
            // Display loading indicator
            <ActivityIndicator size="large" color="#ffffff" />
          ) : (
            // Display the image
            <Image
              style={{
                width: wp('90%'),
                height: '100%',
                // maxHeight: hp('65'),
              }}
              source={{
                uri: image,
                headers: { 'Cache-Control': 'max-age=7884000' },
              }}
              resizeMode="contain"
            />
          )}
        </View>
      ) : null}
      </SafeAreaView>
    </Provider>
  );
}

export default FullScreenImage;
