import React from 'react';
import { View, FlatList, TouchableOpacity, Image } from 'react-native';
import { CustomText } from 'app/components/elements';
import HeaderTitle from 'app/components/HeaderTitle';
import Avatar from 'app/components/elements/Avater';
import GroupParticipantItem from 'app/components/FlatListItems/GroupParticipant';
import { useSelector } from 'react-redux';
import { userId } from 'app/redux/user/reducer';
import CreatorSVG from 'app/assets/svg/creator.svg';
import MemberSVG from 'app/assets/svg/members.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useGetGroup } from 'app/redux/group/hooks';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTags, useIsAdmin } from 'app/redux/group/hooks';
import { createOptimizedThemedStyles, createScalableThemedStyles, useTheme } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

const Loading = require('app/assets/loading.gif');

const GroupInfo = ({ navigation, route }: { navigation: any; route: any }) => {
  const myId = useSelector(userId);
  const { groupDetails } = route.params;
  const groupId = groupDetails?._id || null;

  const { data: group, isLoading: loading } = useGetGroup(groupId);
  const { tags, loadingTags } = useTags();
  const isAdmin = useIsAdmin(group, myId);
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  if (loading || loadingTags) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.colors.background,
        }}
      >
        <Image
          source={Loading}
          style={{
            width: rv(50),
            height: rv(50),
          }}
        />
      </View>
    );
  }

  if (!group) {
    return null;
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <HeaderTitle
        title={
          group.name.length > 17 ? `${group.name.slice(0, 17)}...` : group.name
        }
        navigation={navigation}
      />
      <View style={{ flex: 1, marginHorizontal: 30 }}>
        <View style={{ flexDirection: 'column' }}>
          {/* Group Image and Edit Button */}
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ width: '70%' }}>
              <TouchableOpacity
                onPress={() => {
                  if (group.image) {
                    navigation.push('Common', {
                      screen: 'fullscreen-image',
                      params: { image: group.image },
                    });
                  }
                }}
              >
                <Avatar size={40} type='group' source={group.image} />
              </TouchableOpacity>
            </View>
            <View style={{ width: '30%' }}>
              {isAdmin && (
                <TouchableOpacity
                  onPress={() => navigation.push('edit-group', { group })}
                  style={{
                    borderColor: theme.colors.primary,
                    borderWidth: rv(1),
                    borderRadius: rv(20),
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: rv(7),
                    paddingHorizontal: rv(10),
                  }}
                >
                  <CustomText
                    adjustsFontSizeToFit
                    numberOfLines={1}
                    // textType='semi-bold'
                    style={[styles.font_twelve,{ 
                      fontFamily: 'semiBold'


                     }]}
                  >
                    Edit Group
                  </CustomText>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Group Name and Description */}
          <CustomText
            style={[styles.font_twelve,{ marginTop: 10,               fontFamily: 'bold'
            }]}
            // textType='bold'
          >
            {group.name}
          </CustomText>
          <View style={{ marginTop: 5 }}>
            <CustomText style={[styles.font_twelve,{    
             fontFamily: 'semiBold'
 }]} 
//  textType='semi-bold'
 >
              {t('Description:')}
            </CustomText>
            <CustomText style={[styles.font_twelve,{
               fontFamily: 'medium' }]}>
              {group.description}
            </CustomText>
          </View>

          {/* Group Creator */}
          <View style={{ flexDirection: 'row', marginTop: 20 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <CreatorSVG width={22} height={22} />
              <CustomText style={[styles.font_twelve,{ marginLeft: 4,fontFamily: 'medium'
 }]}>
                {t('GroupDesc_creator')}:
              </CustomText>
            </View>
            <TouchableOpacity
              onPress={() => {
                if (myId !== group.createdBy._id) {
                  navigation.navigate('Common', {
                    screen: 'private-chat',
                    params: { userDetails: group.createdBy },
                  });
                }
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginLeft: 10,
              }}
            >
              <Avatar size={22} source={group.createdBy.profile_picture} />
              <CustomText style={[styles.font_twelve,{ marginLeft: 4, fontFamily: 'medium' }]}>
                {group.createdBy.first_name} {group.createdBy.last_name}
              </CustomText>
            </TouchableOpacity>
          </View>

          {/* Group Members */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 10,
            }}
          >
            <MemberSVG width={22} height={22} />
            <CustomText
              style={[styles.font_twelve, { marginLeft: 4,color: theme.colors.textSecondary, fontFamily: 'medium' }]}
            >
              {t('Groups_members')} ({group.participants.length} member
              {group.participants.length > 1 ? 's' : ''})
            </CustomText>
          </View>

          {/* Participants List */}
          <FlatList
            style={{ marginTop: 20 }}
            data={group.participants}
            keyExtractor={(item) => item._id}
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => {
                  if (myId !== item._id) {
                    navigation.push('Common', {
                      screen: 'private-chat',
                      params: { userDetails: item },
                    });
                  }
                }}
              >
                <GroupParticipantItem item={item} navigation={navigation} />
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  font_twelve: {
    fontSize: fs('BASE'), // rvf(12) -> fs('BASE') (12),
  },
}))

export default GroupInfo;
