import React, { useState, useEffect, useRef, useContext } from 'react';
import * as FileSystem from 'expo-file-system';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  PermissionsAndroid,
  Platform,
  SafeAreaView,
} from 'react-native';

import { Provider, Menu } from 'react-native-paper';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Loader from 'app/components/elements/Loader';
import Ionicons from '@expo/vector-icons/Ionicons';
import FontAwesome from '@expo/vector-icons/FontAwesome';

// import { GlobalContext } from '../app/GlobalProvider';
// import RNFetchBlob from 'rn-fetch-blob';
import PreviewAudio from 'app/components/media/preview-audio';
import PreviewVideo from 'app/components/media/preview-video';
import ContactHelper from 'app/helpers/ContactHelper';
import { Axios } from 'app/api/axios';
import { useSelector } from 'react-redux';
import { userId } from 'app/redux/user/reducer';
import { ShowAlert } from 'app/providers/toast';
import { showModal, hideModal } from 'app/providers/modals';
import HeaderTitle from 'app/components/HeaderTitle';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import { responsiveValue } from 'app/providers/responsive-value';
import { useTheme } from 'app/hooks/useTheme';
import * as Contacts from 'expo-contacts';
import BackArrow from '@/app/assets/svg/BackBtnMedia.svg'


function PreviewAttachment({ navigation, route }: any) {
  const attachment = route.params;

  //const { appToken, myId, showToast, AXIOS } = useContext(GlobalContext);

  const myId = useSelector(userId);
  const { theme } = useTheme();

  const [loading, setLoading] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [contacts, setContacts] = useState<any>(null);
  const [aspectRatio, setAspectRatio] = useState(1);

  // console.log(attachment.attachment, 'trpo');

  const openMenu = () => setShowMenu(true);
  const closeMenu = () => setShowMenu(false);

  async function saveContact(contact) {
    console.log('entered here');
  
    // Request permissions
    if (Platform.OS === 'android') {
      const { status } = await Contacts.requestPermissionsAsync();
  
      if (status !== 'granted') {
        showModal({
          modalVisible: true,
          title: 'Permission Denied',
          message: 'User rejected to grant permission',
          setModalVisible: hideModal, // Function to close the modal
          type: 'alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            navigation.goBack();
            hideModal();
          },
          navigation,
        });
        return;
      }
    }
  
    try {
      const contactId = await Contacts.addContactAsync(contact);
  
      if (contactId) {
        showModal({
          modalVisible: true,
          title: 'Success',
          message: 'Successfully added contact',
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            navigation.goBack();
            hideModal();
          },
          navigation,
        });
      }
    } catch (error) {
      console.error('Error saving contact:', error);
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Failed to save contact',
        setModalVisible: hideModal, // Function to close the modal
        type: 'alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          navigation.goBack();
          hideModal();
        },
        navigation,
      });
    }
  }

  async function deleteAttachment() {
    let URL = '';

    if (attachment.groupId) {
      URL = '/group/delete-message/' + attachment._id;
    } else if (attachment.chatId) {
      URL = '/chats/delete-message/' + attachment._id;
    }

    setLoading(true);

    await Axios({
      method: 'delete',
      url: URL,
    })
      .then((response) => {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: response?.message,
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            console.log('Alert handled!');
            hideModal();
          },
        });
        // ShowAlert({
        //   type: 'info',
        //   className: 'Success',
        //   message: response.data.message,
        // });

        navigation.goBack();
      })
      .finally(() => {
        setLoading(false);
      });
  }

// Helper function to get file extension
const getFileExtension = (fileUrl:any) => {
  return fileUrl ? fileUrl.split('.').pop() : '';
};

async function downloadAttachment(fileUrl) {
  console.log("Starting download...");

  // Close any open menus or modals if necessary
  closeMenu && closeMenu();

  // Start the loading indicator
  setLoading(true);

  try {
    // Check and request permissions
    let { status } = await MediaLibrary.getPermissionsAsync();
    if (status !== 'granted') {
      const permission = await MediaLibrary.requestPermissionsAsync();
      status = permission.status;
    }

    if (status !== 'granted') {
      setLoading(false);
      showModal({
        modalVisible: true,
        title: 'Permission Required',
        message: 'Please grant storage permissions to download the file.',
        setModalVisible: hideModal,
        type: 'error-alert',
        handleConfirm: () => {
          console.log('Permission Alert Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Permission Alert Handled!');
          hideModal();
        },
      });
      return;
    }

    // Extract the file name safely and remove any query parameters
    let fileName = fileUrl.split('/').pop() || 'downloadedFile';
    fileName = fileName.split('?')[0];

    // Build the destination directory and ensure it exists
    // For example, if you're saving documents, use a directory like 'topics/documents/'
    const directory = FileSystem.cacheDirectory + 'topics/documents/';
    const dirInfo = await FileSystem.getInfoAsync(directory);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(directory, { intermediates: true });
    }
    const tempFileUri = directory + fileName;

    let fileUri = '';

    if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
      // Remote file: download it
      const downloadedFile = await FileSystem.downloadAsync(fileUrl, tempFileUri);
      fileUri = downloadedFile.uri;
    } else if (fileUrl.startsWith('file://')) {
      // Local file: copy it
      await FileSystem.copyAsync({
        from: fileUrl,
        to: tempFileUri,
      });
      fileUri = tempFileUri;
    } else {
      throw new Error('Invalid file URL scheme');
    }

    // For Android, save the file to the Downloads album
    if (Platform.OS === 'android') {
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      const albumName = 'Download';
      let album = await MediaLibrary.getAlbumAsync(albumName);

      if (!album) {
        await MediaLibrary.createAlbumAsync(albumName, asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }

      // Show success message
      showModal({
        modalVisible: true,
        title: 'Success',
        message: 'File downloaded successfully to your downloads folder.',
        setModalVisible: hideModal,
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Download confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
      });
    } else if (Platform.OS === 'ios') {
      // On iOS, use Sharing to allow the user to choose where to save it
      await Sharing.shareAsync(fileUri);

      // Show success message
      showModal({
        modalVisible: true,
        title: 'Success',
        message: 'File downloaded successfully. Please choose where to save it.',
        setModalVisible: hideModal,
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Download confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
      });
    }
  } catch (error) {
    console.error('Download Error:', error);

    // Show an error modal
    showModal({
      modalVisible: true,
      title: 'Download Failed',
      message: 'An error occurred while downloading the file.',
      setModalVisible: hideModal,
      type: 'alert',
      handleConfirm: () => {
        console.log('Error Alert Confirmed!');
        hideModal();
      },
      handleAlert: () => {
        console.log('Error Alert Handled!');
        hideModal();
      },
    });
  } finally {
    // Stop the loading indicator
    setLoading(false);
  }
}



  useEffect(() => {
    if (attachment && attachment.attachment && attachment.type === 'contact') {
      setContacts(JSON.parse(attachment.attachment));
    }
  }, [attachment]);

 
  return (
  <Provider>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: theme.colors.background,
        }}
      >
        <Loader loading={loading} />
        <View
          style={{
            flexDirection: 'column',
          }}
        >
          <View
            style={{
              margin: responsiveValue(10),
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            <TouchableOpacity
              onPress={() => {
                navigation.goBack(null);
              }}
              style={{
                width: 25,
                height: 25,
              }}
            >
             <BackArrow width={20} />
            </TouchableOpacity>

            {attachment.type != 'contact' ? (
              <Menu
                visible={showMenu}
                onDismiss={closeMenu}
                anchor={
                  <TouchableOpacity onPress={openMenu}>
                    <Image
                      source={require('app/assets/moredots.png')}
                      style={{
                        width: 4,
                        height: 16,
                        marginTop: 5,
                        marginLeft: 10,
                        marginRight: wp('4%'),
                        tintColor: theme.colors.primary,
                      
                      }}
                    />
                  </TouchableOpacity>
                }
              >
                {attachment.senderId && attachment.senderId._id == myId ? (
                  <Menu.Item onPress={deleteAttachment} title='Delete' />
                ) : null}

                <Menu.Item onPress={()=>downloadAttachment(attachment.attachment)} title='Download' />             
              </Menu>
            ) : (
              <View
                style={{
                  flex: 1,
                  marginTop: -5,
                  marginLeft: 30,
                  justifyContent: 'center',
                }}
              >
                <Text
                  style={{
                    color: theme.colors.primary,
                    fontSize: 23,
                    fontFamily: 'bold'
                  }}
                >
                  Contact
                </Text>
              </View>
            )}
          </View>
        </View>
        {attachment.type === 'image' || attachment.type === 'camera' ? (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
            }}
          >
            <Image
              source={{
                uri: attachment.attachment,
              }}
              style={{
                width: '100%',
                aspectRatio: aspectRatio,
              }}
              onLoad={(event) => {
                const { width, height } = event.nativeEvent.source;
                setAspectRatio(width / height);
              }}
              resizeMode="contain"
            />
          </View>
        ) : attachment.type == 'video' ? (
          <View
            style={{
              height: '90%',
            }}
          >
            <PreviewVideo videoLink={attachment.attachment} />
          </View>
        ) : attachment.type == 'audio' ? (
          <PreviewAudio
            navigation={navigation}
            audioLink={attachment.attachment}
          />
        ) : attachment.type == 'contact' ? (
          <View style={{ flex: 1 }}>
            {contacts ? (
              <View
                style={{
                  flex: 1,
                  marginTop: -40,
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <FontAwesome
                  name='address-book'
                  size={100}
                  style={{ marginRight: 5 }}
                  color={theme.colors.textSecondary}
                />

                <Text
                  style={{
                    paddingTop: 20,
                    fontSize: 30,
                    color: theme.colors.text,
                    fontFamily: 'bold'
                  }}
                >
                  {contacts.name}
                </Text>
                {contacts.phoneNumbers.map((item: any) => (
                  <View>
                    <Text
                      style={{
                        fontSize: 20,
                        color: theme.colors.textSecondary,
                      }}
                    >
                      {item.number}
                    </Text>
                  </View>
                ))}
                <View
                  style={{
                    marginTop: 14,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => saveContact(contacts)}
                    style={{
                      backgroundColor: theme.colors.primary,
                      padding: 18,
                      paddingHorizontal: 25,
                      borderRadius: 10,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 15,
                        fontFamily: 'bold',
                        color: theme.colors.background,
                      }}
                    >
                      Save Contact
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : null}
          </View>
        ) : attachment.type == 'document' ? (
          <View
            style={{
              flex: 1,
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons
              name='document'
              size={80}
              style={{ marginRight: 5 }}
              color={theme.colors.textSecondary}
            />

            <Text
              style={{
                paddingTop: 20,
                fontSize: 30,
                color: theme.colors.text,
                fontFamily: 'bold'              }}
            >
              Download to view
            </Text>
          </View>
        ) : null}
      </SafeAreaView>
      </Provider>
    
  );
}

export default PreviewAttachment;
