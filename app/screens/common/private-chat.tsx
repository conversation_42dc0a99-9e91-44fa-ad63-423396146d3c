import * as Clipboard from 'expo-clipboard';
import React, {
  useState,
  useEffect,
  useContext,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import {
  useActionSheet,
  ActionSheetOptions,
  ActionSheetProvider,
  ActionSheetProviderRef,
} from '@expo/react-native-action-sheet';
import { isSameDay } from 'date-fns'; // Import the isSameDay function
import {
  View,
  StyleSheet,
  Image,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator, // Import ActivityIndicator
  FlatList,
} from 'react-native';
// import Clipboard from '@react-native-community/clipboard'; //Commented out as instructed
import SelectAttachment from 'app/components/select-attachment';
import {
  GiftedChat,
  Bubble,
  Send,
  InputToolbar,
  Time,
  MessageText,
  Composer,
} from 'react-native-gifted-chat';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { Button, Menu, Divider, Provider } from 'react-native-paper';
import SocketIOClient from 'socket.io-client';
import { GlobalContext } from 'app/GlobalProvider';
import ReportDialog from 'app/components/dialogs/report';
import Avatar from 'app/components/elements/Avater';
import { CustomText } from 'app/components/elements';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import { Axios } from 'app/api/axios';
import { useSelector } from 'react-redux';
import { userId, userToken } from 'app/redux/user/reducer';
import { MAIN_URL } from 'app/api/axios';
import { ShowAlert } from 'app/providers/toast';
import useMyChat from 'app/redux/chat/hooks';
import { FileSelectHelper } from '../../helpers/FileSelectHelper';
import { CameraHelper, FileHelper } from 'app/helpers';
import SelectContact from 'app/components/SelectContact';
import GallerySVG from 'app/assets/svg/media/gallery2.svg';
import GallerySVG2 from 'app/assets/svg/media/gallery3.svg';
import CameraSVG from 'app/assets/svg/media/camera.svg';
import DocumentSVG from 'app/assets/svg/media/document.svg';
import ContactSVG from 'app/assets/svg/media/contact.svg';
import AudioSVG from 'app/assets/svg/media/audio.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import MenuSVG from 'app/assets/svg/menu.svg';
import AttachmentSVG from 'app/assets/svg/attachment-icon.svg';
import ChatSendSVG from 'app/assets/svg/chat-send.svg';
import SmileySVG from 'app/assets/svg/smiley.svg';
import EmojiPicker from 'rn-emoji-keyboard';
import { useTranslation } from 'react-i18next';
import { showModal, hideModal } from 'app/providers/modals';
import { SafeAreaView } from 'react-native-safe-area-context';

import Ionicons from '@expo/vector-icons/Ionicons';
import { customParsePatterns } from 'app/helpers/linkpattern';
import useTrackActivity from 'app/hooks/useTrackActivity';
import { Camera } from 'expo-camera';
const Loading = require('../../assets/loading.gif');
import {
  SafeAreaProvider,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { v4 as uuidv4 } from 'uuid';
import { MIN_COMPOSER_HEIGHT } from 'react-native-gifted-chat';
import { Modal } from 'react-native';
import 'react-native-get-random-values'; // Add this at the very top of imports
import { useTheme, createThemedStyles, createScalableThemedStyles, createOptimizedThemedStyles } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

// Container Component
const PrivateChatContainer = ({ navigation, route, type }: any) => {
  const insets = useSafeAreaInsets();
  const myId = useSelector(userId);
  const appToken = useSelector(userToken);
 const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const { refetch: refreshMyChat, isLoading, isFetching } = useMyChat();
  //console.log(isLoading, isFetching) // Removed for brevity

  const { userDetails, bot } = route.params;
  const [showMenu, setShowMenu] = useState(false);
  const [showAttachmentDialog, setShowAttachmentDialog] = useState(false);
  const [singleFile, setSingleFile] = useState([]);
  const [messages, setMessages] = useState<any>([]);
  const [chatDetails, setChatDetails] = useState<any>(null);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [openSearch, setOpenSearch] = useState(false);
  const [searchInput, setSearchInput] = useState('');
  const [reportDetails, setReportDetails] = useState({});
  const [loading, setLoading] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [isLoadingOlderMessages, setIsLoadingOlderMessages] = useState(true);
  const [totalPageNumber, setTotalPageNumber] = useState(0);
  const [quotedData, setQuotedData] = useState<any>(null);
  const [contactDetails, setContactDetails] = useState(null);
  const [showSelectContact, setShowSelectContact] = useState(false);
  const [processingFile, setProcessingFile] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [text, setTextInputValue] = useState(''); // State for text input value
  const { t } = useTranslation();
  const { trackActivity, activityCounts, modalVisible } =
    useTrackActivity('sponsor');
  const { trackActivity: trackShare } = useTrackActivity('share');
  const [isSending, setIsSending] = useState(false);
  const [inputHeight, setInputHeight] = useState(Platform.OS === 'ios' ? 40 : 45);
  const initialMeasured = useRef(false);
  const [defaultComposerHeight, setDefaultComposerHeight] =
    useState(MIN_COMPOSER_HEIGHT);
  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [actionSheetOptions, setActionSheetOptions] = useState<string[]>([]);
  const [cancelButtonIndex, setCancelButtonIndex] = useState<number>(0);
  const [onActionSheetSelect, setOnActionSheetSelect] = useState<
    (index: number) => void
  >(() => {});

  const handleSponsorAndShare = () => {
    trackActivity();
    trackShare();
    //console.log(activityCounts, "activityCounts") //Removed for brevity
  };

  const handleComposerTextChanged = (text: string) => {
    setTextInputValue(text); // Update the text input value in the state
  };

  let socket = useRef<any>(null);

  const openMenu = () => setShowMenu(true);
  const closeMenu = () => setShowMenu(false);

  useEffect(() => {
    if (
      chatDetails &&
      chatDetails._id &&
      userDetails &&
      userDetails._id &&
      !bot &&
      socket.current === null
    ) {
      let connectionOptions: any = {
        'force new connection': true,
        reconnectionAttempts: 'Infinity',
        timeout: 100000,
        transports: ['websocket'],
        auth: {
          token: appToken,
        },
      };

      socket.current = SocketIOClient(MAIN_URL, connectionOptions);

      socket.current.emit(
        'join-chat',
        { chatId: chatDetails._id, accountId: userDetails._id },
        (response: any) => {
          if (!response.success) {
            if (response && response.message) {
              0;
              showModal({
                modalVisible: true,
                title: 'Alert',
                message: response.message,
                setModalVisible: hideModal, // Function to close the modal
                type: 'success-alert',
                handleConfirm: () => {
                  // console.log('Confirmed!');
                  hideModal();
                },
                handleAlert: () => {
                  // console.log('Alert handled!');
                  hideModal();
                },
              });
            }
          }
        }
      );
    }
  }, [chatDetails, userDetails]);

  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
  };

  async function loadData() {
    if (appToken && userDetails && userDetails._id) {
      setLoading(true);
      if (bot) {
        loadBotMessages();
        return;
      }
      await Axios.get(
        '/chats/' + userDetails._id + `?page=${pageNumber}&limit=10`
      )
        .then((response) => {
          setTotalPageNumber(response.data.chats.totalPages);
          setChatDetails(response.data.chats.chatDetails);
          let formattedMessages = JSON.parse(
            JSON.stringify(response.data.chats.docs)
          );
          formattedMessages.forEach((message: any) => {
            message = formatMessage(message);
          });
          // setMessages(formattedMessages);

          // if (messages && messages.length > 0) {
          //   setMessages((messages: any) => [...messages, ...formattedMessages]);
          // }

          setMessages((messages: any) => [...messages, ...formattedMessages]);

          // console.log('gos');
        })
        .catch((error) => {
          console.log('error =====', error);
        })
        .finally(() => {
          setIsLoadingOlderMessages(false);
          setLoading(false);
        });
    }
  }

  async function loadBotMessages() {
    setIsLoadingOlderMessages(true);

    await Axios.get('/chats/bot-chats' + `?page=${pageNumber}&limit=5`)
      .then((response) => {
        setTotalPageNumber(response.data.chats.totalPages);
        setChatDetails(response.data.chats.chatDetails);
        let formattedMessages = JSON.parse(
          JSON.stringify(response.data.chats.docs)
        );
        formattedMessages.forEach((message: any) => {
          message = formatMessage(message);
        });
        // setMessages(formattedMessages);
        setMessages((messages: any) => [...messages, ...formattedMessages]);
        // console.log('gos');
      })
      .finally(() => {
        setIsLoadingOlderMessages(false);
      });
  }

  function loadNewerMessages() {
    setPageNumber(pageNumber + 1);
  }

  function toggleOpenSearch() {
    if (openSearch) {
      setMessages([]);
      loadData();
    }
    setOpenSearch(!openSearch);
  }

  async function searchChat() {
    if (appToken && searchInput != '' && chatDetails && chatDetails._id) {
      await Axios({
        method: 'POST',
        url: '/chats/search/' + chatDetails._id,
        data: {
          search: searchInput,
        },
      }).then((response) => {
        setTotalPageNumber(response.data.totalPages);
        let formattedMessages = JSON.parse(JSON.stringify(response.data.docs));
        formattedMessages.forEach((message: any) => {
          message = formatMessage(message);
        });

        setMessages(formattedMessages);
      });
    }
  }

  useEffect(() => {
    if (searchInput != '') {
      searchChat();
    }
  }, [searchInput]);

  // Initial load: only once when userDetails become available
  useEffect(() => {
    if (userDetails && userDetails._id) {
      // Reset page number to 1 (if needed) and load initial data
      setPageNumber(1);
      loadData();
    }
  }, [userDetails]);

  // Pagination: load additional data when pageNumber changes (for pageNumber > 1)
  useEffect(() => {
    if (pageNumber > 1) {
      loadData();
    }
  }, [pageNumber]);

  function formatMessage(chatMessage: any) {
    let message = { ...chatMessage }; // Create a copy

    message.text = message.message || 'NO_MESSAGE';
    message.emptyText = !message.message;
    message.user = { _id: message.senderId }; // Directly use senderId
    message._id = message._id ? message._id.toString() : uuidv4(); // Ensure _id is a string, or generate one

    return message;
  }

  function openReportDetails(item: any) {
    setReportDetails({ data: { _id: item }, type: 'user' });
    closeMenu();
    setShowReportDialog(true);
  }
  // console.log(messages, 'ibkk')

  async function blockUser() {
    if (appToken && userDetails && userDetails._id) {
      await Axios({
        method: 'patch',
        url: '/chats/block/' + userDetails._id,
        data: {},
      }).then((response) => {
        const message =
          response.data.message === 'Blocked successfully'
            ? 'User blocked'
            : response.data.message;

        refreshMyChat();
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: message,
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            //console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            //console.log('Alert handled!');
            hideModal();
          },
        });

        navigation.goBack();
      });
    }
  }

  async function unblockUser() {
    closeMenu();
    if (appToken && userDetails && userDetails._id) {
      await Axios({
        method: 'patch',
        url: '/chats/unblock/' + userDetails._id,
        data: {},
      }).then((response) => {
        const message =
          response.data.message === 'Chat has been unblocked successfully'
            ? 'User unblocked'
            : response.data.message;

        socket.current = null;
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: message,
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            //console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            //console.log('Alert handled!');
            hideModal();
          },
        });
        // ShowAlert({
        //  type: 'success',
        //  className: 'Success',
        //  message: response.data.message,
        // });
        loadData();
      });
    }
  }

  async function reportUser() {
    if (appToken && userDetails && userDetails._id) {
      await Axios({
        method: 'patch',
        url: '/user/report/' + userDetails._id,
        data: {},
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${appToken}`,
        },
      }).then((response) => {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: response.data.message,
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => {
            //console.log('Confirmed!');
            hideModal();
          },
          handleAlert: () => {
            //console.log('Alert handled!');
            hideModal();
          },
        });
        // ShowAlert({
        //  type: 'success',
        //  className: 'Success',
        //  message: response.data.message,
        // });
        navigation.goBack();
      });
    }
  }

  const __startCamera = async () => {
    const { status } = await Camera.requestCameraPermissionsAsync();
    if (status === 'granted') {
      // start the camera
      navigation.push('Common', {
        screen: 'camera-screen',
      });
    } else {
      alert('Access denied');
    }
  };

  const openShareAttachment = async (attachmentType: any) => {
    try {
      let file = null;
      let fileType = null; // Initialize fileType variable

      if (attachmentType == 'camera') {
        file = await CameraHelper.openCamera();
        fileType = 'image';
      } else if (attachmentType == 'image') {
        file = await CameraHelper.selectImageFromGallery();
        fileType = 'image';
      } else if (attachmentType == 'video') {
        file = await CameraHelper.selectVideoFromGallery(setProcessingFile);
        fileType = 'video';
      } else if (attachmentType == 'contact') {
        if (contactDetails) {
          file = contactDetails;
          fileType = 'contact';
        } else {
          return;
        }
      } else {
        file = await FileSelectHelper(attachmentType);
        fileType = attachmentType;
      }

      if (!file) {
        return;
      }

      navigation.push('Common', {
        screen: 'share-attachment',
        params: {
          attachmentType: fileType, // Use fileType here
          attachment: file,
          name: `Share to ${userDetails.first_name} ${userDetails.last_name}`,
          chatType: 'chat',
          chatId: chatDetails._id,
        },
      });

      //console.log(fileType, 'viewtype'); // Log fileType to debug
    } catch (err) {
      alert(err);
    }
  };

  useEffect(() => {
    if (contactDetails) {
      openShareAttachment('contact');
    }
  }, [contactDetails]);

  async function deleteMessage(messageId: string) {
    if (appToken) {
      await Axios({
        method: 'delete',
        url: '/chats/delete-message/' + messageId,
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + appToken,
        },
      }).then((response) => {
        // console.log('success');
      });
    }
  }

  function onSend(newMessage: { text: string }) {
    if (!chatDetails || !userDetails) {
      console.error('onSend: chatDetails or userDetails are not available');
      return;
    }

    setIsSending(true);
    console.log('newMessage', newMessage);

    // Debug UUID generation
    let localId;
    try {
      console.log('About to generate UUID...');
      localId = uuidv4();
      console.log('Successfully generated UUID:', localId);
    } catch (error) {
      console.error('UUID generation error:', error);
      // Fallback to timestamp-based ID
      localId = `${Date.now()}-${Math.random().toString(36).slice(2, 11)}`;
      console.log('Using fallback ID:', localId);
    }
    
    const now = new Date();
    
    const localMessage = {
      _id: localId,
      message: newMessage.text,
      senderId: myId,
      createdAt: now,
      user: { _id: userDetails._id },
      pending: true,
      ...(quotedData && { quotedReply: quotedData }),
    };

    console.log('localMessage', localMessage);

    // Add the local pending message to state
    setMessages((prev: any) => [localMessage, ...prev]);

    // Clear the input
    setTextInputValue('');
    setInputHeight(defaultComposerHeight);

    // 2) Build the object to actually send to the server
    const messageToSend: any = {
      chatId: chatDetails._id,
      accountId: userDetails._id,
      message: newMessage.text,
      type: 'message',
      token: appToken,
      localId, // (Optional) Pass the localId so server can echo it back
      clientCreatedAt: now, // Add the client-created timestamp
    };

    // If there's quoted data, attach it
    if (quotedData) {
      messageToSend.quotedData = quotedData;
      setQuotedData(null);
    }

    // 3) Emit the message to the server
    socket.current.emit('Private Chat', messageToSend, (response: any) => {
      setIsSending(false);

      if (response.message === 'no-subscription') {
        navigation.navigate('Home', { screen: 'Donate' });
      } else if (!response.success) {
        // Show any error or subscription issues
        const errorMessage = response.message;
        let modalType = 'alert';
        if (
          errorMessage === 'Your subscription has expired' ||
          errorMessage === 'Your subscription was not found'
        ) {
          modalType = 'credit';
        }
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: errorMessage,
          setModalVisible: hideModal,
          type: modalType,
          handleConfirm: () => {
            hideModal();
          },
          handleAlert: () => {
            hideModal();
          },
          navigation,
        });
      }
    });

    handleSponsorAndShare(); // Call this after sending
  }

  useEffect(() => {
    console.log('this is ibk', socket.current);
    if (!socket.current) return;

    socket.current.on('chat-messages', (data: any) => {
      if (data.deletedMessage) {
        setMessages((prev: any) =>
          prev.filter((m: any) => m._id !== data.messageId)
        );
      } else {
        const serverMsg = formatMessage(data); // e.g. { _id, text, createdAt, user, ... }
        console.log(serverMsg, 'serverMsg');

        setMessages((prev: any) => {
          let foundLocalPending = false;
          const newList = prev.map((m: any) => {
            if (
              m.pending &&
              (m.message === serverMsg.text ||
                (data.localId && data.localId === m._id))
            ) {
              console.log(
                'Replacing pending message:',
                m,
                'with server message:',
                serverMsg
              );
              foundLocalPending = true;
              return {
                ...serverMsg,
                pending: false,
              };
            }
            return m;
          });

          if (!foundLocalPending) {
            return [serverMsg, ...newList];
          }
          return newList;
        });

        setIsSending(false);
      }
    });
  }, [socket.current]);

  const renderTime = (props: any) => {
    return (
      <Time
        {...props}
        timeTextStyle={{
          left: {
            color: 'black',
          },
          right: {
            color: 'black',
          },
        }}
      />
    );
  };

  // Custom Bubble component (replace GiftedChat's Bubble)
  const renderBubble = ({ currentMessage, position }: any) => {
    const isMyMessage = position === 'right';
    const bubbleStyles = isMyMessage
      ? {
          backgroundColor: theme.colors.chatBubbleMy,
          borderTopLeftRadius: 20,
          borderBottomLeftRadius: 20,
          borderTopRightRadius: 20,
          borderBottomRightRadius: 0,
        }
      : {
          backgroundColor: theme.colors.chatBubbleOther,
          borderTopLeftRadius: 0,
          borderBottomLeftRadius: 20,
          borderTopRightRadius: 20,
          borderBottomRightRadius: 20,
        };

    // If it's purely text (no recognized attachment type), do a normal text bubble:
    const isAttachment =
      currentMessage.type === 'image' ||
      currentMessage.type === 'video' ||
      currentMessage.type === 'document' ||
      currentMessage.type === 'audio' ||
      currentMessage.type === 'contact';

    // 1) If it's an attachment, wrap in a single TouchableOpacity that navigates to preview
    if (isAttachment) {
      return (
        <TouchableOpacity
          activeOpacity={0.8}
          // "preview-attachment" is your old screen from the snippet
          onPress={() => {
            if (currentMessage.type === 'image') {
                navigation.push('Common', {
                    screen: 'fullscreen-image',
                    params: {
                        image: currentMessage.attachment,
                    },
                });
            } else {
                navigation.push('Common', {
                    screen: 'preview-attachment',
                    params: { ...currentMessage },
                });
            }
        }}
          style={[
            styles.bubbleContainer,
            isMyMessage
              ? styles.myBubbleContainer
              : styles.otherBubbleContainer,
            // if you want some padding around the bubble
            { padding: 5 },
          ]}
        >
          {/** Now handle each attachment type, just like your old code */}
          {currentMessage.type === 'image' && (
            <View style={{ width: wp('45%'), height: hp('30%') }}>
              <Image
                source={{ uri: currentMessage.attachment }}
                style={{ width: '100%', height: '100%' }}
                resizeMode='cover'
              />
            </View>
          )}

          {currentMessage.type === 'video' && (
            <View
              style={{
                width: wp('45%'),
                height: hp('30%'),
                
              }}
            >
              {/* Overlaid play icon + file size */}
              <View
                style={{
                  position: 'absolute',
                  top: '40%', // Adjust as needed
                  left: 0,
                  right: 0,
                  justifyContent: 'center',
                  alignItems: 'center',
                  zIndex: 999,
                  flexDirection: 'column',
                }}
              >
                <FontAwesome name='play-circle' size={35} color='white' />
                <CustomText style={{ color: 'white' }} textType='bold'>
                  {FileHelper.byteToFileSize(
                    currentMessage.attachmentSize || 0
                  )}
                </CustomText>
              </View>
              <View style={{ borderRadius: 20,
                  overflow: 'hidden',
                  backgroundColor: 'black', }}>
                <Image
                  source={{ uri: currentMessage.attachment }}
                  style={{ width: wp('45%'), height: hp('29%') }}
                  resizeMode='cover'
                />
              </View>
            </View>
          )}

          {currentMessage.type === 'contact' && (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                padding: 15,
                paddingHorizontal: 20,
                borderRadius: 5,
                backgroundColor: 'white',
              }}
            >
              <FontAwesome
                name='address-card'
                size={23}
                style={{ marginRight: 5 }}
                color='#FFCB05'
              />
              <CustomText
                style={[styles.font_thirteen,{marginLeft: 4 }]}
                textType='bold'
              >
                {currentMessage.contact
                  ? currentMessage.contact.displayName
                  : ''}
              </CustomText>
            </View>
          )}

          {currentMessage.type === 'document' && (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: 15,
                borderRadius: 5,
                borderWidth: 1,
                borderColor: 'gray',
                backgroundColor: 'white',
              }}
            >
              <FontAwesome
                name='file'
                size={20}
                color='#FFCB05'
                style={{ paddingRight: 5 }}
              />
              <CustomText
                style={[styles.font_thirteen,{paddingRight: 15 }]}
                textType='bold'
              >
                {currentMessage.fileName || 'Document File'}
              </CustomText>
              <CustomText style={styles.font_thirteen}>
                {FileHelper.byteToFileSize(currentMessage.attachmentSize || 0)}
              </CustomText>
            </View>
          )}

          {currentMessage.type === 'audio' && (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: 15,
                borderRadius: 5,
                backgroundColor: 'white',
              }}
            >
              <FontAwesome
                name='play-circle'
                size={21}
                style={{ marginRight: 5 }}
                color='#FFCB05'
              />
              <CustomText
                style={[styles.font_thirteen,{ paddingRight: 15 }]}
                textType='bold'
              >
                Audio File
              </CustomText>
              <CustomText style={styles.font_thirteen}>
                {FileHelper.byteToFileSize(currentMessage.attachmentSize || 0)}
              </CustomText>
            </View>
          )}

          {/* Time under the attachment if you like */}
          <Text style={[styles.font_nine,{ color: theme.colors.textSecondary, marginTop: 5 }]}>
            {new Date(currentMessage.createdAt).toLocaleTimeString()}
          </Text>
        </TouchableOpacity>
      );
    }
    console.log(currentMessage, 'currentMessage');
    return (
        <View
          style={[
            styles.bubbleContainer,
            isMyMessage ? styles.myBubbleContainer : styles.otherBubbleContainer,
          ]}
        >
          {currentMessage.quotedReply && (
            <View style={styles.quotedContainer}>
              <Text numberOfLines={2} style={styles.quotedText}>
                {currentMessage.quotedReply.message}
              </Text>
            </View>
          )}
          <View style={[styles.bubble, bubbleStyles]}>
            <Text
              style={[
                styles.messageText,
                isMyMessage ? styles.myMessageText : styles.otherMessageText,
              ]}
            >
              {currentMessage.message}
            </Text>
          </View>
          <View>
            <Text style={[styles.time]}>
              {new Date(currentMessage.createdAt).toLocaleTimeString()}
            </Text>
          </View>
        </View>
      );
  };

  function renderSend(props: any) {
    const disabled = !props.text?.trim(); // Use optional chaining

    return (
      <View style={styles.sendContainer}>
        <TouchableOpacity
          onPress={() => {
            setShowAttachmentDialog(!showAttachmentDialog);
          }}
          style={styles.attachmentButton}
        >
          <AttachmentSVG width={rv(20)} height={rv(20)} />
        </TouchableOpacity>

        {/* Conditional rendering for the send button */}
        <TouchableOpacity
          style={styles.sendButton}
          onPress={() => {
            if (!disabled) {
              // Call onSend only if not disabled
              props.onSend({ text: props.text.trim() }, true);
            }
          }}
          disabled={disabled} //Keep this disabled to prevent multiple presses
        >
          <ChatSendSVG
            width={rv(30)}
            height={rv(30)}
            fill={disabled ? '#9E9E9E' : '#FFCB05'}
          />
        </TouchableOpacity>
      </View>
    );
  }

  const renderComposer = (props: any) => {
    const handleEmojiSelected = (emoji: any) => {
      const updatedText = `${text} ${emoji.emoji}`;
      setTextInputValue(updatedText);
    };

    // console.log(props, 'Here is where i am talking about');
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'column',
        }}
      >
        <View
          style={{
            width: wp('100%'), // Use percentage width
            backgroundColor: 'transparent',
          }}
        >
          {quotedData ? (
            <View
              style={{
                backgroundColor: 'black',
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
                paddingVertical: 10, // Reduced vertical padding
                paddingLeft: 13,
                borderLeftWidth: 10,
                borderLeftColor: '#9FD0D0',
                marginBottom: 5, // Optional margin to separate from the Composer
                maxHeight: 100
              }}
            >
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <CustomText
                  style={[
                    styles.font_thirteen,{
                    color: '#9FD0D0',
                  
                    marginBottom: 10,
                    alignItems: 'flex-start'
                  }]}
                  textType='bold'
                >
                  Replying to
                </CustomText>
                <TouchableOpacity
                  onPress={() => {
                    setQuotedData(null);
                  }}
                >
                  <FontAwesome
                    name='times'
                    color={'grey'}
                    size={20}
                    style={{
                      paddingHorizontal: 17,
                    }}
                  />
                </TouchableOpacity>
              </View>

              <View>
                <CustomText
                    style={[
                      styles.font_thirteen,{
                        color: 'white',
                     
                    }]}
                    numberOfLines={2} // Limit to two lines
                    ellipsizeMode="tail" // Show ellipsis at the end
                >
                    {quotedData.message}
                </CustomText>
              </View>
            </View>
          ) : null}
        </View>
        <View
          style={{
            width: wp('75%'), // Use percentage width
            flexDirection: 'row',
            paddingLeft: 10,
            alignItems: 'flex-end', // <-- This keeps the smiley fixed at the bottom
            // height: 600,
          }}
        >
          <TouchableOpacity
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              padding: 5,
              paddingHorizontal: 8,
              // paddingBottom: 10,
            }}
            onPress={toggleEmojiPicker}
          >
            <SmileySVG width={rv(25)} height={rv(25)} />
          </TouchableOpacity>

          <Composer
            {...props}
            // Pass our onInputSizeChanged callback so the Composer reports size changes:
            onInputSizeChanged={(layout: { width: number; height: number }) => {
              if (!initialMeasured.current) {
                console.log('here' + layout.height);
                initialMeasured.current = true;
                setDefaultComposerHeight(layout.height);
              } else {
                // For subsequent updates, only change if the difference is significant (e.g. > 5px)
                if (Math.abs(layout.height - inputHeight) > 10) {
                  setInputHeight(layout.height);
                }
              }
            }}
            composerHeight={inputHeight}
            textInputStyle={[
              styles.font_thirteen,{
              // Do not set an explicit height here—Composer will use composerHeight.
              minHeight: Platform.OS === 'ios' ? 40 : MIN_COMPOSER_HEIGHT,
              maxHeight: 200,
              fontFamily: 'regular',
          
              color: 'white',
              lineHeight: 20,
              borderRadius: 30,
              padding: 10,
              paddingTop: 12,
              backgroundColor: '#3D3C39',
            }]}
            text={text}
            onTextChanged={(newText: string) => setTextInputValue(newText)}
            multiline={true}
          />
          {showEmojiPicker && (
            <EmojiPicker
              onEmojiSelected={handleEmojiSelected}
              open={showEmojiPicker}
              onClose={toggleEmojiPicker}
            />
          )}
        </View>
      </View>
    );
  };

  const DaySeparator = ({ date }: { date: string }) => {
    return (
      <View style={{ alignItems: 'center', marginVertical: 10 }}>
        <Text style={{ fontWeight: 'bold', color: theme.colors.textSecondary }}>{date}</Text>
      </View>
    );
  };

  const groupedMessages = useMemo(() => {
    if (!messages || messages.length === 0) return [];

    // First, sort messages in ascending order by createdAt (oldest first)
    const sortedMessages = [...messages].sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    const grouped: any[] = [];
    let lastDate = '';

    sortedMessages.forEach((msg) => {
      const messageDate = new Date(msg.createdAt).toDateString();
      // Insert a day separator when the date changes
      if (messageDate !== lastDate) {
        grouped.push({
          _id: `day-${msg._id}`, // ensure a unique key
          type: 'day',
          date: messageDate,
        });
        lastDate = messageDate;
      }
      grouped.push(msg);
    });

    // Because your FlatList is inverted (newest on top), reverse the grouped array:
    return grouped.reverse();
  }, [messages]);

  const customInputToolbar = (props: any) => {
    return (
      <InputToolbar
        {...props}
        containerStyle={styles.inputToolbar}
        renderComposer={(composerProps: any) => renderComposer(composerProps)}
        renderSend={(sendProps: any) =>
          renderSend({
            ...sendProps,
            text,
            onTextChange: handleComposerTextChanged,
          })
        }
      />
    );
  };

  function showAttachmentDialogs() {
    return <View style={{ flex: 0 }}></View>;
  }
  const [flatListKey, setFlatListKey] = useState(Date.now().toString()); //for flat list re-rendering

  useEffect(() => {
    // Force FlatList to re-render when messages change significantly
    setFlatListKey(Date.now().toString());
  }, []);

  const renderMessage = useCallback(
    ({ item }: { item: any }) => {
      if (item.type === 'day') {
        // Render the day separator
        return <DaySeparator date={item.date} />;
      }
      // NO NEED FOR DEFENSIVE CHECKS here anymore, because formatMessage handles it
      // const giftedChatItem = { ...item }; // formatMessage *already* creates the correct format

      const messagePosition = item.senderId === myId ? 'right' : 'left';
      console.log(item, 'item', myId);
      return (
        <TouchableOpacity
          onLongPress={() => onLongPress(item)}
          activeOpacity={0.7}
          style={{ marginHorizontal: 8, marginVertical: 4 }}
        >
          {renderBubble({ currentMessage: item, position: messagePosition })}
        </TouchableOpacity>
      );
    },
    [myId, renderBubble, renderTime]
  );

  const renderFooter = () => {
    if (isLoadingOlderMessages) {
      return (
        <View style={{ padding: 10 }}>
          <ActivityIndicator size='small' />
        </View>
      );
    }
    return null;
  };
  const renderHeader = () => {
    // Your existing header code, but now inside a function
    return (
      <View style={styles.headerContainer}>
        <View
          style={{ flex: 1, flexDirection: 'row', alignItems: 'center', paddingLeft: 20 }}
        >
          <TouchableOpacity
            onPress={() => {
              navigation.goBack();
            }}
            hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
          >
            <Ionicons name='chevron-back' size={22} color={theme.colors.text} />
          </TouchableOpacity>

          {openSearch ? (
            <TextInput
              placeholderTextColor={theme.colors.textPlaceholder}
              style={styles.searchInput}
              onChangeText={setSearchInput}
              placeholder='Search Chat'
              value={searchInput}
            />
          ) : (
            <TouchableOpacity
              onPress={() => {
                if (userDetails && userDetails._id !== 'bot') {
                  navigation.push('Home', {
                    screen: 'Profile',
                    params: { userDetails },
                  });
                }
              }}
              style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}
            >
              <Image
                source={
                  userDetails &&
                  userDetails.profile_picture &&
                  userDetails.profile_picture?.length > 0
                    ? { uri: userDetails.profile_picture }
                    : require('app/assets/connectify-thumbnail.png')
                }
                style={{
                  width: 30,
                  height: 30,
                  borderRadius: 25,
                  borderColor: 'white',
                  marginHorizontal: 7,
                }}
              />
              <View
                style={{
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: 1,
                }}
              >
                <CustomText
                  ellipsizeMode='tail'
                  style={styles.userName}
                >
                  {userDetails.first_name} {userDetails.last_name}
                </CustomText>

                {chatDetails &&
                chatDetails.blocked &&
                chatDetails.blocked.length > 0 ? (
                  <CustomText style={styles.blockedText}>
                    Chat has been blocked
                  </CustomText>
                ) : null}
              </View>
            </TouchableOpacity>
          )}
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', paddingRight: 10 }}>
          <TouchableOpacity
            onPress={toggleOpenSearch}
            style={{ justifyContent: 'center', alignItems: 'center' }}
          >
            <FontAwesome
              name={openSearch ? 'times' : 'search'}
              size={20}
              style={{ marginHorizontal: 10 }}
              color={theme.colors.text}
            />
          </TouchableOpacity>
          <Menu
            visible={showMenu}
            onDismiss={closeMenu}
            anchor={
              <TouchableOpacity onPress={openMenu}>
                <View
                  style={{
                    alignItems: 'flex-end',
                    flexDirection: 'row',
                    padding: rv(3),
                  }}
                >
                  <MenuSVG width={15} height={15} />
                </View>
              </TouchableOpacity>
            }
          >
            <Menu.Item
              onPress={() => {
                if (userDetails) {
                  navigation.push('Home', {
                    screen: 'Profile',
                    params: { userDetails },
                  });
                }
              }}
              title={t('View Profile')}
              titleStyle={[styles.font_thirteen,{ fontFamily: 'medium' }]}
            />

            {chatDetails && chatDetails.blocked.includes(myId) ? (
              <Menu.Item
                onPress={unblockUser}
                title={t('BlockedChats_opt1')}
                titleStyle={[styles.font_thirteen,{ fontFamily: 'medium' }]}
              />
            ) : (
              <Menu.Item
                onPress={blockUser}
                title={t('Chats_blockUser')}
                titleStyle={[styles.font_thirteen,{ fontFamily: 'medium' }]}
              />
            )}

            <Menu.Item
              onPress={() => {
                if (userDetails && userDetails._id) {
                  openReportDetails(userDetails._id);
                }
              }}
              title={t('Chats_reportUser')}
              titleStyle={[styles.font_thirteen,{ fontFamily: 'medium' }]}
            />
            <Menu.Item
              onPress={() => {
                navigation.goBack();
              }}
              title={t('Chats_closeChat')}
              titleStyle={[styles.font_thirteen,{ fontFamily: 'medium' }]}
            />
          </Menu>
        </View>
      </View>
    );
  };

  function onLongPress(message: any) {
    console.log('on long press triggered');
    const isMyMessage = message.senderId === myId;
    const options = isMyMessage
      ? ['Reply', 'Delete Message', 'Copy Text', 'Cancel']
      : ['Reply', 'Copy Text', 'Cancel'];
    const cancelIndex = options.length - 1;
    console.log(message,"messagezzz")

    // Set up our custom action sheet state:
    setActionSheetOptions(options);
    setCancelButtonIndex(cancelIndex);
    setActionSheetVisible(true);
    setOnActionSheetSelect(() => (buttonIndex: number) => {
      switch (buttonIndex) {
        case 0: // Reply
          setQuotedData({
            chatId: message.chatId,
            message: message.message,
          });
          break;
        case 1: // Delete (only for sender) or Copy (for receiver)
          if (isMyMessage) {
            deleteMessage(message._id);
            showModal({
              modalVisible: true,
              title: 'Alert',
              message: 'Media deleted',
              setModalVisible: hideModal,
              type: 'success-alert',
              handleConfirm: hideModal,
              handleAlert: hideModal,
            });
          } else {
            // For receivers: implement copy logic if desired.
            Clipboard.setStringAsync(message.text).then(() => {
                console.log("copied")
              });
          }
          break;
        case 2: // Copy Text (for sender; for receiver this would be handled as case 1)
          if (isMyMessage) {
            Clipboard.setStringAsync(message.text).then(() => {
                console.log("copied")
              });
            // showToast('Copied to clipboard');
          }
          break;
        default:
          break;
      }
    });
  }

  return (
    <Provider>
      <ActionSheetProvider>
        <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
          {renderHeader()}
          <SelectContact
            show={showSelectContact}
            setShow={setShowSelectContact}
            setContact={setContactDetails}
          />
          <ReportDialog
            show={showReportDialog}
            setShow={setShowReportDialog}
            reportDetails={reportDetails}
          />

          {pageNumber === 1 && loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size='large' color='#0000ff' />
            </View>
          ) : (
            <TouchableWithoutFeedback
              onPress={() => {
                Keyboard.dismiss();
                setShowAttachmentDialog(false);
              }}
            >
              <KeyboardAvoidingView
                style={styles.keyboardAvoidingView}
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 0  : 0}
              >
                <FlatList
                  key={flatListKey}
                  data={groupedMessages}
                  inverted
                  renderItem={renderMessage}
                  keyExtractor={(item) => item._id}
                  ListFooterComponent={renderFooter}
                  onEndReached={() => {
                    // Prevent duplicate calls if already loading or no more pages:
                    if (
                      !isLoadingOlderMessages &&
                      pageNumber < totalPageNumber
                    ) {
                      setPageNumber((prev) => prev + 1);
                    }
                  }}
                  onEndReachedThreshold={0.5}
                  style={styles.flatList}
                  contentContainerStyle={{
                    //  flexGrow: 1, //for inverting data

                    paddingBottom: 10,
                  }}
                />
                {chatDetails &&
                !bot &&
                chatDetails.blocked &&
                chatDetails.blocked.length == 0
                  ? customInputToolbar({
                      onSend,
                      user: { _id: myId },
                    })
                  : null}

                {showAttachmentDialog ? (
                  <SelectAttachment
                    show={showAttachmentDialog}
                    openShareAttachment={openShareAttachment}
                    setShowSelectContact={setShowSelectContact}
                    source={'chat'}
                  />
                ) : null}
              </KeyboardAvoidingView>
            </TouchableWithoutFeedback>
          )}
          {actionSheetVisible && (
            <Modal
              transparent
              animationType='slide'
              visible={actionSheetVisible}
            >
              <TouchableWithoutFeedback
                onPress={() => setActionSheetVisible(false)}
              >
                <View style={styles.actionSheetBackground}>
                  <View style={styles.actionSheetContainer}>
                    {actionSheetOptions.map((option, index) => (
                      <TouchableOpacity
                        key={index}
                        onPress={() => {
                          setActionSheetVisible(false);
                          onActionSheetSelect(index);
                        }}
                      >
                        <Text
                          style={[
                            styles.actionSheetItem,
                            {
                              color: index === cancelButtonIndex ? theme.colors.error : theme.colors.text,
                            }
                          ]}
                        >
                          {option}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Modal>
          )}
        </SafeAreaView>
      </ActionSheetProvider>
    </Provider>
  );
};

// Themed Styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  systemMessageText: {
    fontSize: 14,
    color: theme.colors.text,
    fontFamily: 'bold',
  },
  headerContainer: {
    backgroundColor: theme.colors.headerBackground,
    minHeight: 70,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    marginTop: 5,
    paddingHorizontal: 10,
    fontSize: fs("BASE"),
    fontFamily: 'medium',
    color: theme.colors.text,
  },
  userName: {
    color: theme.colors.text,
    fontSize: fs("BASE"),
    fontFamily: 'bold',
  },
  blockedText: {
    marginTop: rv(3),
    color: theme.colors.textSecondary,
    // fontSize: rvf(9),
    fontSize:fs("XS"),
    fontFamily: 'medium',
  },
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  flatList: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  sendingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
  },
  // listTitle: {
  //   fontSize: 22,
  // },
  listDescription: {
    fontSize: 16,
  },
  image: {
    flex: 1,
    resizeMode: 'contain',
  },
  imageThumbnail: {
    resizeMode: 'cover',
    height: 22,
    width: 22,
  },
  scrollToBottomContainer: {
    height: 40,
    width: 40,
    backgroundColor: theme.colors.surface,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bubbleContainer: {
    marginBottom: 5,
    padding: 3,
    
  },

  myBubbleContainer: {
    alignItems: 'flex-end', // Align to the right for my messages
  },
  otherBubbleContainer: {
    alignItems: 'flex-start', // Align to the left for other messages
  },
  bubble: {
    padding: 10,
    marginHorizontal: 5,
    // Add other common styles here
  },

  messageText: {
    fontSize:fs("BASE"),

  },

  myMessageText: {
    color: theme.colors.chatTextMy,
    fontFamily: 'regular',
  },
  otherMessageText: {
    color: theme.colors.chatTextOther,
    fontFamily: 'regular',
  },
  time: {
    fontSize: fs("XS"), //for 9 we use 10
    color: theme.colors.textSecondary,
  },
  sendContainer: {
    flexDirection: 'row',
    alignItems: 'center', // Vertical centering
    justifyContent: 'flex-end', // Push content to the right
    paddingRight: 10, // Add some right padding
    marginBottom: 5,
    marginRight: 5,
  },
  attachmentButton: {
    marginRight: 10,
    // padding is probably not needed here
  },
  sendButton: {
    // padding: 8, // Padding might not be needed, depending on your SVG
    // Add borderRadius if you want rounded corners
  },

  inputToolbar: {
    backgroundColor: theme.colors.surface,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  composer: {
    marginTop: rv(10),
    backgroundColor: theme.colors.inputBackground,
    padding: 10,
    borderRadius: 20,
    color: theme.colors.text,
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
  },
  quotedContainer: {
    backgroundColor: theme.colors.surfaceVariant,
    padding: 8,
    borderRadius: 8,
    maxHeight: 60,
    marginBottom: 4,
  },
  quotedHeader: {
    color: theme.colors.text,
    fontSize:fs("BASE"),
    fontWeight: 'bold',
    marginBottom: 2,
  },
  quotedText: {
    color: theme.colors.textSecondary,
    fontSize:fs("BASE")
  },
  // Action Sheet
  actionSheetBackground: {
    flex: 1,
    backgroundColor: theme.colors.overlayBackground,
    justifyContent: 'flex-end',
  },
  actionSheetContainer: {
    backgroundColor: theme.colors.surface,
    padding: 20,
  },
  actionSheetItem: {
    fontSize: 18,
    padding: 10,
    textAlign: 'left',
    color: theme.colors.text,
  },
  font_thirteen: {
      fontSize:fs("MD")
  },
  font_nine:{
    fontSize: fs("XS"), // FOR 9 WE USE 10
  }

}));

export default function PrivateChat({ navigation, route }: any) {
  return (
    <SafeAreaProvider>
      <PrivateChatContainer navigation={navigation} route={route} />
    </SafeAreaProvider>
  );
}
