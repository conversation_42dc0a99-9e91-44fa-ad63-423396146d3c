import React, { useState, useEffect, useContext, useRef } from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import Loader from 'app/components/elements/Loader';
import PreviewAudio from 'app/components/media/preview-audio';
import PreviewVideo from 'app/components/media/preview-video';
import { CustomText } from 'app/components/elements';
import { Axios } from 'app/api/axios';
import { ShowAlert } from 'app/providers/toast';
import { useCreateComment } from 'app/redux/topic/hooks';
import { useTranslation } from 'react-i18next';
import { hideModal, showModal } from 'app/providers/modals';
import { SafeAreaView } from 'react-native-safe-area-context';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme } from 'app/hooks/useTheme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
import { createOptimizedThemedStyles } from 'app/theme';

function ShareAttachment({ navigation, route }: any) {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const { mutateAsync: createComment, isLoading: isLoadingCreateComment } =
    useCreateComment();

  const sharedData = route.params;

  let contact: any = null;

  if (sharedData && sharedData.attachmentType == 'contact') {
    contact = JSON.parse(sharedData.attachment);
  }

  let [loading, setLoading] = useState(false);
  const [caption, setCaption] = useState<string>('');

  async function uploadAttachment() {
    if (loading) {
      return;
    }
    setLoading(true);
    try {
      if (sharedData.chatType == 'group' || sharedData.chatType == 'chat') {
        await uploadChatAttachments();
      } else if (sharedData.chatType == 'topic-comment') {
        await uploadTopicCommentAttachments();
      }
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setLoading(false);
    }
  }

  async function uploadChatAttachments() {
    let URL = null;
    if (sharedData.attachmentType == 'image') {
      URL = '/chats/upload-image-attachment/' + sharedData.chatId;
    } else if (sharedData.attachmentType == 'video') {
      URL = '/chats/upload-video-attachment/' + sharedData.chatId;
    } else if (sharedData.attachmentType == 'audio') {
      URL = '/chats/upload-audio-attachment/' + sharedData.chatId;
    } else if (sharedData.attachmentType == 'document') {
      URL = '/chats/upload-document-attachment/' + sharedData.chatId;
    } else if (sharedData.attachmentType == 'contact') {
      URL = '/chats/upload-contact-attachment/' + sharedData.chatId;
    } else {
      return;
    }

    let formData = new FormData();

    if (sharedData.attachmentType == 'contact') {
      formData.append('contact', sharedData.attachment);
    } else {
      formData.append('file', sharedData.attachment);
    }

    formData.append('type', sharedData.chatType);
    if (caption && caption.trim()) {
      formData.append('message', caption.trim());
    }
    setLoading(true);

    await Axios({
      method: 'post',
      url: URL,
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' },
    })
      .then((response) => {
        showModal({
          modalVisible: true,
          title: 'Success',
          message: response.data.message,
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: () => {
            hideModal();
          },
          handleAlert: () => {
            hideModal();
          },
          navigation,
        });
        navigation.goBack(null);
      })
      .catch((err) => {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: err.message,
          setModalVisible: hideModal,
          type: 'alert',
          handleConfirm: () => {
            hideModal();
          },
          handleAlert: () => {
            hideModal();
          },
          navigation,
        });
        navigation.goBack(null);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  async function uploadTopicCommentAttachments() {
    if (!sharedData.post) {
      return;
    }

    let formData = new FormData();

    formData.append('file', sharedData.attachment);
    formData.append('attachmentType', sharedData.attachmentType);
    if (caption && caption.trim()) {
      formData.append('comment', caption.trim());
    }

    let payload = {
      postId: sharedData.post._id,
      data: formData,
    };

    try {
      const response = await createComment(payload);
      if (response.success) {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: response.message,
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: () => {
            hideModal();
          },
          handleAlert: () => {
            hideModal();
          },
        });
        navigation.goBack(null);
      } else {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: response.message,
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: () => {
            hideModal();
          },
          handleAlert: () => {
            hideModal();
            navigation.goBack();
          },
        });
      }
    } catch (error: any) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: error.response.data.message,
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: () => {
          hideModal();
        },
        handleAlert: () => {
          hideModal();
        },
      });
    }
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View style={{ flex: 1 }}>
          <Loader loading={loading || isLoadingCreateComment} />
          <View
            style={{
              margin: 13,
              marginTop: 20,
              marginBottom: hp('0.5%'),
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <TouchableOpacity
              onPress={() => {
                navigation.goBack(null);
              }}
              style={{
                width: 25,
                height: 25,
                justifyContent: 'center',
              }}
            >
              <FontAwesome name='arrow-left' size={20} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>

          <View
            style={{
              flex: 1,
              paddingHorizontal: wp('10%'),
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {sharedData.attachmentType == 'image' ? (
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Image
                  source={sharedData.attachment}
                  resizeMode={'contain'}
                  style={{
                    width: wp('80%'),
                    height: hp('50%'),
                    maxWidth: wp('80%'),
                    maxHeight: hp('50%'),
                  }}
                />
              </View>
            ) : sharedData.attachmentType == 'video' ? (
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  backgroundColor: theme.colors.surface,
                }}
              >
                <PreviewVideo
                  videoLink={sharedData.attachment.uri}
                  width={wp('80%')}
                  height={hp('40%')}
                  style={{ alignSelf: 'center' }}
                />
              </View>
            ) : sharedData.attachmentType == 'audio' ? (
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                }}
              >
                <PreviewAudio audioLink={sharedData.attachment.path} />
              </View>
            ) : sharedData.attachmentType == 'contact' ? (
              <View
                style={{
                  flex: 1,
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                {contact ? (
                  <View
                    style={{
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <FontAwesome
                      name='address-card'
                      size={100}
                      color={theme.colors.textSecondary}
                    />
                    <CustomText
                      style={{
                        paddingTop: 20,
                        color: theme.colors.text,
                        fontFamily: 'bold',
                        fontSize: fs('BASE'),
                      }}
                    >
                      {contact.name}
                    </CustomText>
                    {contact.phoneNumbers.map((item: any, index: number) => (
                      <CustomText
                        key={index}
                        style={{
                          color: theme.colors.textSecondary,
                          fontSize: fs('BASE'),
                        }}
                      >
                        {item.number}
                      </CustomText>
                    ))}
                  </View>
                ) : null}
              </View>
            ) : sharedData.attachmentType == 'document' ? (
              <View
                style={{
                  flex: 1,
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <FontAwesome
                  name='paperclip'
                  size={100}
                  color={theme.colors.textSecondary}
                />
                <CustomText
                  style={{
                    paddingTop: 20,
                    color: theme.colors.text,
                    fontFamily: 'bold',
                    fontSize: fs('BASE'),
                  }}
                >
                  Document Attached
                </CustomText>
              </View>
            ) : null}

            <View
              style={{
                marginTop: hp('2%'),
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <TextInput
                style={{
                  flex: 1,
                  height: 'auto',
                  color: theme.colors.text,
                  fontFamily: 'regular',
                  fontSize: fs('BASE'),
                  padding: 10,
                  borderRadius: 10,
                  backgroundColor: theme.colors.surface,
                }}
                multiline={true}
                placeholderTextColor={theme.colors.textPlaceholder}
                placeholder={t('profile_message')}
                onChangeText={(text) => setCaption(text)}
                value={caption}
              />
              <TouchableOpacity
                onPress={uploadAttachment}
                style={{
                  width: 50,
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  opacity: loading ? 0.5 : 1,
                  marginLeft: 10,
                }}
                disabled={loading}
              >
                <Image
                  source={require('app/assets/floating.png')}
                  style={{ width: 50, height: 50 }}
                  resizeMethod='resize'
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

export default ShareAttachment;

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  font_base: {
    fontSize: fs('BASE'),
  },
}));