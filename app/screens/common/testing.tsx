import { Button } from 'app/components/Button'
import { Results } from 'app/components/Results'
import React from 'react'
import EmojiPicker from 'rn-emoji-keyboard'
import {EmojiType}   from 'rn-emoji-keyboard'
import { View, ScrollView } from 'react-native'
import { CustomButton } from 'app/components/elements/Button'
import { CustomText } from 'app/components/elements/Text'
import FollowButton from 'app/components/elements/FollowButton'
import { ThemeToggleButton } from 'app/components/ThemeToggleButton'
import { useTheme, createThemedStyles, createOptimizedThemedStyles } from 'app/theme'
import { responsiveValue as rv } from 'app/providers/responsive-value'
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize'

// Create themed styles for the test screen
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
  },
  section: {
    marginBottom: theme.spacing.xl,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  sectionTitle: {
    // fontSize: rv(18),
    fontSize:fs("LG"),
    fontFamily: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  buttonContainer: {
    gap: theme.spacing.sm,
  },
}));

export default function () {
  const [result, setResult] = React.useState<string>()
  const [isModalOpen, setIsModalOpen] = React.useState<boolean>(false)
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const handlePick = (emoji: EmojiType) => {
    console.log(emoji)
    setResult(emoji.emoji)
    setIsModalOpen((prev) => !prev)
  }

  return (
    <ScrollView style={styles.container}>
      {/* Theme Toggle */}
      <View style={styles.section}>
        <CustomText style={styles.sectionTitle}>Theme Testing</CustomText>
        <ThemeToggleButton />
        <CustomText style={{ color: theme.colors.textSecondary, textAlign: 'center', marginTop: rv(8) }}>
          Current theme: {theme.mode}
        </CustomText>
      </View>

      {/* Custom Button Testing */}
      <View style={styles.section}>
        <CustomText style={styles.sectionTitle}>Custom Button Themes</CustomText>
        <View style={styles.buttonContainer}>
          <CustomButton
            label="Primary Button"
            buttonTheme="primary"
            onPress={() => console.log('Primary pressed')}
          />
          <CustomButton
            label="Secondary Button"
            buttonTheme="secondary"
            onPress={() => console.log('Secondary pressed')}
          />
          <CustomButton
            label="Tertiary Button"
            buttonTheme="tertiary"
            onPress={() => console.log('Tertiary pressed')}
          />
          <CustomButton
            label="Quaternary Button"
            buttonTheme="quaternary"
            onPress={() => console.log('Quaternary pressed')}
          />
          <CustomButton
            label="Quinary Button"
            buttonTheme="quinary"
            onPress={() => console.log('Quinary pressed')}
          />
          <CustomButton
            label="Loading Button"
            buttonTheme="primary"
            loading={true}
            onPress={() => console.log('Loading pressed')}
          />
          <CustomButton
            label="Disabled Button"
            buttonTheme="primary"
            disabled={true}
            onPress={() => console.log('Disabled pressed')}
          />
        </View>
      </View>

      {/* Text Component Testing */}
      <View style={styles.section}>
        <CustomText style={styles.sectionTitle}>Text Component Types</CustomText>
        <CustomText textType="regular">Regular text</CustomText>
        <CustomText textType="medium">Medium text</CustomText>
        <CustomText textType="bold">Bold text</CustomText>
        <CustomText textType="semi-bold">Semi-bold text</CustomText>
        <CustomText textType="extra-bold">Extra-bold text</CustomText>
        <CustomText textType="light">Light text</CustomText>
      </View>

      {/* Simple Button Testing */}
      <View style={styles.section}>
        <CustomText style={styles.sectionTitle}>Simple Button</CustomText>
        <Button onPress={() => setIsModalOpen(true)} label="Open Emoji Picker" />
      </View>

      {/* Follow Button Testing */}
      <View style={styles.section}>
        <CustomText style={styles.sectionTitle}>Follow Button</CustomText>
        <FollowButton userId="test-user-id" />
      </View>

      {/* Original Emoji Picker */}
      <View style={styles.section}>
        <CustomText style={styles.sectionTitle}>Emoji Picker Result</CustomText>
        <Results label={result} />
      </View>

      <EmojiPicker
        onEmojiSelected={handlePick}
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </ScrollView>
  )
}