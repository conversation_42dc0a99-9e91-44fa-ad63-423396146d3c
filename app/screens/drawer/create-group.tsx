import {
  View,
  TextInput,
  Image,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  
} from 'react-native';
import React, { FunctionComponent, useState, useContext, useEffect, useCallback} from 'react';
import { useFocusEffect } from '@react-navigation/native';
import HeaderTitle from 'app/components/HeaderTitle';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import SelectImageDialog from 'app/components/dialogs/select-image';
import Loader from 'app/components/elements/Loader';
import { CustomButton, CustomText } from 'app/components/elements';
import SelectInterests from 'app/components/elements/SelectInterests';
import { CreateGroupSVG, WarnInfoSVG } from 'app/providers/svg/loader';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { Axios } from 'app/api/axios';
import { useTranslation } from 'react-i18next';
import { UploadPhotoSVG } from 'app/providers/svg/loader';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import CustomModal from '../../components/elements/Modals';
import { queryClient } from 'app/redux/user/hooks';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useCreateGroup } from 'app/redux/group/hooks';
import { navigate } from 'app/navigation/root';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

type PostATopicProps = {
  navigation: any;
};

// Container Component
const CreateGroupContainer: FunctionComponent<PostATopicProps> = ({ navigation }) => {
  const [showSelectImage, setShowSelectImage] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [imageFile, setImageFile] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [groupName, setGroupName] = useState<string>('');
  const [groupDesc, setGroupDesc] = useState<string>('');
  const [interest, setInterest] = useState<string>('');
  const [modalMessage, setModalMessage] = useState<string>('');
  const { mutateAsync: createGroups, isLoading: isLoadingCreateGroup } =
    useCreateGroup();

  const { t } = useTranslation();
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  

  const handleConfirm = async () => {
    setModalVisible(false);
  };

  async function createGroup() {
    if (interest.length === 0) {
      setModalMessage('You need to select a tag');
      setModalVisible(true);
      return;
    }
    if (groupName.length === 0) {
      setModalMessage('A group name is required');
      setModalVisible(true);
      return;
    }
    if (groupDesc.length == 0) {
      setModalMessage('A group Description is required');
      setModalVisible(true);
      return;
    }

    const formData = new FormData();
    formData.append('name', groupName);
    formData.append('description', groupDesc);
    formData.append('tag', interest);
    if (imageFile) {
      formData.append('file', imageFile);
    }

    setLoading(true);
    await Axios({
      method: 'POST',
      url: '/group/create',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' },
      
    })
      .then((response: any) => {
        //showToast(response.data.message);
        queryClient.invalidateQueries({queryKey:['my-groups']})
        navigation.navigate('Home', {
          screen: 'Quiz',
          params: {
             screen: 'GameMode',
             params: {
              screen: 'GroupsScreen'
           },
          },
        })
      })
      .finally(() => {
        setLoading(false);
        setImageFile(null);
        setGroupName('');
        setGroupDesc('');
        setInterest('');
      });
     
  }

  console.log(imageFile,"imagae")

  return (
    <CreateGroupPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      t={t}
      modalVisible={modalVisible}
      setModalVisible={setModalVisible}
      modalMessage={modalMessage}
      handleConfirm={handleConfirm}
      loading={loading}
      showSelectImage={showSelectImage}
      setShowSelectImage={setShowSelectImage}
      setImageFile={setImageFile}
      imageFile={imageFile}
      groupName={groupName}
      setGroupName={setGroupName}
      groupDesc={groupDesc}
      setGroupDesc={setGroupDesc}
      interest={interest}
      setInterest={setInterest}
      createGroup={createGroup}
    />
  );
};

// Presentation Component
interface CreateGroupPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  t: any;
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  modalMessage: string;
  handleConfirm: () => void;
  loading: boolean;
  showSelectImage: boolean;
  setShowSelectImage: (show: boolean) => void;
  setImageFile: (file: any) => void;
  imageFile: any;
  groupName: string;
  setGroupName: (name: string) => void;
  groupDesc: string;
  setGroupDesc: (desc: string) => void;
  interest: string;
  setInterest: (interest: string) => void;
  createGroup: () => void;
}

const CreateGroupPresentation: React.FC<CreateGroupPresentationProps> = ({
  theme,
  styles,
  navigation,
  t,
  modalVisible,
  setModalVisible,
  modalMessage,
  handleConfirm,
  loading,
  showSelectImage,
  setShowSelectImage,
  setImageFile,
  imageFile,
  groupName,
  setGroupName,
  groupDesc,
  setGroupDesc,
  interest,
  setInterest,
  createGroup,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        
      {modalVisible && (
          <CustomModal
          title='Alert'
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type='alert'
          message={modalMessage}
          handleConfirm={handleConfirm}
          navigation
        />
        )}
        <HeaderTitle title={t('CreateGroup_title')} navigation={navigation} />

        <Loader loading={loading} />
        <SelectImageDialog
          show={showSelectImage}
          setShow={setShowSelectImage}
          setImage={setImageFile}
        />

        <View
          style={{
            flex: 1,
            flexDirection: 'column',
            paddingHorizontal: wp('8%'),
          }}
        >
          <View
            style={{
              alignItems: 'center',
              marginTop: 10,
            }}
          >
            <TouchableOpacity
              onPress={() => setShowSelectImage(true)}
              activeOpacity={0.7}
              style={{
                borderRadius: 35,
                height: 70,
                width: 70,
                alignItems: 'center',
                flexDirection: 'row',
                marginLeft: -30,
              }}
            >
              {imageFile ? (
                <Image
                  source={{ uri: imageFile.uri }}
                  style={{
                    width: 100,
                    height: 100,
                    borderRadius: 50,
                  }}
                />
              ) : (
                <View
                  style={{
                    backgroundColor: '#E7E7E7',
                    padding: 20,
                    borderRadius: 80,
                  }}
                >
                  <UploadPhotoSVG width={wp('15')} height={wp('15')} />
                  {/* <CreateGroupSVG width={50} height={50} /> */}
                </View>
              )}
            </TouchableOpacity>
          </View>

          <View
            style={{
              marginTop: 40,
              flexDirection: 'column',
            }}
          >
            <CustomText style={styles.label}>
              {t('CreateGroup_groupName')}{' '}
            </CustomText>

            <TextInput
              style={styles.textInput}
              maxLength={20}
              onChangeText={(text) => {
                setGroupName(text);
              }}
              placeholder={t('CreateGroup_groupName')}
              placeholderTextColor={theme.colors.textPlaceholder}
              inlineImagePadding={20}
              value={groupName}
            />
          </View>

          <View
            style={{
              flexDirection: 'column',
            }}
          >
            <CustomText style={styles.label}>
              {t('CreateGroup_Description')}{' '}
            </CustomText>

            <TextInput
              style={styles.textInput}
              maxLength={70}
              onChangeText={(text) => {
                setGroupDesc(text);
              }}
              placeholder={t('CreateGroup_Description')}
              placeholderTextColor={theme.colors.textPlaceholder}
              inlineImagePadding={20}
              value={groupDesc}
            />
          </View>

          <View
            style={{
              marginTop: 20,
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <CustomText style={styles.tagLabel}>
              Select a tag relevant to this group
            </CustomText>

            <WarnInfoSVG width={16} />
          </View>
          <SelectInterests interest={interest} setInterest={setInterest} />

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-end',
              marginBottom: 50,
            }}
          >
            <CustomButton
              label={t('CreateGroup_title')}
              onPress={() => createGroup()}
              buttonTheme='primary'
              loading={loading}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
    backgroundColor: theme.colors.background,
    flexDirection: 'column',
  },
  label: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    marginBottom: 5,
    fontFamily: 'medium',
  },
  tagLabel: {
    fontSize: fs("BASE"),
    marginRight: 10,
    marginTop: 5,
    color: theme.colors.textSecondary,
    fontFamily: 'bold',
  },
  textInput: {
    height: 60,
    borderRadius: 14,
    paddingLeft: 20,
    backgroundColor: theme.colors.inputBackground || theme.colors.surfaceVariant,
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
    fontSize: fs("BASE"),
    fontFamily: 'medium',
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder || theme.colors.border,
  },
}));

export default CreateGroupContainer;
