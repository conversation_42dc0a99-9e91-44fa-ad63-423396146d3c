import {
  View,
  Text,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  RefreshControl,
  Image,
} from 'react-native';
import React, { useState, useEffect } from 'react';
import HeaderTitle from 'app/components/HeaderTitle';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CustomButton, CustomText } from 'app/components/elements';
import BuyCreditModal from 'app/components/elements/buy-credit-modal';
import { ShowAlert } from 'app/providers/toast';
import { MAIN_URL } from 'app/api/axios';
import {
  useGetCredit,
  useGetSubscription,
  useGetCreditTransactionHistory,
} from 'app/redux/credit/hook';
import { queryClient } from 'app/redux/user/hooks';
import WebView from 'react-native-webview';
import {
  responsiveValue,
  responsiveValue as rv,
} from 'app/providers/responsive-value';
import moment from 'moment';
import {
  useGetFreeCredit,
  useGetFreeSubscription,
} from '../../../redux/credit/hook';

import { formatAmount } from 'app/helpers/formatAmout';
import { useTranslation } from 'react-i18next';
import CustomModal from 'app/components/elements/Modals';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { showModal, hideModal } from 'app/providers/modals';
import { useTheme, createThemedStyles, createOptimizedThemedStyles } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
const Loading = require('app/assets/loading.gif');

const CreditScreen = ({ navigation, route }: any) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const [showBuyCredit, setShowBuyCredit] = useState(false);
  const {
    data: subscription,
    isLoading: isSubscriptionLoading,
    refetch,
  } = useGetSubscription();

  const [paymentLink, setPaymentLink] = useState<string>('');

  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [type, setType] = useState<any>(null);
  const [title, setTitle] = useState('');
  const { mutate: getFreeSubscription, isLoading: freeSubscriptionLoading } =
    useGetFreeSubscription({
      onSuccess: (response: any) => {
        setAlertMessage(response.message);
        setType('success-alert');
        setTitle('Success');
        setModalVisible(true);
      },
      onError: (error: any) => {
        setAlertMessage(
          error.message ||
            "Looks like you've already received your free subscription in the past 5 days"
        );
        setType('alert');
        setTitle('Oops');
        setModalVisible(true);
      },
    });

  function processPaymentError(error: any) {
    const { nativeEvent } = error;
    setPaymentLink('');
    console.log('WebView error: ', nativeEvent);
  }

  function watchPaymentProcess(data: any) {
    console.log(data, '== watching');

    if (data.url.includes(`${MAIN_URL}/success?status=successful`)) {
      setPaymentLink('');
      setAlertMessage(
        'Payment was successful, Your credits will be updated shortly'
      );
      setType('success-alert');
      setTitle('Transaction Successful');
      setModalVisible(true);

      setTimeout(() => {
        queryClient.invalidateQueries(['credit', 'credit-transaction-history']);
      }, 3000);
    }

    if (data.url.includes(`${MAIN_URL}/success?status=cancelled`)) {
      setPaymentLink('');
      setType('alert');
      setTitle('Failed Transaction');
      setAlertMessage('Payment was cancelled');
      setModalVisible(true);
    }
  }

  useEffect(() => {
    if (route.params && route.params.message) {
      setShowBuyCredit(true);
    }
  }, [route.params]);

  useFocusEffect(
    React.useCallback(() => {
      refetch();
      return () => {
        console.log('');
      };
    }, [subscription])
  );

  const handleConfirm = () => {
    setModalVisible(false);
    setType(null);
    setTitle('');
    setAlertMessage('');
  };

  const handleAlert = () => {
    setShowBuyCredit(true);
    setModalVisible(false);
    setType(null);
    setTitle('');
    setAlertMessage('');
  };

  return (
    <>
      {paymentLink ? (
        <SafeAreaView style={styles.safeAreaContainer}>
          <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
          <WebView
            source={{ uri: paymentLink }}
            scalesPageToFit
            onError={processPaymentError}
            onNavigationStateChange={watchPaymentProcess}
          />
        </SafeAreaView>
      ) : (
        <SafeAreaView style={styles.safeAreaContainer}>
          <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
          {modalVisible && type && (
            <CustomModal
              modalVisible={modalVisible}
              setModalVisible={(visible) => {
                setModalVisible(visible);
                if (!visible) {
                  setType(null);
                  setTitle('');
                  setAlertMessage('');
                }
              }}
              type={type}
              message={alertMessage}
              title={title}
              handleConfirm={handleConfirm}
              handleAlert={handleAlert}
              navigation={navigation}
            />
          )}
          <HeaderTitle title='Membership Access' navigation={navigation} />

          <View
            style={{
              flex: 1,
              width: '100%',
              flexDirection: 'column',
              justifyContent: 'center',
            }}
          >
            {/* Subscription Information */}
            <ScrollView
              contentContainerStyle={styles.scrollViewContent}
              refreshControl={
                <RefreshControl
                  refreshing={isSubscriptionLoading}
                  onRefresh={refetch}
                />
              }
            >
              <View
                style={{
                  width: '100%',
                }}
              >
                <View style={{ flexDirection: 'column', alignItems: 'center' }}>
                  <CustomText style={styles.membershipTitle}>
                    Membership days left
                  </CustomText>
                  {isSubscriptionLoading ? (
                    // Show a loading GIF
                    <Image
                      source={Loading}
                      style={{
                        width: rv(50),
                        height: rv(50),
                        alignSelf: 'center',
                        marginVertical: rv(20),
                      }}
                    />
                  ) : subscription ? (
                    // If not loading and subscription exists
                    <CustomText style={styles.daysLeftText}>
                      {subscription.daysLeft}{' '}
                      {subscription.daysLeft === 1 ? 'day' : 'days'}
                    </CustomText>
                  ) : (
                    // If not loading but subscription is null
                    <CustomText style={styles.daysLeftText}>
                      No Subscription
                    </CustomText>
                  )}
                  <CustomButton
                    label='Sponsorship Plans'
                    onPress={() => {
                      setShowBuyCredit(true);
                    }}
                    buttonTheme='quinary'
                    style={{
                      borderRadius: 10,
                      color: 'white',
                      marginTop: rv(10),
                    }}
                  />
                  <CustomButton
                    label='Free Plan'
                    onPress={() => {
                      // Check if the subscription exists and if daysLeft is not 0
                      if (subscription && subscription.daysLeft !== 0) {
                        setAlertMessage(
                          'You still have days left on your current plan'
                        );
                        setType('alert');
                        setTitle('Free Plan Unavailable');
                        setModalVisible(true);
                      } else {
                        // Only call getFreeSubscription if daysLeft is 0
                        getFreeSubscription();
                      }
                    }}
                    buttonTheme='tertiary'
                    style={{
                      borderRadius: 10,
                    }}
                    loading={freeSubscriptionLoading}
                  />
                </View>
                <View
                  style={{
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}
                >
                  <View style={styles.currentPlanContainer}>
                    <Text style={styles.currentPlanLabel}>
                      Current Plan
                    </Text>
                    {subscription && subscription.subscription ? (
                      <Text style={styles.currentPlanText}>
                        {subscription.subscription.subscriptionType
                          .charAt(0)
                          .toUpperCase() +
                          subscription.subscription.subscriptionType.slice(
                            1
                          )}{' '}
                        Plan
                      </Text>
                    ) : (
                      <Text style={styles.currentPlanText}>
                        No Active Subscription
                      </Text>
                    )}
                  </View>
                </View>
              </View>
            </ScrollView>

            {showBuyCredit ? (
              <BuyCreditModal
                isOpen={showBuyCredit}
                setIsOpen={setShowBuyCredit}
                currentPlan={subscription?.subscription?.subscriptionType || undefined}
              />
            ) : null}
          </View>
        </SafeAreaView>
      )}
    </>
  );
};

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  safeAreaContainer: {
    backgroundColor: theme.colors.background,
    flex: 1,
    flexDirection: 'column',
  },
  scrollViewContent: {
    backgroundColor: theme.colors.background,
    flexDirection: 'column',
    paddingHorizontal: rv(20),
  },
  membershipTitle: {
    color: theme.colors.textSecondary,
    // fontSize: 15,
    fontSize: fs("LG"),
    fontFamily: 'semiBold',
  },
  daysLeftText: {
    color: theme.colors.textSecondary,
    fontSize: 40,
    fontFamily: 'semiBold',
  },
  currentPlanContainer: {
    borderColor: theme.colors.border,
    borderRadius: rv(19),
    borderWidth: 1,
    borderStyle: 'solid',
    width: '100%',
    marginTop: rv(51),
    height: rv(248),
    backgroundColor: theme.colors.background,
  },
  currentPlanLabel: {
    fontSize:fs("LG"),
    color: theme.colors.textSecondary,
    marginTop: rv(40),
    marginLeft: rv(33),
    fontFamily: 'medium',
  },
  currentPlanText: {
    fontSize:fs("XL"),
    color: theme.colors.textSecondary,
    marginTop: rv(40),
    textAlign: 'center',
    fontFamily: 'semiBold',
  },
}));

export default CreditScreen;
