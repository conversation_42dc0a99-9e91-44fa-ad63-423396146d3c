import React from 'react';
import { View, ScrollView, SafeAreaView } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTranslation } from 'react-i18next';
import HeaderTitle from 'app/components/HeaderTitle';
import { CustomText, CustomButton } from 'app/components/elements';
import { Linking } from 'react-native';

type DonateScreenProps = {
  navigation: any;
};

// Container Component
const DonateContainer: React.FC<DonateScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const handleDonate = () => {
    // Open donation link or navigate to donation flow
    Linking.openURL('https://pennytots.com/donate').catch((err) =>
      console.warn('Could not open URL:', err)
    );
  };

  return (
    <DonatePresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      t={t}
      handleDonate={handleDonate}
    />
  );
};

// Presentation Component
interface DonatePresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  t: any;
  handleDonate: () => void;
}

const DonatePresentation: React.FC<DonatePresentationProps> = ({
  theme,
  styles,
  navigation,
  t,
  handleDonate,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      <HeaderTitle title="Support Connectify" navigation={navigation} />
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.section}>
            <CustomText style={styles.title}>
              Help Us Grow
            </CustomText>
            <CustomText style={styles.description}>
              Your support helps us maintain and improve the Connectify platform for everyone.
              Every contribution makes a difference in building a stronger community.
            </CustomText>
          </View>

          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Why Donate?
            </CustomText>
            <CustomText style={styles.bulletPoint}>
              • Keep the platform free for all users
            </CustomText>
            <CustomText style={styles.bulletPoint}>
              • Support new feature development
            </CustomText>
            <CustomText style={styles.bulletPoint}>
              • Maintain server infrastructure
            </CustomText>
            <CustomText style={styles.bulletPoint}>
              • Enable community events and programs
            </CustomText>
          </View>

          <View style={styles.buttonContainer}>
            <CustomButton
              label="Donate Now"
              onPress={handleDonate}
              buttonTheme="primary"
            />
          </View>

          <View style={styles.section}>
            <CustomText style={styles.thankYou}>
              Thank you for being part of the Connectify community!
            </CustomText>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: rv(20),
  },
  section: {
    marginBottom: rv(30),
  },
  title: {
    fontSize: fs("XXXXL"),
    fontFamily: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: rv(16),
  },
  description: {
    fontSize: fs("XL"),
    fontFamily: 'regular',
    color: theme.colors.textSecondary,
    lineHeight: rv(24),
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: fs("XXL"),
    fontFamily: 'bold',
    color: theme.colors.text,
    marginBottom: rv(12),
  },
  bulletPoint: {
    fontSize: fs("LG"),
    fontFamily: 'regular',
    color: theme.colors.textSecondary,
    lineHeight: rv(22),
    marginBottom: rv(8),
  },
  buttonContainer: {
    marginVertical: rv(20),
    alignItems: 'center',
  },
  thankYou: {
    fontSize: fs("XL"),
    fontFamily: 'medium',
    color: theme.colors.primary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
}));

export default DonateContainer;