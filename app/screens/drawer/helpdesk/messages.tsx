import {
  View,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  TextInput,
  Image,
  SafeAreaView
} from 'react-native';
import React, { useEffect, useState } from 'react';
import {
  useGetHelpdeskMessages,
  useReplyHelpDeskMessage,
} from '../../../redux/helpdesk/hooks';
import { CustomText } from '../../../components/elements/Text';
import HeaderTitle from 'app/components/HeaderTitle';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '../../../redux/user/reducer';
import { CustomButton } from '../../../components/elements/Button';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import SelectImageDialog from 'app/components/dialogs/select-image';
import AttachmentSVG from 'app/assets/svg/attachment.svg';
import ImageIconSVG from 'app/assets/svg/image-icon.svg';
import CloseSVG from 'app/assets/svg/close.svg';
import { timeAgo } from 'app/helpers/time-ago';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTheme, createThemedStyles, createOptimizedThemedStyles } from 'app/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
  },
  scrollContainer: {
    flexDirection: 'column' as const,
    justifyContent: 'center' as const,
    paddingHorizontal: wp(10),
  },
  titleText: {
    fontSize: fs("MD"),
    marginBottom: 20,
    fontFamily: 'bold',
    color: theme.colors.text,
  },
  replyContainer: {
    width: '100%' as const,
    flexDirection: 'column' as const,
  },
  textInput: {
    backgroundColor: theme.colors.inputBackground,
    height: hp('25%'),
    textAlignVertical: 'top' as const,
    fontFamily: 'semiBold',
    fontSize: fs("BASE"),
    color: theme.colors.text,
    lineHeight: 20,
    fontWeight: '800' as const,
    padding: rv(10),
    borderRadius: 10,
    borderWidth: 2,
    borderColor: theme.colors.inputBorder,
  },
  buttonContainer: {
    flexDirection: 'row' as const,
    flex: 1,
    alignItems: 'center' as const,
  },
  attachmentButton: {
    flexDirection: 'column' as const,
    margin: 5,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    paddingTop: 15,
  },
  attachmentText: {
    paddingTop: 4,
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    fontFamily: 'medium',
  },
  imagePreview: {
    height: 50,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 6,
    paddingHorizontal: 10,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    marginVertical: 0,
  },
  imagePreviewContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  imagePreviewText: {
    marginLeft: 10,
    fontFamily: 'medium',
    color: theme.colors.text,
  },
  closeButton: {
    justifyContent: 'flex-end' as const,
    padding: 5,
  },
  messagesContainer: {
    flex: 1,
    flexDirection: 'column' as const,
  },
  messageItem: {
    flexDirection: 'row' as const,
    marginVertical: 20,
  },
  messageContent: {
    flex: 1,
    flexDirection: 'column' as const,
  },
  messageHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
  },
  senderName: {
    fontSize: fs("BASE"),
    color: theme.colors.text,
    flexWrap: 'nowrap' as const,
    fontFamily: 'bold',
  },
  messageTime: {
    fontSize: fs("BASE"),
    color: theme.colors.textSecondary,
    flexWrap: 'nowrap' as const,
    fontFamily: 'medium',
  },
  messageText: {
    fontSize: fs("BASE"),
    color: theme.colors.text,
    marginTop: 5,
    fontFamily: 'medium',
  },
  messageImage: {
    width: '100%' as const,
    height: hp('30%'),
    borderRadius: wp('2%'),
    borderColor: theme.colors.border,
    borderWidth: wp('0.6%'),
    marginTop: 8,
  },
  floatingButton: {
    position: 'absolute' as const,
    width: 130,
    height: 50,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    right: 30,
    bottom: 50,
  },
}));

const HelpDeskMessagesScreen = ({ navigation, route }: any) => {
  let { helpdeskId, helpdesk } = route.params;

  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const { data: helpdeskMessages } = useGetHelpdeskMessages(helpdeskId);
  const { mutateAsync: replyMessage, isLoading } = useReplyHelpDeskMessage();

  const [content, setContent] = useState('');
  const { t } = useTranslation();

  const [showReply, setShowReply] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [showSelectImage, setShowSelectImage] = useState(false);

  const user = useSelector(userAuthInfo);

  useEffect(() => {
    console.log(helpdeskMessages, '=messages');
  }, [helpdeskMessages]);

  async function submit() {
    let formData = new FormData();

    formData.append('message', content);

    if (selectedImage) {
      formData.append('file', selectedImage);
    }

    let payload = {
      helpdeskId,
      data: formData,
    };

    replyMessage(payload).then(() => {
      setShowReply(false);
      setContent('');
      setSelectedImage(null);
    });
  }

  useEffect(() => {
    console.log(selectedImage, 'select image');
  }, [selectedImage]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={theme.colors.statusBarStyle}
        backgroundColor={theme.colors.background}
      />
      <HeaderTitle title={t('HelpDesk_')} navigation={navigation} />

      <SelectImageDialog
        show={showSelectImage}
        setShow={setShowSelectImage}
        setImage={setSelectedImage}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContainer}
      >
        {helpdesk && helpdeskMessages ? (
          <>
            <CustomText style={styles.titleText}>
              {helpdesk.title}
            </CustomText>

            {showReply ? (
              <View style={styles.replyContainer}>
                <TextInput
                  multiline={true}
                  numberOfLines={10}
                  placeholder={t('HelpDesk_messageBox')}
                  placeholderTextColor={theme.colors.textPlaceholder}
                  value={content!}
                  style={styles.textInput}
                  underlineColorAndroid='transparent'
                  onChangeText={(contents) => setContent(contents)}
                  maxLength={200}
                />

                <View style={styles.buttonContainer}>
                  <CustomButton
                    label={t('HelpDesk_sendBtn')}
                    style={{
                      width: 120,
                      alignItems: 'center',
                      justifyContent: 'center',
                      display: 'flex',
                      height: 50,
                    }}
                    textSize={14}
                    buttonTheme='secondary'
                    onPress={() => submit()}
                    loading={isLoading}
                  />

                  <TouchableOpacity
                    onPress={() => setShowSelectImage(true)}
                    style={styles.attachmentButton}
                  >
                    <AttachmentSVG width={20} height={20} />
                    <CustomText style={styles.attachmentText}>
                      Select Image
                    </CustomText>
                  </TouchableOpacity>
                </View>

                {selectedImage ? (
                  <View style={styles.imagePreview}>
                    <View style={styles.imagePreviewContent}>
                      <ImageIconSVG />
                      <CustomText style={styles.imagePreviewText}>
                        Image-{new Date().getTime()}.png
                      </CustomText>
                    </View>

                    <TouchableOpacity
                      style={styles.closeButton}
                      onPress={() => setSelectedImage(null)}
                    >
                      <CloseSVG width={13} height={13} />
                    </TouchableOpacity>
                  </View>
                ) : null}
              </View>
            ) : null}
            <View style={styles.messagesContainer}>
              {helpdeskMessages.map((message: any, index: number) => (
                <View key={index} style={styles.messageItem}>
                  <View style={styles.messageContent}>
                    <View style={styles.messageHeader}>
                      <CustomText style={styles.senderName}>
                        {message.senderId !== user._id
                          ? 'Pennytots Admin'
                          : `${user.first_name} ${user.last_name}`}
                      </CustomText>

                      <CustomText style={styles.messageTime}>
                        {timeAgo(message.createdAt)}
                      </CustomText>
                    </View>
                    <View>
                      <CustomText style={styles.messageText}>
                        {message.message}
                      </CustomText>

                      {message.image ? (
                        <TouchableOpacity
                          style={{
                            flex: 1,
                            width: '100%',
                          }}
                          onPress={() =>
                            navigation.push('Common', {
                              screen: 'fullscreen-image',
                              params: {
                                image: message.image,
                              },
                            })
                          }
                        >
                          <Image
                            source={{
                              uri: message.image,
                            }}
                            style={styles.messageImage}
                            resizeMode="cover"
                          />

                        </TouchableOpacity>
                      ) : null}

                      {/* Divider */}
                      {/* <View
                        style={{
                          height: 0.8,
                          width: '100%',
                          backgroundColor: 'gray',
                          marginTop: 10,
                          opacity: 0.5,
                        }}
                      ></View> */}
                    </View>

                    <View></View>
                  </View>
                </View>
              ))}
            </View>
          </>
        ) : null}
      </ScrollView>

      <View style={styles.floatingButton}>
        {!showReply && (<CustomButton
          label='Reply'
          textSize={rv(14)}

          style={{
            fontFamily: 'medium',
                        textAlign: 'center',
            alignItems: 'center',
            height: rv(55),
          }}
          onPress={() => setShowReply(!showReply)}
        // loading={isLoading}
        />)}
      </View>
    </SafeAreaView>
  );
};

export default HelpDeskMessagesScreen;
