// Create react native functional component
import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Linking,
  StyleSheet,
  FlatList,
  Image,
  ScrollView,
  Text,
  RefreshControl,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CustomText } from 'app/components/elements';
import SegmentedControlTab from 'react-native-segmented-control-tab';
import { TopicCard } from 'app/components/cards';
import Group from 'app/components/cards/group';
import { userAuthInfo } from 'app/redux/user/reducer';
import { useSelector } from 'react-redux';
import { Axios } from 'app/api/axios';
import AgeSVG from 'app/assets/svg/age.svg';
import UserSVG from 'app/assets/svg/user.svg';
import LocationSVG from 'app/assets/svg/location.svg';
import LinkSVG from 'app/assets/svg/link.svg';
import FBSVG from 'app/assets/svg/fb.svg';
import LinkedInSVG from 'app/assets/svg/linkedin.svg';
import CompanySVG from 'app/assets/svg/company.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { SafeAreaView } from 'react-native-safe-area-context';
import FollowButton from '../../components/elements/FollowButton';
import HeaderTitle from 'app/components/HeaderTitle';
import { useTranslation } from 'react-i18next';
import DefaultProfileSVG from 'app/assets/svg/profile-picture.svg';
import { useRoute } from '@react-navigation/native';
import { useFetchTopic } from 'app/redux/topic/hooks';
import { useFetchGroups } from 'app/redux/group/hooks';
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles } from 'app/utils/createThemedStyles';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
import { FONT_SIZES } from 'app/constants/theme';

const Loading = require('../../assets/loading.gif');

export type AccountProfileScreenProps = {
  navigation?: any;
  route: any;
  userDetails: any;
};

// Container Component
const ProfileContainer: React.FC<AccountProfileScreenProps> = ({
  navigation,
  route,
}) => {
  const profile = useSelector(userAuthInfo);
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  let { userDetails } = route.params;

  const [accountProfile, setAccountProfile] = useState<any | null>(null);
  console.log(accountProfile, 'profile');
  const [refreshing, setRefreshing] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);

  // For Topics & Groups:
  const userInfo = userDetails?.profile ? profile : userDetails;
  const { data: topic, isLoading: isLoadingTopics } = useFetchTopic(userInfo);
  const { data: group } = useFetchGroups(userInfo);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  function HandleTabChange(index: number) {
    setTabIndex(index);
  }

  // Load user details if not the authenticated user
  function LoadUserDetails() {
    if (userDetails && userDetails._id) {
      Axios({
        method: 'GET',
        url: '/user/' + userDetails._id,
      }).then((response: any) => {
        setAccountProfile(response.data);
      });
    }
  }

  // Initial load
  useEffect(() => {
    if (userDetails?.profile) {
      // If it's the logged-in user's own profile
      setAccountProfile(profile);
      userDetails = profile;
    } else {
      // Another user's profile
      LoadUserDetails();
    }
  }, [userDetails, profile]);

  // Decide which data to show in FlatList
  let data: any[] = [];
  if (tabIndex === 0) {
    data = topic || [];
  } else if (tabIndex === 1) {
    data = group || [];
  }

  // Helper: Open link with https:// if missing
  const openLink = (url?: string) => {
    if (!url) return;
    let link = url.toLowerCase().includes('http') ? url : `https://${url}`;
    Linking.openURL(link).catch((err) =>
      console.warn('Could not open URL:', err)
    );
  };

  return (
    <ProfilePresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      t={t}
      userDetails={userDetails}
      accountProfile={accountProfile}
      refreshing={refreshing}
      onRefresh={onRefresh}
      tabIndex={tabIndex}
      HandleTabChange={HandleTabChange}
      data={data}
      isLoadingTopics={isLoadingTopics}
      openLink={openLink}
    />
  );
};

// Presentation Component
interface ProfilePresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  t: any;
  userDetails: any;
  accountProfile: any;
  refreshing: boolean;
  onRefresh: () => void;
  tabIndex: number;
  HandleTabChange: (index: number) => void;
  data: any[];
  isLoadingTopics: boolean;
  openLink: (url?: string) => void;
}

const ProfilePresentation: React.FC<ProfilePresentationProps> = ({
  theme,
  styles,
  navigation,
  t,
  userDetails,
  accountProfile,
  refreshing,
  onRefresh,
  tabIndex,
  HandleTabChange,
  data,
  isLoadingTopics,
  openLink,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      <HeaderTitle
        title={t('SideNav_profile')}
        navigation={navigation}
        userDetails={userDetails}
      />

      {accountProfile ? (
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {/* Cover and Profile Picture */}
          <View style={{ alignItems: 'center' }}>
            <TouchableOpacity
              style={{ alignItems: 'flex-start', width: '100%' }}
              onPress={() => {
                if (accountProfile.header_image) {
                  navigation.push('Common', {
                    screen: 'fullscreen-image',
                    params: { image: accountProfile.header_image },
                  });
                }
              }}
            >
              <Image
                source={
                  accountProfile.header_image
                    ? { uri: accountProfile.header_image }
                    : require('app/assets/default_profile_img.png')
                }
                style={styles.coverImage}
                resizeMode='cover'
              />

              {/* Profile picture */}
              <TouchableOpacity
                onPress={() => {
                  if (accountProfile.profile_picture) {
                    navigation.push('Common', {
                      screen: 'fullscreen-image',
                      params: { image: accountProfile.profile_picture },
                    });
                  }
                }}
                style={{ marginTop: -hp('10.6%') }}
              >
                <Image
                  source={
                    accountProfile.profile_picture
                      ? { uri: accountProfile.profile_picture }
                      : require('app/assets/connectify-thumbnail.png')
                  }
                  style={styles.profilePicture}
                  resizeMode='cover'
                />
              </TouchableOpacity>
            </TouchableOpacity>
          </View>

          {/* User's Name + Edit or Follow button */}
          <View style={{ marginTop: 25 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              {/* Name & Gender (optional) */}
              <View style={{ width: '70%' }}>
                <CustomText style={styles.userName}>
                  {accountProfile.first_name} {accountProfile.last_name}
                </CustomText>
                {accountProfile.graduation_status ? (
                  <CustomText style={styles.userGender}>
                    {accountProfile.graduation_status}
                  </CustomText>
                ) : null}
                {accountProfile.graduation_year ? (
                  <CustomText style={styles.userGender}>
                    Graduation Year: {accountProfile.graduation_year}
                  </CustomText>
                ) : null}
                <CustomText style={styles.userGender}>
                  Sponsorship Plan:{' '}
                  {accountProfile.subscriptionType?.charAt(0).toUpperCase() +
                    accountProfile.subscriptionType?.slice(1) || 'Free'}
                </CustomText>
              </View>

              {/* Edit / Follow button */}
              {userDetails && userDetails.profile ? (
                <TouchableOpacity
                  onPress={() =>
                    navigation.push('Common', { screen: 'EditProfile' })
                  }
                  style={styles.editButton}
                >
                  <CustomText style={styles.editButtonText}>
                    {t('PersonalProfile_edit')}
                  </CustomText>
                </TouchableOpacity>
              ) : (
                <FollowButton userId={userDetails._id} />
              )}
            </View>
          </View>

          {/* ======= ARRANGED LIKE THE SCREENSHOT ======= */}
          {/* Job Title at Company */}
          {(accountProfile.company_position || accountProfile.company) && (
            <View style={styles.row2}>
              <CompanySVG width={20} />
              <CustomText style={styles.companyText}>
                {accountProfile.company_position
                  ? accountProfile.company_position
                  : ''}
                {accountProfile.company ? ` at ${accountProfile.company}` : ''}
              </CustomText>
            </View>
          )}

          {accountProfile.bio && (
            <View
              style={[styles.row, { flexWrap: 'wrap', alignItems: 'center' }]}
            >
              <UserSVG width={20} />
              <CustomText style={[styles.bioText, { flex: 1 }]}>
                {accountProfile.bio}
              </CustomText>
            </View>
          )}

          {/* City, State, Country all on one line */}
          {(accountProfile.city ||
            accountProfile.state ||
            accountProfile.country) && (
            <View style={styles.row}>
              <LocationSVG width={20} />
              <CustomText style={styles.locationText}>
                {`${accountProfile.city || ''}${
                  accountProfile.city && accountProfile.state ? ', ' : ''
                }${accountProfile.state || ''}${
                  (accountProfile.city || accountProfile.state) &&
                  accountProfile.country
                    ? ', '
                    : ''
                }${accountProfile.country || ''}`}
              </CustomText>
            </View>
          )}

          {/* Public Links */}
          {(accountProfile.website ||
            accountProfile.linkedin ||
            accountProfile.facebook) && (
            <>
              <View style={styles.row}>
                <LinkSVG width={20} />
                <CustomText style={styles.publicLinksTitle}>
                  Public links
                </CustomText>
              </View>

              {/* Website */}
              {accountProfile.website && (
                <TouchableOpacity
                  style={{ marginLeft: rv(30), marginTop: rv(5) }}
                  onPress={() => openLink(accountProfile.website)}
                >
                  <CustomText style={styles.link}>
                    {accountProfile.website}
                  </CustomText>
                </TouchableOpacity>
              )}

              {/* LinkedIn */}
              {accountProfile.linkedin && (
                <TouchableOpacity
                  style={{ marginLeft: rv(30), marginTop: rv(5) }}
                  onPress={() => openLink(accountProfile.linkedin)}
                >
                  <CustomText style={styles.link}>
                    {accountProfile.linkedin}
                  </CustomText>
                </TouchableOpacity>
              )}

              {/* Facebook */}
              {accountProfile.facebook && (
                <TouchableOpacity
                  style={{ marginLeft: rv(30), marginTop: rv(5) }}
                  onPress={() => openLink(accountProfile.facebook)}
                >
                  <CustomText style={styles.link}>
                    {accountProfile.facebook}
                  </CustomText>
                </TouchableOpacity>
              )}
            </>
          )}

          {/* Segmented Control for Topics or Groups */}
          <View style={{ alignItems: 'center', marginTop: 25 }}>
            <View style={{ width: wp('90%') }}>
              <SegmentedControlTab
                activeTabStyle={styles.activeTabStyle}
                activeTabTextStyle={styles.activeTabTextStyle}
                tabTextStyle={styles.tabTextStyle}
                tabStyle={styles.tabStyle}
                values={[t('profile_topics'), t('profile_groups')]}
                selectedIndex={tabIndex}
                onTabPress={HandleTabChange}
              />

              {isLoadingTopics ? (
                <View style={styles.loadingContainer}>
                  <Image
                    source={Loading}
                    style={{ width: rv(50), height: rv(50) }}
                  />
                </View>
              ) : (
                <FlatList
                  data={data}
                  keyExtractor={(item: any) => item._id}
                  style={{ marginTop: 6 }}
                  renderItem={({ item }) => {
                    return (
                      <View
                        style={{
                          flex: 1,
                          marginVertical: 3,
                          marginLeft: rv(10),
                        }}
                      >
                        {tabIndex === 0 ? (
                          <TopicCard item={item} navigation={navigation} />
                        ) : (
                          <Group
                            item={item}
                            navigation={navigation}
                            menu={false}
                          />
                        )}
                      </View>
                    );
                  }}
                />
              )}
            </View>
          </View>
        </ScrollView>
      ) : null}
    </SafeAreaView>
  );
};

// Themed styles with optimized font sizes
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: wp('5'),
  },
  coverImage: {
    width: '100%',
    height: hp('25%'),
    borderTopLeftRadius: wp('4%'),
    borderTopRightRadius: wp('4%'),
    borderBottomRightRadius: wp('4%'),
    borderBottomLeftRadius: wp('13%'),
  },
  profilePicture: {
    width: wp('18%'),
    height: wp('18%'),
    borderRadius: wp('18%'),
    borderColor: theme.colors.background,
    borderWidth: wp('0.7%'),
    alignSelf: 'center',
  },
  userName: {
    fontSize: fs('LG'), // rvf(14) -> fs('LG') (14)
    fontFamily: 'bold',
    marginBottom: 0,
    padding: 0,
    color: theme.colors.text,
    lineHeight:fs("XXXL")
  },
  userGender: {
    fontSize: fs('BASE'), // rvf(12) -> fs('BASE') (12)
    fontFamily: 'regular',
    marginTop: 0,
    padding: 0,
    lineHeight: fs('LG'),
    color: theme.colors.textSecondary,
  },
  editButton: {
    borderColor: theme.colors.buttonQuaternary,
    borderWidth: rv(1),
    borderRadius: rv(12),
    justifyContent: 'center',
    alignItems: 'center',
    height: rv(34),
    paddingHorizontal: rv(8),
  },
  editButtonText: {
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    fontFamily: 'semiBold',
    color: theme.colors.text,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: rv(5),
    gap: rv(5),
  },
  row2: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: rv(12),
    gap: rv(5),
  },
  companyText: {
    fontSize: fs('BASE'), // rvf(12) -> fs('BASE') (12)
    fontFamily: 'semiBold',
    flexWrap: 'wrap',
    color: theme.colors.text,
  },
  bioText: {
    fontSize: fs('BASE'), // rvf(12) -> fs('BASE') (12)
    fontFamily: 'regular',
    flexShrink: 1,
    flex: 1,
    flexWrap: 'wrap',
    color: theme.colors.text,
  },
  locationText: {
    fontSize: fs('BASE'), // rvf(12) -> fs('BASE') (12)
    fontFamily: 'semiBold',
    flexWrap: 'wrap',
    color: theme.colors.text,
  },
  publicLinksTitle: {
    fontSize: fs('BASE'), // rvf(12) -> fs('BASE') (12)
    fontFamily: 'semiBold',
    color: theme.colors.text,
  },
  link: {
    fontSize: fs('BASE'), // rvf(12) -> fs('BASE') (12)
    fontFamily: 'regular',
    color: theme.colors.info,
    textDecorationLine: 'underline',
  },

  // Segmented Control
  activeTabStyle: {
    backgroundColor: theme.colors.background,
    borderBottomWidth: 2,
    borderBottomColor: theme.colors.primary,
  },
  activeTabTextStyle: {
    color: theme.colors.text,
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    fontFamily: 'bold',
    paddingVertical: 8,
  },
  tabTextStyle: {
    color: theme.colors.text,
    fontSize: fs('MD'), // rvf(13) -> fs('MD') (13)
    paddingVertical: 8,
    fontFamily: 'regular',
  },
  tabStyle: {
    backgroundColor: theme.colors.background,
    borderColor: theme.colors.background,
    borderBottomWidth: 0.5,
    borderBottomColor: theme.colors.primary,
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
  },
}));

export default ProfileContainer;
