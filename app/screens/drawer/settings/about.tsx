import { CustomText } from 'app/components/elements';
import React from 'react';
import { ScrollView, StatusBar, View, SafeAreaView } from 'react-native';
import HeaderTitle from 'app/components/HeaderTitle';
import WebView from 'react-native-webview';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'app/hooks/useTheme';
import { createThemedStyles } from 'app/utils/createThemedStyles';

type AboutScreenProps = {
  navigation: any;
};

const About: React.FC<AboutScreenProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const termsHTML = `
  <html>
  <head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-size: 14px;
      line-height: 1.5em;
      max-width: 100%;
      background-color: ${theme.colors.background};
      color: ${theme.colors.text};
      margin: 0;
      padding: 16px;
    }
    p {
      font-size: 14px;
      color: ${theme.colors.text};
      margin-bottom: 12px;
    }
    a {
      color: ${theme.colors.primary};
      text-decoration: underline;
    }
  </style>
  </head>
  <body>
  <p>
  The Connectify Network is a free to use mobile directory and messaging app developed by Pennytots Limited (UK), Laten Geuberen Limited (Canada) and iKnowAfrica Limited (Nigeria), on behalf of the Connectify. It is intended to strengthen the Connectify network through increased communication, member search, knowledge sharing, and collaborations.
  </p>
  <p><a data-fr-linked="true" href="https://pennytots.com">www.pennytots.com</a></p>
  <p><br></p>
  </body>
  </html>
  `;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={{ flex: 1 }}>
        <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
        <HeaderTitle title='About Connectify' navigation={navigation} />
        <View style={styles.content}>
          <WebView source={{ html: termsHTML }} style={{ flex: 1 }} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
}));

export default About;
