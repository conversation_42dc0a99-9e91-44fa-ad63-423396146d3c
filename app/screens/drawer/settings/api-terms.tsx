import { CustomText } from 'app/components/elements';
import React from 'react';
import { ScrollView, StatusBar, View, SafeAreaView } from 'react-native';
import HeaderTitle from 'app/components/HeaderTitle';
import WebView from 'react-native-webview';
import { useTranslation } from 'react-i18next';
import { useTheme, createThemedStyles } from 'app/theme';

type APITermsScreenProps = {
  navigation: any;
};

const APITerms: React.FC<APITermsScreenProps> = ({ navigation }:any) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const apiTermsHTML = `
  <html>
  <head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-size: 14px;
      line-height: 1.5em;
      max-width: 100%;
      background-color: ${theme.colors.background};
      color: ${theme.colors.text};
      margin: 0;
      padding: 16px;
    }
    p {
      font-size: 14px;
      color: ${theme.colors.text};
      margin-bottom: 12px;
    }
    a {
      color: ${theme.colors.primary};
      text-decoration: underline;
    }
  </style>
  </head>
  <body>
  <p>
  This Agreement was last modified on 25th January 2025.
  </p>
  <p>
  By accepting these API T&Cs or by accessing or using the API, you agree to be bound by these API T&Cs, and the <a href="https://www.Pennytots.com/terms">User Agreement</a>, <a href="https://www.Pennytots.com/privacy">Privacy Policy</a>, <a href="https://www.Pennytots.com/copyright">Copyright Policy</a> and <a href="https://www.Pennytots.com/code-of-conduct">Code of Conduct</a>.
  </p>
  <p>
  You may not use the API in violation of any law or regulation, or rights of any person, including but not limited to intellectual property rights, rights of privacy, or rights of personality, or in any manner inconsistent with these API T&Cs.
  </p>
  <p>
  You agree to indemnify and hold Pennytots.com, Pennytots.com affiliates, and their respective officers, directors, shareholders, employees, and assigns harmless from any claim, demand, expense, or damage arising from your use of the Services.
  </p>
  <p>
  This Agreement shall be governed by the laws of the Federal Republic of Nigeria.
  </p>
  </body>
  </html>
  `;

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
        <HeaderTitle title='API Terms And Condition' navigation={navigation} />
        <View style={styles.container}>
          <WebView source={{ html: apiTermsHTML }} style={styles.webView} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = createThemedStyles((theme) => ({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: theme.colors.background,
  },
  webView: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
}));

export default APITerms;