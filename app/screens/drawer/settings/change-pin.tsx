import React, { useState, useContext } from 'react';
import { View, TextInput, ScrollView, SafeAreaView } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Loader from 'app/components/elements/Loader';
import { CustomButton, CustomText } from 'app/components/elements';
import HeaderTitle from 'app/components/HeaderTitle';
import { useCommonStyles } from 'app/assets/styles/CommonStyles';
// Removed direct Axios import
import { ShowAlert } from 'app/providers/toast';
import { showModal, hideModal } from 'app/providers/modals';
import { useTranslation } from 'react-i18next';
import { responsiveValue } from 'app/providers/responsive-value';
import { useChangePin } from 'app/redux/user/hooks'; // <-- Using our new hook
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

type ChangePINScreenProps = {
  navigation: any;
};

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    backgroundColor: theme.colors.background,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerContainer: {
    width: '85%',
    height: hp('80'),
    marginTop: 20,
  },
  inputContainer: {
    flexDirection: 'column',
  },
  inputContainerSpaced: {
    flexDirection: 'column',
    marginTop: 30,
  },
  labelText: {
    color: theme.colors.textSecondary,
    fontSize: fs("BASE"),
    fontFamily: 'medium'
  },
  buttonContainer: {
    marginTop: 'auto',
  },
}));

const ChangePin: React.FC<ChangePINScreenProps> = ({ navigation }) => {
  const [currentPIN, setCurrentPIN] = useState('');
  const [newPIN, setNewPIN] = useState('');
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const commonStyles = useCommonStyles();

  // useChangePin hook
  const { mutate: changePINMutation, isLoading, error } = useChangePin();

  async function handleChangePIN() {
    if (!currentPIN.trim()) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Current PIN is required',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
      });
      return;
    }

    if (!newPIN.trim()) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'New PIN is required',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
      });
      return;
    }

    changePINMutation(
      { currentPIN, newPIN },
      {
        onSuccess: (data: any) => {
          showModal({
            modalVisible: true,
            title: 'Alert',
            message: data?.message || 'Your password has been set',
            setModalVisible: hideModal,
            type: 'success-alert',
            handleConfirm: () => {
              console.log('Confirmed!');
              hideModal();
            },
            handleAlert: () => {
              console.log('Alert handled!');
              hideModal();
            },
          });
        },
        onError: (err: any) => {
          // If there's an error, we can show a modal or alert the user
          const errorMessage = err?.data?.message || 'Check phone number and pin';
          showModal({
            modalVisible: true,
            title: 'Oops',
            message: errorMessage,
            setModalVisible: hideModal,
            type: 'alert',
            handleConfirm: () => {
              console.log('Confirmed!');
              hideModal();
            },
            handleAlert: () => {
              console.log('Alert handled!');
              hideModal();
            },
          });
        },
      }
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <HeaderTitle title={t('Settings_pin')} navigation={navigation} />
        <Loader loading={isLoading} />
        <View style={styles.contentContainer}>
          <View style={styles.innerContainer}>
            <View style={styles.inputContainer}>
              <CustomText style={styles.labelText}>
                {t('ChangePin_currentPin')}
              </CustomText>
              <TextInput
                style={commonStyles.inputField}
                onChangeText={(text) => setCurrentPIN(text)}
                inlineImageLeft='key'
                placeholder={t('ChangePin_currrentPinPlaceholder')}
                placeholderTextColor={theme.colors.textPlaceholder}
                inlineImagePadding={20}
                textContentType='password'
                secureTextEntry={true}
                keyboardType='numeric'
                maxLength={4}
                value={currentPIN}
              />
            </View>
            <View style={styles.inputContainerSpaced}>
              <CustomText style={styles.labelText}>
                {t('ChangePin_newPin')}
              </CustomText>
              <TextInput
                style={commonStyles.inputField}
                onChangeText={(text) => setNewPIN(text)}
                inlineImageLeft='key'
                placeholder={t('ChangePin_newPinPlaceholder')}
                placeholderTextColor={theme.colors.textPlaceholder}
                inlineImagePadding={20}
                textContentType='password'
                secureTextEntry={true}
                keyboardType='numeric'
                maxLength={4}
                value={newPIN}
              />
            </View>
            <View style={styles.buttonContainer}>
              <CustomButton
                label={t('ChangePin_title')}
                onPress={handleChangePIN}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ChangePin;
