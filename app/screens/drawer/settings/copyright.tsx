import React from 'react';
import { ScrollView, StatusBar, View, SafeAreaView } from 'react-native';
import HeaderTitle from 'app/components/HeaderTitle';
import WebView from 'react-native-webview';
import { useTheme, createThemedStyles } from 'app/theme';

const CopyrightInfringementPolicy = ({ navigation }:any) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const policyHTML = `
  <html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
      body {
        font-size: 14px;
        line-height: 1.5em;
        max-width: 100%;
        background-color: ${theme.colors.background};
        color: ${theme.colors.text};
        margin: 0;
        padding: 16px;
      }
      p, li {
        font-size: 14px;
        color: ${theme.colors.text};
        margin-bottom: 12px;
      }
      a {
        color: ${theme.colors.primary};
        text-decoration: underline;
      }
      ul {
        padding-left: 20px;
      }
    </style>
  </head>
  <body>
    <p><PERSON><PERSON><PERSON> respects the intellectual property rights of others. If you believe that your work has been copied in a way that constitutes copyright infringement, please provide Pennytots's Copyright Agent with the information specified below in the form of a "Notification of Alleged Infringement."</p>
    <p>DMCA NOTIFICATION OF ALLEGED COPYRIGHT INFRINGEMENT</p>
    <p>Submit claims by sending a Notification of Claimed Infringement to:</p>
    <p>Copyright Agent c/o Pennytots Limited, 35 Redbrook Way, Bradford, England, BD9 6SF <a href="mailto:<EMAIL>"><EMAIL></a></p>
    <p>Required information includes:</p>
    <ul>
      <li>Signature of the owner or authorized agent</li>
      <li>Identification of the copyrighted material</li>
      <li>Details of the infringing material</li>
      <li>Contact information</li>
      <li>Good faith belief statement</li>
      <li>Accuracy and authorization statement</li>
    </ul>
    <p>DMCA COUNTER-NOTIFICATION</p>
    <p>Submit a Counter-Notification to:</p>
    <p>Copyright Agent c/o 35 Redbrook Way, Bradford, England, BD9 6SF <a href="mailto:<EMAIL>"><EMAIL></a></p>
    <p>Required information includes:</p>
    <ul>
      <li>Signature of the User or authorized agent</li>
      <li>Details of removed material</li>
      <li>Good faith belief statement</li>
      <li>Jurisdiction consent and service of process acceptance</li>
    </ul>
    <p>Misrepresentation may lead to liability under Section 512(f) of the U.S. Copyright Act. We enforce a policy for terminating repeat infringers.</p>
  </body>
  </html>
  `;

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
        <HeaderTitle title='Copyright Infringement Policy' navigation={navigation} />
        <View style={styles.container}>
          <WebView source={{ html: policyHTML }} style={styles.webView} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = createThemedStyles((theme) => ({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: theme.colors.background,
  },
  webView: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
}));

export default CopyrightInfringementPolicy;
