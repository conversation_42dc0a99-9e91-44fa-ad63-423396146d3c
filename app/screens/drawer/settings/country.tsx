import React, { useEffect, useState } from 'react';
import { Text, View, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NgSVG, VectorSVG, AmSVG, GhSVG, CaSVG, UsSVG, FrSVG, ZaSVG, CvSVG } from 'app/providers/svg/loader';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import HeaderTitle from 'app/components/HeaderTitle';
import { setCountry, country as selectCountry } from 'app/redux/main/reducer'; // Adjust the path as needed
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

const countries = [
  { name: 'Global', code: 'GLO', icon: VectorSVG, selectable: true },
  { name: 'Nigeria', code: 'NGN', icon: NgSVG, selectable: true },
  { name: 'United Kingdom', code: 'GBR', icon: AmSVG, selectable: false },
  { name: 'Ghana', code: 'GHA', icon: GhSVG, selectable: false },
  { name: 'Canada', code: 'CAN', icon: CaSVG, selectable: false },
  { name: 'USA', code: 'USA', icon: UsSVG, selectable: false },
  { name: 'South Africa', code: 'ZAF', icon: ZaSVG, selectable: false },
  { name: 'Cape Verde', code: 'CPV', icon: CvSVG, selectable: false },
  { name: 'France', code: 'FRA', icon: FrSVG, selectable: false }
];

const Country = ({ navigation }:{navigation:any}) => {
  const dispatch = useDispatch();
  const selectedCountryFromState = useSelector(selectCountry);
  const [selectedCountry, setSelectedCountry] = useState(selectedCountryFromState);
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  useEffect(() => {
    const getCountry = async () => {
      try {
        const storedCountry = await AsyncStorage.getItem('selectedCountry');
        if (storedCountry !== null) {
          setSelectedCountry(storedCountry);
          dispatch(setCountry(storedCountry));
        }
        else {
          // Set default to 'GLO' if no country is stored
          setSelectedCountry('GLO');
          dispatch(setCountry('GLO'));
          await AsyncStorage.setItem('selectedCountry', 'GLO');
        }
      } catch (error) {
        console.error('Failed to load country:', error);
      }
    };

    getCountry();
  }, []);

  useEffect(() => {
    const storeCountry = async () => {
      try {
        await AsyncStorage.setItem('selectedCountry', selectedCountry);
      } catch (error) {
        console.error('Failed to save country:', error);
      }
    };

    storeCountry();
  }, [selectedCountry]);

  const handleCountrySelect = (countryCode :any) => {
    setSelectedCountry(countryCode);
    dispatch(setCountry(countryCode));
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderTitle title="Country" navigation={navigation} />
      <ScrollView>
        <Text style={styles.instructions}>
          Questions are based on the country you select
        </Text>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Available countries</Text>
          {countries.filter(country => country.selectable).map((country, index) => (
            <TouchableOpacity 
              key={index} 
              style={[
                styles.countryRow, 
                selectedCountry === country.code && styles.selectedCountryRow
              ]}
              onPress={() => handleCountrySelect(country.code)}
            >
              <country.icon width={30} height={35} />
              <Text style={styles.countryName}>{country.name}</Text>
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Coming soon</Text>
          {countries.filter(country => !country.selectable).map((country, index) => (
            <View key={index} style={styles.countryRow}>
              <country.icon width={30} height={35} />
              <Text style={styles.countryName}>{country.name}</Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  instructions: {
    marginLeft: rv(11),
    marginTop: rv(10),
    color: theme.colors.textSecondary,
    fontFamily: 'regular',
    fontSize: fs("MD"),
  },
  section: {
    marginTop: rv(16),
  },
  sectionTitle: {
    padding: 15,
    backgroundColor: theme.colors.surfaceVariant,
    fontFamily: 'regular',
    fontSize: fs("LG"),
    color: theme.colors.textSecondary,
  },
  countryRow: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: rv(14),
    padding: 15,
    backgroundColor: theme.colors.background,
  },
  countryName: {
    color: theme.colors.text,
    fontFamily: 'medium',
    fontSize: fs("LG"),
    marginLeft: rv(12),
  },
  selectedCountryRow: {
    backgroundColor: theme.colors.secondary,
  },
}));

export default Country;
