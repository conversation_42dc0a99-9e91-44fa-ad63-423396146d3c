import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { View, ScrollView, SafeAreaView, TouchableOpacity } from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { useFontSize } from '../../../redux/fontSize/useFontSize';
import { setFontSizeScale as setFontSizeScaleAction } from '../../../redux/fontSize/fontSizeSlice';
import { useOptimizedFontSize } from '../../../hooks/useOptimizedFontSize';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { useTranslation } from 'react-i18next';
import HeaderTitle from 'app/components/HeaderTitle';
import { CustomText } from 'app/components/elements';
import { FontSizeScale, FONT_SIZE_LABELS } from 'app/providers/FontSizeProvider';
import { FontSizeScale as OptimizedFontSizeScale } from 'app/utils/optimizedFontSizes';
import { RootState } from 'app/store';


type FontSizeSettingsScreenProps = {
  navigation: any;
};

// Map slider values to font size scales
const SLIDER_TO_SCALE: Record<number, FontSizeScale> = {
  0: 'small',
  1: 'normal',
  2: 'large',
  3: 'extra-large'
};

const SCALE_TO_SLIDER: Record<FontSizeScale, number> = {
  'small': 0,
  'normal': 1,
  'large': 2,
  'extra-large': 3
};

// Container Component
const FontSizeSettingsContainer: React.FC<FontSizeSettingsScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { fontSizeScale, setFontSizeScale } = useFontSize();
  const { fontSizeScale: optimizedFontSizeScale, setFontSizeScale: setOptimizedFontSizeScale } = useOptimizedFontSize();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const fontSizeScale = useSelector((state: RootState) => state.fontSize.fontSizeScale);

  const handleSliderChange = (value: number) => {
    const roundedValue = Math.round(value);
    const scale = SLIDER_TO_SCALE[roundedValue];
    if (scale) {
      setFontSizeScale(scale);
      setOptimizedFontSizeScale(scale as OptimizedFontSizeScale);
      dispatch(setFontSizeScaleAction(scale));
      dispatch(setFontSizeScale(scale));
    }
  };

  return (
    <FontSizeSettingsPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      t={t}
      fontSizeScale={fontSizeScale}
      sliderValue={SCALE_TO_SLIDER[fontSizeScale]}
      onSliderChange={handleSliderChange}
    />
  );
};

// Presentation Component
interface FontSizeSettingsPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  t: any;
  fontSizeScale: FontSizeScale;
  sliderValue: number;
  onSliderChange: (value: number) => void;
}

const FontSizeSettingsPresentation: React.FC<FontSizeSettingsPresentationProps> = ({
  theme,
  styles,
  navigation,
  t,
  fontSizeScale,
  sliderValue,
  onSliderChange,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      <HeaderTitle title="Font Size" navigation={navigation} />
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Choose Font Size
            </CustomText>
            <CustomText style={styles.sectionDescription}>
              Adjust the slider to change the font size throughout the app.
            </CustomText>

            <View style={styles.sliderContainer}>
              <View style={styles.sliderLabels}>
                <CustomText style={styles.sliderLabel}>Small</CustomText>
                <CustomText style={styles.sliderLabel}>Normal</CustomText>
                <CustomText style={styles.sliderLabel}>Large</CustomText>
                <CustomText style={styles.sliderLabel}>Extra Large</CustomText>
              </View>

              {/* Custom Slider Track */}
              <View style={styles.sliderTrack}>
                <View style={[styles.sliderProgress, { width: `${(sliderValue / 3) * 100}%` }]} />

                {/* Slider Steps */}
                {[0, 1, 2, 3].map((step) => (
                  <TouchableOpacity
                    key={step}
                    style={[
                      styles.sliderStep,
                      { left: `${(step / 3) * 100}%` },
                      sliderValue === step && styles.sliderStepActive
                    ]}
                    onPress={() => onSliderChange(step)}
                  />
                ))}
              </View>

              <View style={styles.currentValueContainer}>
                <CustomText style={styles.currentValueText}>
                  Current: {FONT_SIZE_LABELS[fontSizeScale]}
                </CustomText>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Preview
            </CustomText>
            <View style={styles.previewContainer}>
              <CustomText style={styles.previewTitle}>
                Sample Title Text
              </CustomText>
              <CustomText style={styles.previewBody}>
                This is how regular text will appear with your selected font size. 
                You can see how it affects readability and overall appearance of the app.
              </CustomText>
              <CustomText textType="bold" style={styles.previewBold}>
                This is bold text sample
              </CustomText>
              <CustomText textType="medium" style={styles.previewMedium}>
                This is medium weight text sample
              </CustomText>
            </View>
          </View>

          <View style={styles.section}>
            <CustomText style={styles.sectionTitle}>
              Current Setting
            </CustomText>
            <CustomText style={styles.currentSettingText}>
              {FONT_SIZE_LABELS[fontSizeScale]}
            </CustomText>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: rv(20),
  },
  section: {
    marginBottom: rv(30),
  },
  sectionTitle: {
    fontSize: fs("XXL"),
    fontFamily: 'bold',
    color: theme.colors.text,
    marginBottom: rv(8),
  },
  sectionDescription: {
    fontSize: fs("LG"),
    fontFamily: 'regular',
    color: theme.colors.textSecondary,
    marginBottom: rv(20),
    lineHeight: rv(20),
  },
  sliderContainer: {
    paddingHorizontal: rv(10),
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: rv(15),
    paddingHorizontal: rv(10),
  },
  sliderLabel: {
    fontSize: fs("BASE"),
    fontFamily: 'regular',
    color: theme.colors.textSecondary,
    textAlign: 'center',
    flex: 1,
  },
  sliderTrack: {
    height: rv(6),
    backgroundColor: theme.colors.border,
    borderRadius: rv(3),
    marginHorizontal: rv(10),
    marginBottom: rv(20),
    position: 'relative',
  },
  sliderProgress: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: rv(3),
  },
  sliderStep: {
    position: 'absolute',
    top: rv(-7),
    width: rv(20),
    height: rv(20),
    borderRadius: rv(10),
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.border,
    marginLeft: rv(-10),
  },
  sliderStepActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  currentValueContainer: {
    alignItems: 'center',
    marginTop: rv(10),
  },
  currentValueText: {
    fontSize: fs("XL"),
    fontFamily: 'medium',
    color: theme.colors.primary,
  },
  previewContainer: {
    padding: rv(20),
    borderRadius: rv(12),
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  previewTitle: {
    fontFamily: 'bold',
    color: theme.colors.text,
    marginBottom: rv(12),
  },
  previewBody: {
    fontFamily: 'regular',
    color: theme.colors.text,
    lineHeight: rv(20),
    marginBottom: rv(12),
  },
  previewBold: {
    marginBottom: rv(8),
  },
  previewMedium: {
  },
  currentSettingText: {
    fontSize: fs("XL"),
    fontFamily: 'medium',
    color: theme.colors.primary,
  },
}));

export default FontSizeSettingsContainer;
