import {
  CountrySVG,
  DarkAboutSVG,
  DarkChangePinSVG,
  LanguageSVG,

  DarkLikeSVG,
  DarkUserTermSVG,
  EmptySearchSVG,
  HelpdeskSVG,
  BuyCreditSVG,
  DeleteSVG,
  UserTermsSVG,
  AboutPennySVG,
  InterestIconSVG,
  LogoutInredSVG,
  SettingSVG,
  SupportSVG
} from 'app/providers/svg/loader';
import React, {
  Component,
  useContext,
  useState,
  useEffect,
  useRef,
  FunctionComponent,
} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  ListRenderItem,
  TouchableOpacity,
  ScrollView,
  TextInput,
  SafeAreaView
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CustomText } from 'app/components/elements';
import HeaderTitle from 'app/components/HeaderTitle';
import Avatar from 'app/components/elements/Avater';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useSelector } from 'react-redux';
import { userAuthInfo } from 'app/redux/user/reducer';
import InterestSVG from 'app/assets/svg/lucosa-interest.svg';
import HelpDesk from '../helpdesk';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { logout } from 'app/redux/user/hooks';

import { useDeleteAccount } from 'app/redux/user/hooks';

import { useTranslation } from 'react-i18next';
import CustomModal from 'app/components/elements/Modals';
import { BulbSVG } from 'app/providers/svg/loader';
import { GroupSVG } from 'app/providers/svg/loader';
import { CreateGroupSVG } from 'app/providers/svg/loader';
import { NewGroupSVG } from 'app/providers/svg/loader';
import Profile from '../profile';
import ProfileDefaultSVG from 'app/components/elements/Avater';
import LogoutSVG from '@/app/assets/svg/LogoutinRed.svg'
import DarkBlockedChatSVG from '@/app/assets/svg/lucosa-blockedchat.svg'
import UserTermSVG from '@/app/assets/svg/user-terms-lucosa.svg'
import { useTheme } from 'app/hooks/useTheme';
import { createOptimizedThemedStyles, createThemedStyles } from 'app/utils/createThemedStyles';
import { ThemeSelector } from 'app/components/ThemeSelector';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';

console.log(Profile)

type SettingsScreenProps = {
  navigation: any;
};

// Container Component
const SettingsContainer: FunctionComponent<SettingsScreenProps> = ({ navigation }) => {
  const profile = useSelector(userAuthInfo);
  const { t } = useTranslation();
  const deleteAccount = useDeleteAccount();
  const [modalVisible, setModalVisible] = useState(false);
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);

  const handleDelete = () => {
    setModalVisible(true)
  };

  interface ISettingsData {
    [key: string]: {
      name: string;
      items: Array<{
        id: number;
        name: string;
        icon: () => JSX.Element;
        route?: string;
        action?: () => void;
      }>;
    },


  }

  let SettingsData: ISettingsData = {
    appearance: {
      name: 'Appearance',
      items: [
        {
          id: 1,
          name: 'Theme',
          icon: () => {
            return <BulbSVG width={29} />;
          },
          route: 'theme-settings',
        },
        {
          id: 2,
          name: 'Font Size',
          icon: () => {
            return <SettingSVG width={29} />;
          },
          route: 'font-size-settings',
        },
      ],
    },
    preference: {
      name: t('Settings_preferences'),
      items: [
        // {
        //   id: 1,
        //   name: 'Trivia Categories',
        //   icon: () => {
        //     return <CountrySVG width={29} />;
        //   },
        //   route: 'country',
        // },
        {
          id: 1,
          name: t('Settings_interest'),
          icon: () => {
            return <InterestSVG width={29} />;
          },
          route: 'area-of-interest',
        },

        // {
        //   id: 3,
        //   name: t('Settings_language'),
        //   icon: () => {
        //     return <LanguageSVG />;
        //   },
        //   route: 'change-language',
        // },
      ],
    },
    account: {
      name: t("Settings_account"),
      items: [
        {
          id: 1,
          name: t('Settings_pin'),
          icon: () => {
            return <DarkChangePinSVG />;
          },
          route: 'change-pin',
        },
        {
          id: 2,
          name: t('Settings_blockedChats'),
          icon: () => {
            return <DarkBlockedChatSVG />;
          },
          route: 'blocked-chats',
        },

        {
          id: 3,
          name: 'Logout',
          icon: () => {
            return <LogoutSVG width={22}/>
          },
          action: logout,
        },
        {
          id: 4,
          name: 'Delete Account',
          icon: () => {
            return <LogoutInredSVG  />;
          },
          action: handleDelete,
        },
      ],
    },
    help: {
      name: t('Help'),
      items: [
        {
          id: 1,
          name: t('Settings_userTerms'),
          icon: () => {
            return <UserTermSVG />
          },
          route: 'user-terms'
        },
        {
          id: 2,
          name: "About Connectify",
          icon: () => {
            return <AboutPennySVG />
          },
          route: 'about'
        },
        {
          id: 3,
          name: "Privacy Policy",
          icon: () => {
            return <UserTermSVG />
          },
          route: 'privacy-policy'
        },
        {
          id: 4,
          name: "End-User License Agreement",
          icon: () => {
            return <UserTermSVG />
          },
          route: 'eula'
        }
      ]
    },
  };

  const handleConfirm = () => {
    deleteAccount.mutate();
    setModalVisible(false);
  };

  return (
    <SettingsPresentation
      theme={theme}
      styles={styles}
      profile={profile}
      navigation={navigation}
      t={t}
      modalVisible={modalVisible}
      setModalVisible={setModalVisible}
      handleConfirm={handleConfirm}
      SettingsData={SettingsData}
    />
  );
};

// Presentation Component
interface SettingsPresentationProps {
  theme: any;
  styles: any;
  profile: any;
  navigation: any;
  t: any;
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  handleConfirm: () => void;
  SettingsData: ISettingsData;
}

const SettingsPresentation: React.FC<SettingsPresentationProps> = ({
  theme,
  styles,
  profile,
  navigation,
  t,
  modalVisible,
  setModalVisible,
  handleConfirm,
  SettingsData,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      {modalVisible && (
        <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type='delete' // or "stake" based on the scenario
          message='Are you sure you want to delete your account. Going on with this action will lead to the permanent deletion of your account'
          title='Waoh !'
          handleConfirm={handleConfirm}
          navigation
        />
      )}
      <HeaderTitle title={t("Settings")} navigation={navigation} />
      <ScrollView>
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            paddingBottom: 24,
          }}
        >
          {profile ? (
            <View
              style={{
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                paddingBottom: 10,
              }}
            >
              <ProfileDefaultSVG source={profile.profile_picture} size={hp('13%')} />
              <CustomText style={styles.profileName}>
               {`${profile.first_name} ${profile.last_name}`.length > 9 
    ? `${`${profile.first_name} ${profile.last_name}`.slice(0, 9)}...` 
    : `${profile.first_name} ${profile.last_name}`}
              </CustomText>
            </View>
          ) : null}
          <TouchableOpacity
            onPress={() =>
              navigation.push('Common', {
                screen: 'EditProfile',
              })
            } style={styles.editButton}
          >
            <CustomText style={styles.editButtonText}>
              {t("PersonalProfile_edit")}
            </CustomText>

            <FontAwesome
              name='chevron-right'
              size={11}
              color={theme.colors.primary}
              style={{
                marginRight: 5,
              }}
            />
          </TouchableOpacity>

          {/* <CustomText
          style={{
            fontSize: rv(24),
            fontFamily: 'semiBold'
          }}>
          {t("settings")}
          </CustomText> */}
        </View>

        {Object.keys(SettingsData).map((key, index) => (
          <View
            style={{
              flexDirection: 'column',
            }}
          >
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionHeaderText}>
                {SettingsData[key].name}
              </Text>
            </View>
            <View style={styles.sectionContent}>
              {SettingsData[key].items.map((item) => (
                <TouchableOpacity
                  onPress={() => {
                    if ((item.name === 'Delete Account' || item.name === 'Logout') && item.action) {
                      item.action(); // Call the logout function
                    } else {
                      navigation.navigate(item.route);
                    }
                  }}
                  style={styles.settingsItem}
                >
                  <item.icon />
                  <CustomText
                    style={[
                      styles.settingsItemText,
                      (item.name === 'Delete Account' || item.name === 'Logout') && styles.dangerText
                    ]}
                  >
                    {item.name}
                  </CustomText>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

// Themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  profileName: {
    color: theme.colors.text,
    fontSize: fs("BASE"),
    marginTop: 10,
    fontFamily: 'bold',
  },
  editButton: {
    borderColor: theme.colors.primary,
    borderWidth: 1,
    borderRadius: 20,
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 5,
    paddingHorizontal: 15,
    flexDirection: 'row',
  },
  editButtonText: {
    marginRight: 5,
    fontSize: fs("BASE"),
    fontFamily: 'medium',
    color: theme.colors.text,
  },
  sectionHeader: {
    backgroundColor: theme.colors.surfaceVariant,
    padding: 15,
  },
  sectionHeaderText: {
    color: theme.colors.textSecondary,
    fontFamily: 'regular',
    fontSize: fs("LG"),
  },
  sectionContent: {
    flexDirection: 'column',
    padding: 15,
    backgroundColor: theme.colors.background,
  },
  settingsItem: {
    marginTop: rv(10),
    flexDirection: 'row',
    alignItems: 'center',
    padding: 11,
  },
  settingsItemText: {
    fontSize: fs("SM"),
    paddingLeft: rv(16),
    color: theme.colors.text,
    fontFamily: 'medium',
  },
  dangerText: {
    color: theme.colors.error,
  },
}));

export default SettingsContainer;