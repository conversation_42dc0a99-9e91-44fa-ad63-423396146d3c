import React from 'react';
import { ScrollView, StatusBar, View, SafeAreaView } from 'react-native';
import HeaderTitle from 'app/components/HeaderTitle';
import WebView from 'react-native-webview';

const KYCPolicy = ({ navigation }:any) => {
  const policyHTML = `
    <html>
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
    </head>
    <body style="font-size: 16px; line-height: 1.5em; max-width: 100%">
      <p>Pennytots's KYC program ensures a trusted marketplace for all users by confirming user identity through documentation collection. This helps to prevent fraud, money laundering, and the financing of terrorism.</p>
      <p>We believe that KYC is a requirement that ensures a safer and secure online advert space for all Pennytots members. In addition, several of our service providers, such as our payment gateway providers, require us to perform certain checks on customer identity.</p>
      <p><strong>How do I complete KYC to verify my identity?</strong></p>
      <p>You can become Pennytots verified by completing your profile on the website.</p>
      <p><strong>What happens if I'm not able to pass KYC?</strong></p>
      <p>If you are unable to become Pennytots Verified, Pennytots may place an account limitation or suspend your account as a security measure.</p>
      <p>Falsifying your identity is a crime. Pennytots may report users that provide false documentation.</p>
      <p>If you have any questions or concerns with these requirements you can contact our customer support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
    </body>
    </html>
  `;

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
      <ScrollView style={{ backgroundColor: '#ffffff' }} contentContainerStyle={{ flex: 1 }}>
        <StatusBar barStyle='dark-content' backgroundColor='white' />
        <HeaderTitle title='KYC Policy' navigation={navigation} />
        <View style={{ flex: 1, paddingHorizontal: 20 }}>
          <WebView source={{ html: policyHTML }} style={{ flex: 1 }} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default KYCPolicy;
