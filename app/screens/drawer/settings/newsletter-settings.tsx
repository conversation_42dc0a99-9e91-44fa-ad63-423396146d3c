import React, { useCallback } from 'react';
import {
  View,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from 'app/hooks/useTheme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
import { CustomText } from 'app/components/elements';
import HeaderTitle from 'app/components/HeaderTitle';
import { createOptimizedThemedStyles } from 'app/utils/createThemedStyles';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import {
  useGetNewsletterSettings,
  useUpdateNewsletterSettings
} from 'app/redux/newsletter/hooks';
import { NewsletterSettings } from 'app/api/newsletter';

interface NewsletterSettingsScreenProps {
  navigation: any;
}

// Container Component
const NewsletterSettingsContainer: React.FC<NewsletterSettingsScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();

  // Safety checks
  if (!theme || !fs) {
    return (
      <SafeAreaView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <CustomText style={{ marginTop: 20 }}>Loading...</CustomText>
      </SafeAreaView>
    );
  }

  const styles = createStyles(theme, fs);

  // Use React Query hooks
  const { data: settingsData, isLoading } = useGetNewsletterSettings();
  const updateSettingsMutation = useUpdateNewsletterSettings();

  // Get current settings with default values
  const settings: NewsletterSettings = settingsData?.data || {
    enabled: true,
    frequency: 'bi-weekly',
    contentPreferences: {
      includeFollowingPosts: true,
      includePopularPosts: true,
      includeGroupActivity: true,
      includeTrendingTopics: true,
    },
  };

  // Handler functions using the mutation hook
  const updateSettings = useCallback((newSettings: Partial<NewsletterSettings>) => {
    updateSettingsMutation.mutate(newSettings);
  }, [updateSettingsMutation]);

  const handleToggleNewsletter = useCallback((enabled: boolean) => {
    updateSettings({ enabled });
  }, []);

  const handleFrequencyChange = useCallback((frequency: NewsletterSettings['frequency']) => {
    updateSettings({ frequency });
  }, []);

  const handleContentPreferenceChange = useCallback((key: keyof NewsletterSettings['contentPreferences'], value: boolean) => {
    const newContentPreferences = {
      ...settings.contentPreferences,
      [key]: value,
    };
    updateSettings({ contentPreferences: newContentPreferences });
  }, [settings.contentPreferences]);

  return (
    <NewsletterSettingsPresentation
      theme={theme}
      styles={styles}
      navigation={navigation}
      settings={settings}
      loading={isLoading}
      saving={updateSettingsMutation.isLoading}
      onToggleNewsletter={handleToggleNewsletter}
      onFrequencyChange={handleFrequencyChange}
      onContentPreferenceChange={handleContentPreferenceChange}
    />
  );
};

// Presentation Component
interface NewsletterSettingsPresentationProps {
  theme: any;
  styles: any;
  navigation: any;
  settings: NewsletterSettings;
  loading: boolean;
  saving: boolean;
  onToggleNewsletter: (enabled: boolean) => void;
  onFrequencyChange: (frequency: NewsletterSettings['frequency']) => void;
  onContentPreferenceChange: (key: keyof NewsletterSettings['contentPreferences'], value: boolean) => void;
}

const NewsletterSettingsPresentation: React.FC<NewsletterSettingsPresentationProps> = React.memo(({
  theme,
  styles,
  navigation,
  settings,
  loading,
  saving,
  onToggleNewsletter,
  onFrequencyChange,
  onContentPreferenceChange,
}) => {
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <HeaderTitle title="Newsletter Settings" navigation={navigation} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.accent} />
          <CustomText style={styles.loadingText}>Loading settings...</CustomText>
        </View>
      </SafeAreaView>
    );
  }

  const frequencyOptions = [
    { key: 'daily', label: 'Daily' },
    { key: 'weekly', label: 'Weekly' },
    { key: 'bi-weekly', label: 'Twice a week' },
    { key: 'monthly', label: 'Monthly' },
  ];

  const contentOptions = [
    { key: 'includeFollowingPosts', label: 'Posts from people you follow' },
    { key: 'includePopularPosts', label: 'Popular posts' },
    { key: 'includeGroupActivity', label: 'Group activity' },
    { key: 'includeTrendingTopics', label: 'Trending topics' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <HeaderTitle title="Newsletter Settings" navigation={navigation} />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        
        {/* Newsletter Toggle */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <CustomText style={styles.sectionTitle}>Email Newsletter</CustomText>
          </View>
          <View style={styles.settingItem}>
            <View style={styles.settingContent}>
              <CustomText style={styles.settingLabel}>Receive newsletter emails</CustomText>
              <CustomText style={styles.settingDescription}>
                Get updates about posts, groups, and trending topics delivered to your inbox
              </CustomText>
            </View>
            <Switch
              value={settings.enabled}
              onValueChange={onToggleNewsletter}
              trackColor={{ false: theme.colors.border, true: theme.colors.accent }}
              thumbColor={settings.enabled ? theme.colors.background : theme.colors.textSecondary}
              disabled={saving}
            />
          </View>
        </View>

        {/* Frequency Settings */}
        {settings.enabled && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <CustomText style={styles.sectionTitle}>Frequency</CustomText>
            </View>
            {frequencyOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={styles.settingItem}
                onPress={() => onFrequencyChange(option.key as NewsletterSettings['frequency'])}
                disabled={saving}
              >
                <CustomText style={styles.settingLabel}>{option.label}</CustomText>
                <View style={[
                  styles.radioButton,
                  settings.frequency === option.key && styles.radioButtonSelected
                ]}>
                  {settings.frequency === option.key && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Content Preferences */}
        {settings.enabled && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <CustomText style={styles.sectionTitle}>Content Preferences</CustomText>
              <CustomText style={styles.sectionDescription}>
                Choose what type of content you'd like to receive
              </CustomText>
            </View>
            {contentOptions.map((option) => {
              const key = option.key as keyof NewsletterSettings['contentPreferences'];
              const value = Boolean(settings.contentPreferences[key]);

              return (
                <View key={option.key} style={styles.settingItem}>
                  <CustomText style={styles.settingLabel}>{option.label}</CustomText>
                  <Switch
                    value={value}
                    onValueChange={(newValue) => onContentPreferenceChange(key, newValue)}
                    trackColor={{ false: theme.colors.border, true: theme.colors.accent }}
                    thumbColor={value ? theme.colors.background : theme.colors.textSecondary}
                    disabled={saving}
                  />
                </View>
              );
            })}
          </View>
        )}

        {saving && (
          <View style={styles.savingIndicator}>
            <ActivityIndicator size="small" color={theme.colors.accent} />
            <CustomText style={styles.savingText}>Saving...</CustomText>
          </View>
        )}

      </ScrollView>
    </SafeAreaView>
  );
});

// Themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: rv(50),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: rv(200),
  },
  loadingText: {
    marginTop: rv(20),
    fontSize: fs("LG"),
    color: theme.colors.textSecondary,
  },
  section: {
    marginTop: rv(20),
    marginHorizontal: rv(16),
    backgroundColor: theme.colors.surface,
    borderRadius: rv(12),
    overflow: 'hidden',
  },
  sectionHeader: {
    paddingHorizontal: rv(16),
    paddingVertical: rv(16),
    backgroundColor: theme.colors.surfaceVariant,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  sectionTitle: {
    fontSize: fs("XL"),
    fontFamily: 'semibold',
    color: theme.colors.text,
    marginBottom: rv(5),
  },
  sectionDescription: {
    fontSize: fs("MD"),
    color: theme.colors.textSecondary,
    lineHeight: rv(18),
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: rv(16),
    paddingVertical: rv(16),
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  settingContent: {
    flex: 1,
    marginRight: rv(16),
  },
  settingLabel: {
    fontSize: fs("LG"),
    fontFamily: 'medium',
    color: theme.colors.text,
    marginBottom: rv(5),
  },
  settingDescription: {
    fontSize: fs("MD"),
    color: theme.colors.textSecondary,
    lineHeight: rv(16),
  },
  radioButton: {
    width: rv(20),
    height: rv(20),
    borderRadius: rv(10),
    borderWidth: 2,
    borderColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: theme.colors.accent,
  },
  radioButtonInner: {
    width: rv(10),
    height: rv(10),
    borderRadius: rv(5),
    backgroundColor: theme.colors.accent,
  },
  savingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: rv(16),
    marginTop: rv(16),
  },
  savingText: {
    marginLeft: rv(8),
    fontSize: fs("MD"),
    color: theme.colors.textSecondary,
  },
}));

export default NewsletterSettingsContainer;
