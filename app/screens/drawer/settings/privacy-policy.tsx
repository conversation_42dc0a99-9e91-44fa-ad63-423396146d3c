import { CustomText } from 'app/components/elements';
import React, { useState } from 'react';
import { ScrollView, StatusBar, View, SafeAreaView, ActivityIndicator } from 'react-native';
import HeaderTitle from 'app/components/HeaderTitle';
import WebView from 'react-native-webview';
import { useTranslation } from 'react-i18next';
import { useTheme, createThemedStyles } from 'app/theme';

type PrivacyPolicyProps = {
  navigation: any;
};

const PrivacyPolicy: React.FC<PrivacyPolicyProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [isLoading, setIsLoading] = useState(true);

  // CSS injection to override the external site's styling for dark mode
  const injectedCSS = `
    (function() {
      const style = document.createElement('style');
      style.innerHTML = \`
        * {
          background-color: ${theme.colors.background} !important;
          color: ${theme.colors.text} !important;
        }
        body {
          background-color: ${theme.colors.background} !important;
          color: ${theme.colors.text} !important;
        }
        p, div, span, h1, h2, h3, h4, h5, h6, li, td, th {
          color: ${theme.colors.text} !important;
          background-color: transparent !important;
        }
        a {
          color: ${theme.colors.primary} !important;
        }
        input, textarea, select {
          background-color: ${theme.colors.surface} !important;
          color: ${theme.colors.text} !important;
          border-color: ${theme.colors.border} !important;
        }
      \`;
      document.head.appendChild(style);
    })();
  `;

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={theme.colors.statusBarStyle} backgroundColor={theme.colors.background} />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <HeaderTitle title="Connectify Privacy Policy" navigation={navigation} />
        <View style={styles.container}>
          <WebView
            source={{ uri: 'https://app.pennytots.com/privacy-Policy' }}
            style={[styles.webView, { opacity: isLoading ? 0 : 1 }]}
            onLoadStart={() => setIsLoading(true)}
            onLoadEnd={() => setIsLoading(false)}
            injectedJavaScript={injectedCSS}
            onMessage={() => {}}
          />
          {isLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.accent} />
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Themed Styles
const createStyles = createThemedStyles((theme) => ({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  webView: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
}));

export default PrivacyPolicy;