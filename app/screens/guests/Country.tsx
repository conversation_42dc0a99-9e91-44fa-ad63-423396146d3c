import { CountryCode } from 'app/types/types'
import React, { useState } from 'react'
import { View, Text, StyleSheet, PixelRatio, Switch } from 'react-native'
import CountryPicker, { Country } from 'react-native-country-picker-modal'

// Theme system imports
import { useTheme, createThemedStyles, ThemedStatusBar } from 'app/theme';
import { Theme } from 'app/types/theme';

// Option component for switches
interface OptionProps {
  title: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
}

const Option: React.FC<OptionProps> = ({ title, value, onValueChange }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.optionContainer}>
      <Text style={styles.optionText}>{title}</Text>
      <Switch value={value} onValueChange={onValueChange} />
    </View>
  );
};

// Container Component (handles logic, state, theme)
const CountryPickerContainer: React.FC = () => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [countryCode, setCountryCode] = useState<CountryCode>('FR')
  const [country, setCountry] = useState<Country>(null)
  const [withCountryNameButton, setWithCountryNameButton] = useState<boolean>(
    false,
  )
  const [withFlag, setWithFlag] = useState<boolean>(true)
  const [withEmoji, setWithEmoji] = useState<boolean>(true)
  const [withFilter, setWithFilter] = useState<boolean>(true)
  const [withAlphaFilter, setWithAlphaFilter] = useState<boolean>(false)
  const [withCallingCode, setWithCallingCode] = useState<boolean>(false)

  const onSelect = (country: Country) => {
    setCountryCode(country.cca2)
    setCountry(country)
  }

  return (
    <CountryPickerPresentation
      theme={theme}
      styles={styles}
      countryCode={countryCode}
      country={country}
      withCountryNameButton={withCountryNameButton}
      setWithCountryNameButton={setWithCountryNameButton}
      withFlag={withFlag}
      setWithFlag={setWithFlag}
      withEmoji={withEmoji}
      setWithEmoji={setWithEmoji}
      withFilter={withFilter}
      setWithFilter={setWithFilter}
      withAlphaFilter={withAlphaFilter}
      setWithAlphaFilter={setWithAlphaFilter}
      withCallingCode={withCallingCode}
      setWithCallingCode={setWithCallingCode}
      onSelect={onSelect}
    />
  );
};

// Presentational Component Props Interface
interface CountryPickerPresentationProps {
  theme: Theme;
  styles: any;
  countryCode: CountryCode;
  country: Country | null;
  withCountryNameButton: boolean;
  setWithCountryNameButton: (value: boolean) => void;
  withFlag: boolean;
  setWithFlag: (value: boolean) => void;
  withEmoji: boolean;
  setWithEmoji: (value: boolean) => void;
  withFilter: boolean;
  setWithFilter: (value: boolean) => void;
  withAlphaFilter: boolean;
  setWithAlphaFilter: (value: boolean) => void;
  withCallingCode: boolean;
  setWithCallingCode: (value: boolean) => void;
  onSelect: (country: Country) => void;
}

// Presentational Component (pure UI rendering)
const CountryPickerPresentation: React.FC<CountryPickerPresentationProps> = ({
  theme,
  styles,
  countryCode,
  country,
  withCountryNameButton,
  setWithCountryNameButton,
  withFlag,
  setWithFlag,
  withEmoji,
  setWithEmoji,
  withFilter,
  setWithFilter,
  withAlphaFilter,
  setWithAlphaFilter,
  withCallingCode,
  setWithCallingCode,
  onSelect,
}) => {
  return (
    <View style={styles.container}>
      <ThemedStatusBar />
      <Text style={styles.welcome}>Welcome to Country Picker !</Text>
      <Option
        title='With country name on button'
        value={withCountryNameButton}
        onValueChange={setWithCountryNameButton}
      />
      <Option title='With flag' value={withFlag} onValueChange={setWithFlag} />
      <Option
        title='With emoji'
        value={withEmoji}
        onValueChange={setWithEmoji}
      />
      <Option
        title='With filter'
        value={withFilter}
        onValueChange={setWithFilter}
      />
      <Option
        title='With calling code'
        value={withCallingCode}
        onValueChange={setWithCallingCode}
      />
      <Option
        title='With alpha filter code'
        value={withAlphaFilter}
        onValueChange={setWithAlphaFilter}
      />
      <CountryPicker
        {...{
          countryCode,
          withFilter,
          withFlag,
          withCountryNameButton,
          withAlphaFilter,
          withCallingCode,
          withEmoji,
          onSelect,
        }}
        visible
      />
      <Text style={styles.instructionText}>Press on the flag to open modal</Text>
      {country !== null && (
        <Text style={styles.countryDataText}>{JSON.stringify(country, null, 2)}</Text>
      )}
    </View>
  )
};

// Create themed styles
const createStyles = createThemedStyles((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: 16,
  },
  welcome: {
    fontSize: 18,
    fontFamily: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 20,
  },
  optionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'medium',
    color: theme.colors.text,
  },
  instructionText: {
    fontSize: 14,
    fontFamily: 'regular',
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 20,
  },
  countryDataText: {
    fontSize: 12,
    fontFamily: 'regular',
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
}));

// Export container as default
export default CountryPickerContainer;