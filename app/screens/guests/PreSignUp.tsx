import React, { useEffect } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Text, View, Image, TouchableOpacity, StyleSheet, ImageBackground } from "react-native";
import { responsiveValue as rv } from "app/providers/responsive-value";
import { navigate } from "app/navigation/root";
import { useNavigation } from "@react-navigation/native";
import Logo_onboard from '@/app/assets/svg/connectifyBigLogo.svg'
import Pre_login from '@/app/assets/svg/PreLogin.svg'
import { t } from "i18next";
import { CustomText } from "app/components/elements";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Theme system imports
import { useTheme, createThemedStyles, ThemedStatusBar, createOptimizedThemedStyles } from 'app/theme';
import { Theme } from 'app/types/theme';
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";

// Container Component (handles logic, state, theme)
const PreSignUpContainer: React.FC = () => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const navigation = useNavigation();

  const handleNavigateToRegister = async () => {
    try {
      await AsyncStorage.removeItem('formData'); // Clear storage
      navigation.navigate("register" as never); // Navigate to the register screen
    } catch (error) {
      console.error('Error clearing form data', error);
    }
  };

  const handleNavigateToLogin = () => {
    navigation.navigate("login" as never);
  };

  return (
    <PreSignUpPresentation
      theme={theme}
      styles={styles}
      handleNavigateToLogin={handleNavigateToLogin}
      handleNavigateToRegister={handleNavigateToRegister}
    />
  );
};

// Presentational Component Props Interface
interface PreSignUpPresentationProps {
  theme: Theme;
  styles: any;
  handleNavigateToLogin: () => void;
  handleNavigateToRegister: () => void;
}

// Presentational Component (pure UI rendering)
const PreSignUpPresentation: React.FC<PreSignUpPresentationProps> = ({
  theme,
  styles,
  handleNavigateToLogin,
  handleNavigateToRegister,
}) => {
  return (
<View style={styles.container}>
    <ThemedStatusBar />
    <SafeAreaView style={styles.scrollview}>
    <View style={styles.logoContainer}>
          <Logo_onboard />
        </View>
     <View style={styles.textContainer}>
      <CustomText style={styles.subtitleText}>
      Join the Connectify Network. Connect, collaborate, and grow together
            </CustomText>
     </View>
      <View style={styles.buttonsContainer}>

        <View style={styles.buttonWrapper}>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={handleNavigateToLogin}
          >
            <Text style={styles.loginInnerText}>{t("login")}</Text>
          </TouchableOpacity>

        </View>
        <View style={styles.signupButtonWrapper}>
        <TouchableOpacity
            style={styles.signupButton}
            onPress={handleNavigateToRegister}
          >
            <Text style={styles.signupText}>{t("signUp")}</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.spacer}></View>
      </View>
    </SafeAreaView>

</View>
  );
};
// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flexGrow: 1,
    alignContent: "center",
    justifyContent: "center",
    backgroundColor: theme.colors.background
  },
  scrollview: {
    flex: 1,
    paddingHorizontal: rv(16),
    backgroundColor: 'transparent',
    alignContent: "center",
    justifyContent: "center",
    zIndex: 9999,
    position: 'absolute',
    width: '100%'
  },
  logoContainer: {
    alignSelf: 'center',
    marginTop: rv(46)
  },
  textContainer: {
    marginTop: rv(40),
  },
  subtitleText: {
    fontFamily: 'semiBold',
    fontSize: fs("MD"),
    textAlign: 'center',
    color: theme.colors.textSecondary,
    marginTop: rv(16)
  },
  buttonsContainer: {
    justifyContent: 'center',
    marginTop: rv(30),
  },
  buttonWrapper: {
    alignItems: 'center'
  },
  signupButtonWrapper: {
    marginTop: rv(16),
    alignItems: 'center'
  },
  spacer: {
    height: rv(90)
  },
  signupText: {
    fontSize: fs("LG"),
    color: theme.colors.text,
    fontFamily: 'medium',
  },
  loginInnerText: {
    color: theme.colors.background,
    fontFamily: 'medium',
    fontSize: fs("LG")
  },
  signupButton: {
    maxWidth: rv(364),
    width: '100%',
    backgroundColor: theme.colors.inputBackground,
    height: rv(50),
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: theme.colors.primary
  },
  loginButton: {
    maxWidth: rv(364),
    width: '100%',
    height: rv(50),
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: theme.colors.primary,
  },
}));

// Export container as default
export default PreSignUpContainer;