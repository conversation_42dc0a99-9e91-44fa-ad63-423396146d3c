import React, {
  Component,
  useState,
  useEffect,
  useRef,
  useContext,
} from "react";
// import { useIsFocused } from '@react-navigation/native';
import {
  TextInput,
  Text,
  View,
  Image,
  Button,
  StyleSheet,
  Alert,
  TouchableHighlight,
  ScrollView,
  Linking,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { SafeAreaView } from 'react-native-safe-area-context';
import { default as themeFont } from "app/assets/themes/fonts.json";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { CustomButton, CustomText } from "app/components/elements";
import SVG from "app/providers/svg";
import RNPickerSelect from "react-native-picker-select";
import { Flag } from "react-native-svg-flagkit";
import countryAndCode from "app/Data/countryAndCode";
import { PickerSelectStyle } from "app/assets/styles";
import { accountLogin } from "app/redux/user/hooks";
import useNotification from "app/helpers/notification";
import { useDispatch, useSelector } from "react-redux";
import { appLoading, setLoading } from "app/redux/main/reducer";
import CustomCountryPicker from "app/components/CountryPicker";
import { Country as CountryType } from "../../types/types";
import CountryPicker, { Country } from "react-native-country-picker-modal";
import { useTranslation } from "react-i18next";
import * as Notifications from "expo-notifications";
import CustomModal from "app/components/elements/Modals";
// import { GhanaloginlogoSVG } from "app/providers/svg/loader";
import { responsiveValue as rv } from "app/providers/responsive-value";
import Logo_onboard from '@/app/assets/svg/connectifyBigLogo.svg'
// import Logo_onboard_sm from '@/app/assets/svg/AgriFood Logo small.svg'
import Arrow from '@/app/assets/svg/arrowEast.svg'

// Theme system imports
import { useTheme, createThemedStyles, ThemedStatusBar, createOptimizedThemedStyles } from 'app/theme';
import { createCommonStyles } from 'app/assets/styles/CommonStyles';
import { Theme } from 'app/types/theme';
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";
import { RootState } from 'app/redux';


type SignInScreenProps = {
  navigation: any;
};

interface CustomCountryPickerProps {
  onCountrySelect: (countryCode: string) => void;
}

// Container Component (handles logic, state, theme)
const LoginContainer: React.FC<SignInScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const commonStyles = createCommonStyles(theme,fs);
  const styles = createStyles(theme, fs);

  const loading = useSelector(appLoading);
  const dispatch = useDispatch();
  const [userPassword, setUserPassword] = useState("");
  const [phone, setPhone] = useState();
  const [country, setCountry] = useState("+234");
  const [selectedCountry, setSelectedCountry] = useState<CountryType | null>(
    null
  );
  const [countryFlag, setCountryFlag] = useState("NG");
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [isCountryPickerVisible, setCountryPickerVisible] = useState(false);
  const fontSizeScale = useSelector((state: RootState) => state.fontSize.fontSizeScale);

  type CountryProps = {
    label: string;
    value: string;
    name: string;
    flag: string;
  };

  function getCountryFlag(): string {
    let countryData = countryAndCode.find((item: CountryProps) => {
      return item.value == country;
    });
    if (countryData) {
      return countryData.flag;
    } else {
      return "";
    }
  }

  useEffect(() => {
    setCountryFlag(getCountryFlag());
    console.log(country);
  }, [country]);

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);
    setCountry(`+${country.callingCode[0]}`);
  };

  const handleSubmitPress = async () => {
    if (!phone) {
      setAlertMessage("Please fill phone number");
      setModalVisible(true);
      return;
    }
    if (!userPassword) {
      setAlertMessage("Please fill your PIN");
      setModalVisible(true);
      return;
    }

    await accountLogin({
      phone_number: {
        code: country,
        number: phone,
      },
      password: userPassword,
    });
  };

  const handleConfirm = async () => {
    setModalVisible(false);
  };

  return (
    <LoginPresentation
      theme={theme}
      styles={styles}
      commonStyles={commonStyles}
      navigation={navigation}
      loading={loading}
      userPassword={userPassword}
      setUserPassword={setUserPassword}
      phone={phone}
      setPhone={setPhone}
      country={country}
      selectedCountry={selectedCountry}
      countryFlag={countryFlag}
      t={t}
      modalVisible={modalVisible}
      setModalVisible={setModalVisible}
      alertMessage={alertMessage}
      isCountryPickerVisible={isCountryPickerVisible}
      setCountryPickerVisible={setCountryPickerVisible}
      handleCountryChange={handleCountryChange}
      handleSubmitPress={handleSubmitPress}
      handleConfirm={handleConfirm}
    />
  );
};
// Presentational Component Props Interface
interface LoginPresentationProps {
  theme: Theme;
  styles: any;
  commonStyles: any;
  navigation: any;
  loading: boolean;
  userPassword: string;
  setUserPassword: (password: string) => void;
  phone: any;
  setPhone: (phone: any) => void;
  country: string;
  selectedCountry: CountryType | null;
  countryFlag: string;
  t: any;
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  alertMessage: string;
  isCountryPickerVisible: boolean;
  setCountryPickerVisible: (visible: boolean) => void;
  handleCountryChange: (country: Country) => void;
  handleSubmitPress: () => void;
  handleConfirm: () => void;
}

// Presentational Component (pure UI rendering)
const LoginPresentation: React.FC<LoginPresentationProps> = ({
  theme,
  styles,
  commonStyles,
  navigation,
  loading,
  userPassword,
  setUserPassword,
  phone,
  setPhone,
  country,
  selectedCountry,
  countryFlag,
  t,
  modalVisible,
  setModalVisible,
  alertMessage,
  isCountryPickerVisible,
  setCountryPickerVisible,
  handleCountryChange,
  handleSubmitPress,
  handleConfirm,
}) => {
  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardContainer}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          {modalVisible && (
            <CustomModal
              modalVisible={modalVisible}
              setModalVisible={setModalVisible}
              type="alert" // or "stake" based on the scenario
              message={alertMessage}
              handleConfirm={handleConfirm}
              navigation
            />
          )}
          <ThemedStatusBar />

        {/* Your existing components */}
        {/* <GhanaloginlogoSVG style={{alignSelf: 'center'}} /> */}

        <Logo_onboard  />

       {/* <Image

       source={require('app/assets/Logo_onboard.png')}
       style={{width: rv(200), alignSelf: 'center'}}

     /> */}

     <View style={styles.headerContainer}>

     <CustomText style={styles.loginTitle}>
    Login
      </CustomText>
      <CustomText style={styles.loginSubtitle}>
      Your gateway to connecting with your closest network
      </CustomText>

     </View>

      <View style={styles.formContainer}>
      <CustomText style={styles.fieldLabel} textType='medium'>
           Phone number
          </CustomText>

      <View >
          <View style={styles.phoneInputContainer}>
            <View style={styles.countryCodeContainer}>
              <TouchableOpacity
                onPress={() => {
                  setCountryPickerVisible(true);
                }}
                hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                style={styles.arrowContainer}
              >
                <Text style={styles.arrow}>▼</Text>
              </TouchableOpacity>
              <View style={styles.container2}>
              <TouchableOpacity
                onPress={() => setCountryPickerVisible(true)}
                hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                style={styles.container2}
              >
                <Text style={styles.countryCodeText}>
                  +{selectedCountry ? selectedCountry.callingCode[0] : "234"}
                </Text>
                {selectedCountry && (
                  <Image
                    style={styles.flagImage}
                    source={{ uri: selectedCountry.flag }}
                  />
                )}
                </TouchableOpacity>
                <CountryPicker
                  withCallingCode
                  withFilter
                  withFlag
                  onSelect={handleCountryChange}
                  countryCode={selectedCountry ? selectedCountry.cca2 : "NG"}
                  containerButtonStyle={styles.countryPickerContainer}
                  visible={isCountryPickerVisible}
                  onClose={() => setCountryPickerVisible(false)}
                />
              </View>
            </View>
            <View style={styles.phoneNumberInputContainer}>

              <TextInput
                style={commonStyles.inputField}
                maxLength={10}
                onChangeText={(text: any) => {
                  setPhone(text);
                }}
                inlineImageLeft="callplain"
                placeholder={t("LoginPage_PhoneNumber")}
                inlineImagePadding={20}
                keyboardType="phone-pad"
                value={phone}
                placeholderTextColor={theme.colors.textPlaceholder}
              />
            </View>
          </View>
          <CustomText style={styles.fieldLabel} textType='medium'>
          Pin
          </CustomText>
          <TextInput
            style={commonStyles.inputField}
            onChangeText={(userPassword) => setUserPassword(userPassword)}
            inlineImageLeft="key"
            placeholder={t("LoginPage_pin")}
            inlineImagePadding={20}
            textContentType="password"
            secureTextEntry={true}
            keyboardType="numeric"
            maxLength={4}
            value={userPassword}
            placeholderTextColor={theme.colors.textPlaceholder}
          />
        </View>
      </View>
        <View style={styles.buttonContainer}>
          <CustomButton
            label={t("LoginPage_loginBtn")}
            onPress={() => {
              handleSubmitPress();
            }}
            style={{

              textAlign: 'left'

            }}
            loading={loading}

          />

        </View>
     

        <View style={styles.bottomLinksContainer}>
          <View style={styles.bottomLinksRow}>
              <View >
              <TouchableOpacity
              onPress={() => {
                navigation.navigate("register");
              }}
            >
              <CustomText style={styles.linkText}>
                {t("LoginPage_signUp")}
              </CustomText>
            </TouchableOpacity>
        </View>
            <CustomText
              textType="bold"
              style={styles.dividerText}
            >
              |
            </CustomText>
            <View>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("forgot-password");
                }}
              >
                <CustomText style={styles.linkText} textType="medium">
                  {t("LoginPage_forgotPass")}
                </CustomText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  headerContainer: {
    marginTop: rv(18),
    width: '85%'
  },
  loginTitle: {
    fontFamily: 'semiBold',
    fontSize: fs("LG"),
    color: theme.colors.textSecondary
  },
  loginSubtitle: {
    fontFamily: 'medium',
    fontSize: fs("MD"),
    color: theme.colors.textSecondary,
    marginTop: rv(10),
    marginBottom: rv(25)
  },
  formContainer: {
    width: '85%'
  },
  fieldLabel: {
    color: theme.colors.text,
    fontSize: fs("BASE")
  },
  phoneInputContainer: {
    flex: 1,
    flexDirection: "row",
    marginTop: rv(2)
  },
  countryCodeContainer: {
    ...{
      height: rv(50),
      paddingHorizontal: 20,
      backgroundColor: theme.colors.inputBackground,
      marginBottom: 15,
      borderStyle: 'solid',
      marginTop: 10,
      fontSize: fs("MD"),
      fontFamily: 'medium',
      borderWidth: 2,
      borderColor: theme.colors.inputBorder,
      color: theme.colors.textPlaceholder
    },
    padding: 5,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 10,
    flexDirection: "row",
  },
  phoneNumberInputContainer: {
    flex: 1,
    marginLeft: 1,
  },
  container2: {
    flexDirection: "row",
    alignItems: "center",
  },
  countryCodeText: {
    marginLeft: rv(7),
    fontSize: fs("MD"),
    marginRight: rv(3),
    color: theme.colors.text
  },
  flagImage: {
    width: rv(25),
    height: rv(15),
    marginLeft: -25,
  },
  arrowContainer: {
    marginLeft: 5,
  },
  arrow: {
    fontSize: 13,
    color: theme.colors.text,
  },
  countryPickerContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  buttonContainer: {
    width: wp("85%")
  },
  bottomLinksContainer: {
    width: "85%",
    marginTop: rv(30),
    marginBottom: rv(25),
  },
  bottomLinksRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: 'center',
  },
  linkText: {
    color: theme.colors.text,
    fontSize: fs("MD"),
    textAlign: "center",
    fontFamily: 'medium'
  },
  dividerText: {
    paddingHorizontal: 20,
    color: theme.colors.primary
  }
}));

// Export container as default
export default LoginContainer;