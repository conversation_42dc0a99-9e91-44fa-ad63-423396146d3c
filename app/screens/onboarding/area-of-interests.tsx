import React, {
  FunctionComponent,
  useState,
  useContext,
  useEffect,
} from "react";
import {
  View,
  Image,
  TextInput,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
// import { useTranslation } from 'react-i18next';
import { SignUpHeaderSVG } from "app/providers/svg/loader";
import { CustomText, CustomButton } from "app/components/elements";
import Loader from "app/components/elements/Loader";
import SelectInterests from "app/components/elements/SelectInterests";
import { Axios } from "app/api/axios";
import { queryClient, updateUserInfo } from "app/redux/user/hooks";
import { useTranslation } from "react-i18next";
import { showModal, hideModal } from "app/providers/modals";
import { responsiveValue as rv } from "app/providers/responsive-value";
import InterestSVG from "@/app/assets/svg/connectifyHorizontallogo.svg";
import { CommonStyles } from "app/assets/styles/CommonStyles";
import {
  useTheme,
  createThemedStyles,
  createOptimizedThemedStyles,
} from "app/theme";
import { Theme } from "app/types/theme";
import { useOptimizedFontSize } from "app/hooks/useOptimizedFontSize";

// Container Component
const AreaOfInterestsContainer: FunctionComponent<{
  navigation: any;
}> = ({ navigation }) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const [interests, setInterests] = useState<string[]>([]);
  const [allInterests, setAllInterests] = useState<IInterest[]>([]);
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    Axios({
      method: "get",
      url: "/tags/list",
    }).then((response: any) => {
      let interestData = response.data.data;
      interestData.sort((a: IInterest, b: IInterest) => {
        if (a.name.toLowerCase() === "business") return -1;
        if (b.name.toLowerCase() === "business") return 1;
        return 0;
      });
      setAllInterests(interestData);
    });
  }, []);

  const handleStatus = () => {};

  // const { t, i18n } = useTranslation();
  const message =
    "Hello everyone. I just joined our amazing network. Nice to be here. Aut Caesar Aut Nihil - The Best or Nothing 🙏🏾";
  async function handleSubmitPost(defaultContent: string) {
    try {
      const tagsArray = allInterests.map((tag) => tag._id);

      const topicData = new FormData();
      topicData.append("content", defaultContent);
      topicData.append("tags", JSON.stringify(tagsArray));

      const response = await Axios.post("/topics/create", topicData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      // ❷ Invalidate queries, just like before
      queryClient.invalidateQueries(["topics"]);

      // ❸ Return the newly created topic object
      return response.data.topic;
    } catch (error: any) {
      console.error("Error creating topic:", error);
      throw error; // re-throw so the caller can catch it
    }
  }

  async function updateAreaOfInterests() {
    setLoading(true);
    try {
      // ❶ Update user tags (interests)
      await Axios.post("/tags/set-user-tags", interests);
      // ❷ Refresh user info
      await updateUserInfo();

      // ❸ Create the default welcome post
      const createdTopic = await handleSubmitPost(message);
      console.log(message, "tagMessage");

      // ➍ Navigate once the topic is successfully created
      if (createdTopic && createdTopic._id) {
        navigation.navigate("Home", {
          screen: "home-landing",
          params: {
            screen: "TopicScreen",
            params: {
              postid: createdTopic._id,
            },
          },
        });
      } else {
        // Optionally handle edge case if no topic is returned
        console.warn("No topic returned from handleSubmitPost");
      }
    } catch (error: any) {
      console.error("Error in updateAreaOfInterests:", error);

      // If your backend sometimes returns a "Please select at least one interest" error:
      if (
        error?.response?.data?.message?.includes(
          "Please select at least one interest"
        )
      ) {
        showModal({
          modalVisible: true,
          title: "Alert",
          message: "You need to select at least one interest",
          setModalVisible: hideModal,
          type: "alert",
          handleConfirm: hideModal,
          handleAlert: hideModal,
        });
      } else {
        // Fallback for other errors
        showModal({
          modalVisible: true,
          title: "Alert",
          message: error?.response?.data?.message || "An error occurred",
          setModalVisible: hideModal,
          type: "alert",
          handleConfirm: hideModal,
          handleAlert: hideModal,
        });
      }
    } finally {
      setLoading(false);
    }
  }

  return (
    <AreaOfInterestsPresentation
      theme={theme}
      styles={styles}
      loading={loading}
      interests={interests}
      t={t}
      setInterests={setInterests}
      updateAreaOfInterests={updateAreaOfInterests}
      navigation={navigation}
    />
  );
};

// Presentational Component
interface AreaOfInterestsPresentationProps {
  theme: Theme;
  styles: any;
  loading: boolean;
  interests: string[];
  t: any;
  setInterests: (interests: string[]) => void;
  updateAreaOfInterests: () => void;
  navigation: any;
}

const AreaOfInterestsPresentation: React.FC<
  AreaOfInterestsPresentationProps
> = ({
  theme,
  styles,
  loading,
  interests,
  t,
  setInterests,
  updateAreaOfInterests,
  navigation,
}) => {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
    >
      <ScrollView style={styles.scrollView}>
        <Loader loading={loading} />
        <View
          style={{
            flexDirection: "row",

            paddingTop: rv(10),
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <CustomText
            style={{
              color: "#696969",
              fontSize: 21,
              fontFamily: "medium",
            }}
            // textType='medium'
          >
            {t("Signup_areas_of_interest")}
          </CustomText>
          <InterestSVG />

          {/* <View
          style={{
            width: '50%',
          }}
        >
          <SignUpHeaderSVG />
        </View> */}
        </View>
        <View
          style={{
            flexDirection: "column",
            marginTop: rv(12),
          }}
        >
          <CustomText
            style={[
              styles.font_md,
              {
                color: "#696969",
                fontFamily: "medium",
              },
            ]}
          >
            I’m interested in posts from
          </CustomText>

          <SelectInterests
            interests={interests}
            setInterests={setInterests}
            multiple={true}
          />
        </View>

        <View style={styles.buttonSection}>
          <View style={styles.buttonWrapper}>
            <CustomButton
              buttonTheme="primary"
              label="Finished"
              onPress={() => updateAreaOfInterests()}
              loading={loading}
              disabled={loading}
            />
            <TouchableOpacity
              onPress={() => navigation.navigate("status")}
              style={styles.backButton}
            >
              <CustomText style={styles.backButtonText} textType="medium">
                Back
              </CustomText>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    display: "flex",
    flexDirection: "row",
    backgroundColor: theme.colors.background,
    paddingTop: rv(10),
    flex: 1,
  },
  scrollView: {
    flex: 1,
    backgroundColor: theme.colors.background,
    flexDirection: "column",
    paddingHorizontal: wp("5"),
  },
  headerContainer: {
    flexDirection: "row",
    paddingTop: rv(10),
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerText: {
    color: theme.colors.textSecondary,
    fontSize: fs("XXXL"),
    fontFamily: "medium",
  },
  contentContainer: {
    flexDirection: "column",
    marginTop: rv(12),
  },
  descriptionText: {
    fontSize: fs("MD"),
    color: theme.colors.textSecondary,
    fontFamily: "medium",
  },
  buttonSection: {
    marginTop: "auto",
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  buttonWrapper: {
    alignSelf: "center",
    width: "93%",
  },
  backButton: {
    height: rv(50),
    paddingHorizontal: 20,
    backgroundColor: "transparent",
    marginBottom: 15,
    borderStyle: "solid",
    marginTop: 10,
    borderWidth: 2,
    borderColor: theme.colors.border,
    alignItems: "center",
    justifyContent: "center",
  },
  backButtonText: {
    textAlign: "center",
    fontSize: fs("MD"),
    color: theme.colors.text,
  },
  font_md: {
    fontSize: fs("BASE"),
  },
}));

export default AreaOfInterestsContainer;
