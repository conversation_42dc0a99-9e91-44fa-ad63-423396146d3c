import React, {
  FunctionComponent,
  useState,
  useRef,
  useEffect,
  useContext,
} from 'react';
import {
  View,
  Image,
  TextInput,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { SignUpHeaderSVG } from 'app/providers/svg/loader';
import { CustomText, CustomButton } from 'app/components/elements';
import Loader from 'app/components/elements/Loader';
import { GlobalContext } from 'app/GlobalProvider';
import { CommonStyles } from 'app/assets/styles';
import { Axios } from 'app/api/axios';
import { ShowAlert } from 'app/providers/toast';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { showModal, hideModal } from 'app/providers/modals';
import HelpUsSVG from '@/app/assets/svg/connectifyHorizontallogo.svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme, createThemedStyles, createOptimizedThemedStyles } from 'app/theme';
import { Theme } from 'app/types/theme';
import { useOptimizedFontSize } from 'app/hooks/useOptimizedFontSize';
// Container Component
const PersonalContainer: FunctionComponent<{
  navigation: any;
}> = ({ navigation }) => {
  const { theme } = useTheme();
  const { fs } = useOptimizedFontSize();
  const styles = createStyles(theme, fs);
  const [loading, setLoading] = useState(false);
  const [bio, setBio] = useState('');
  const [link1, setLink1] = useState('');
  const [link2, setLink2] = useState('');
  const [link3, setLink3] = useState('');
  const { t } = useTranslation();

  const saveFormData = async () => {
    try {
      await AsyncStorage.setItem(
        'personalformData',
        JSON.stringify({ bio, link1, link2, link3 })
      );
    } catch (error) {
      console.error('Error saving form data', error);
    }
  };

  const loadFormData = async () => {
    try {
      const storedData = await AsyncStorage.getItem('personalformData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setBio(parsedData.bio || '');
        setLink1(parsedData.link1 || '');
        setLink2(parsedData.link2 || '');
        setLink3(parsedData.link3 || '');
      }
    } catch (error) {
      console.error('Error loading form data', error);
    }
  };

  // Call loadFormData when the component mounts
  useEffect(() => {
    loadFormData();
  }, []);

  // Update storage when values change
  useEffect(() => {
    saveFormData();
  }, [bio, link1, link2, link3]);

  const validateURL = (link: string) => {
    const regex = new RegExp(
      '(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?'
    );
    return regex.test(link);
  };

  const updateProfile = () => {
    if (!bio) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Bio field must be filled before proceeding.',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
      });
      return;
    }

    // Only validate the link if it's provided
    if (link1 && !validateURL(link1)) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Please enter a valid URL',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: () => hideModal(),
        handleAlert: () => hideModal(),
      });
      return;
    }

    // Show loader while processing
    setLoading(true);

    // Prepare form data for submission
    const formData = {
      bio,
      linkedin: link1,
    };

    // Submit form data to the backend
    Axios({
      method: 'POST',
      url: '/user/update-profile',
      data: formData,
    })
      .then((response: any) => {
        showModal({
          modalVisible: true,
          title: 'Success',
          message: 'Updated successfully',
          setModalVisible: hideModal, // Function to close the modal
          type: 'success-alert',
          handleConfirm: () => hideModal(),
          handleAlert: () => hideModal(),
        });
        // Navigate to another screen (e.g., 'company') after success
        navigation.navigate('company');
      })
      .catch((error) => {
        showModal({
          modalVisible: true,
          title: 'Error',
          message: 'Failed to update profile',
          setModalVisible: hideModal, // Function to close the modal
          type: 'error-alert',
          handleConfirm: () => hideModal(),
          handleAlert: () => hideModal(),
        });
        console.error('Update Profile Error:', error);
      })
      .finally(() => {
        setLoading(false); // Stop the loader after the request finishes
      });
  };

  return (
    <PersonalPresentation
      theme={theme}
      styles={styles}
      loading={loading}
      bio={bio}
      link1={link1}
      t={t}
      setBio={setBio}
      setLink1={setLink1}
      updateProfile={updateProfile}
      navigation={navigation}
    />
  );
};

// Presentational Component
interface PersonalPresentationProps {
  theme: Theme;
  styles: any;
  loading: boolean;
  bio: string;
  link1: string;
  t: any;
  setBio: (value: string) => void;
  setLink1: (value: string) => void;
  updateProfile: () => void;
  navigation: any;
}

const PersonalPresentation: React.FC<PersonalPresentationProps> = ({
  theme,
  styles,
  loading,
  bio,
  link1,
  t,
  setBio,
  setLink1,
  updateProfile,
  navigation,
}) => {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <Loader loading={loading} />
        <View style={styles.headerContainer}>
          <View style={styles.headerTextContainer}>
            <CustomText style={styles.headerText} textType='medium'>
              {t('Signup_beFound')}
            </CustomText>
          </View>
          <HelpUsSVG />
        </View>
        <View style={styles.contentContainer}>
          <CustomText style={styles.descriptionText}>
            {t('Signup_page3text')}
          </CustomText>

          <View style={styles.fieldContainer}>
            <CustomText style={styles.fieldLabel}>
              {t('EditProfile_Bio')}
            </CustomText>

            <TextInput
              style={[CommonStyles.inputField, { color: theme.colors.text }]}
              onChangeText={(text: string) => {
                setBio(text);
              }}
              inlineImageLeft='callplain'
              placeholder={t('EditProfile_Bio')}
              placeholderTextColor={theme.colors.textPlaceholder}
              inlineImagePadding={20}
              value={bio}
            />
          </View>

          <View style={styles.linkSection}>
            <CustomText style={styles.linkSectionLabel}>
              Prove your identity: (optional)
            </CustomText>
            <View style={styles.linkContainer}>
              <View style={styles.inputGroup}>
                <TextInput
                  style={[CommonStyles.inputField, { color: theme.colors.text }]}
                  onChangeText={(text) => setLink1(text)}
                  placeholder='e.g Linkedin profile link (optional)'
                  placeholderTextColor={theme.colors.textPlaceholder}
                  value={link1}
                />
              </View>
            </View>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity onPress={() => navigation.navigate('welcome')}>
            <CustomText style={styles.backButtonText} textType='medium'>
              Back
            </CustomText>
          </TouchableOpacity>

          <View style={styles.nextButtonContainer}>
            <CustomButton
              label={t('Signup_nextbtn')}
              onPress={() => updateProfile()}
              style={styles.nextButton}
              loading={loading}
            />
          </View>
        </View>
        {/* <View style={{display: 'flex', flexDirection: 'row', justifyContent: 'center', marginTop: rv(40), marginBottom: rv(10)}}>
        <TouchableOpacity onPress={()=>{
          navigation.navigate('login')
        }}>
        <CustomText
              style={{ color: "#000000", fontSize: rv(13) }}
              textType="medium"
            >
             {t("Signup_loginbtn")}
            </CustomText>
          </TouchableOpacity>
          <CustomText
              textType="medium"
              style={{
                paddingHorizontal: rv(40),
              }}
            >
              |
            </CustomText>
            <View >
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("forgot-password");
                }}
              >
                <CustomText
                  style={{ color: "#000000", fontSize: rv(13) }}
                  textType="medium"
                >
                 
                  {t("LoginPage_forgotPass")}
                </CustomText>
              </TouchableOpacity>
            </View>
        </View>  */}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

// Create themed styles
const createStyles = createOptimizedThemedStyles((theme, fs) => ({
  container: {
    display: 'flex',
    flexDirection: 'row',
    backgroundColor: theme.colors.background,
    paddingTop: rv(10),
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingTop: rv(10),
    backgroundColor: theme.colors.background,
    flexDirection: 'column',
    paddingHorizontal: wp('5'),
  },
  headerContainer: {
    flexDirection: 'row',
    display: 'flex',
    justifyContent: 'space-between',
  },
  headerTextContainer: {
    width: '50%',
  },
  headerText: {
    color: theme.colors.textSecondary,
    fontSize: fs("XXXL"),
    fontFamily: 'medium',
  },
  contentContainer: {
    flexDirection: 'column',
  },
  descriptionText: {
    fontSize: fs("MD"),
    marginTop: rv(12),
    marginBottom: 20,
    color: theme.colors.textSecondary,
    fontFamily: 'regular',
  },
  fieldContainer: {
    flexDirection: 'column',
  },
  fieldLabel: {
    color: theme.colors.text,
    fontSize: fs("MD"),
    fontFamily: 'medium',
  },
  linkSection: {
    marginTop: 6,
    flexDirection: 'column',
  },
  linkSectionLabel: {
    color: theme.colors.text,
    fontSize: fs("BASE"),
    fontFamily: 'medium',
    marginBottom: rv(10),
  },
  linkContainer: {
    display: 'flex',
    flexDirection: 'column',
  },
  inputGroup: {
    // Add any specific styling for input group if needed
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginTop: rv(40),
  },
  backButtonText: {
    color: theme.colors.text,
    fontSize: fs("MD"),
  },
  nextButtonContainer: {
    width: wp('30'),
  },
  nextButton: {
    width: '100%',
    maxWidth: rv(118),
    borderRadius: rv(12),
    paddingVertical: rv(13),
    alignSelf: 'flex-end',
  },
}));

export default PersonalContainer;
