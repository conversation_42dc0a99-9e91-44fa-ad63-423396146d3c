import { queryClient } from 'app/redux/user/hooks';
import { topic } from 'app/api/topic';
import { chat } from 'app/api/chat';
import { group } from 'app/api/group';
import { performanceMonitor } from 'app/utils/performanceMonitor';

interface WarmupConfig {
  priority: 'critical' | 'important' | 'nice-to-have';
  delay?: number; // Delay before starting warmup (ms)
}

class CacheWarmingService {
  private isWarming = false;
  private warmupQueue: Array<{ fn: () => Promise<void>; config: WarmupConfig }> = [];

  // Warm up critical data when app starts
  async warmupCriticalData() {
    if (this.isWarming) return;
    
    this.isWarming = true;
    console.log('🔥 Starting cache warmup...');
    
    try {
      // Critical data - load immediately
      await this.warmupWithConfig([
        {
          fn: () => this.warmupTopics(),
          config: { priority: 'critical' }
        },
        {
          fn: () => this.warmupChats(),
          config: { priority: 'critical' }
        },
        {
          fn: () => this.warmupMyGroups(),
          config: { priority: 'critical' }
        }
      ]);

      // Important data - load after a short delay
      setTimeout(() => {
        this.warmupWithConfig([
          {
            fn: () => this.warmupConvos(),
            config: { priority: 'important' }
          },
          {
            fn: () => this.warmupSuggestedGroups(),
            config: { priority: 'important' }
          }
        ]);
      }, 2000);

      // Nice-to-have data - load after longer delay
      setTimeout(() => {
        this.warmupWithConfig([
          {
            fn: () => this.warmupRecentTopicDetails(),
            config: { priority: 'nice-to-have' }
          }
        ]);
      }, 5000);

    } catch (error) {
      console.warn('Cache warmup failed:', error);
    } finally {
      this.isWarming = false;
      console.log('✅ Cache warmup completed');
    }
  }

  // Warm up topics list
  private async warmupTopics() {
    try {
      performanceMonitor.startTiming('Warmup: Topics');
      
      await queryClient.prefetchQuery(
        ['topics'],
        topic.getTopics,
        {
          staleTime: 1000 * 60 * 2, // 2 minutes
          cacheTime: 1000 * 60 * 30, // 30 minutes
        }
      );
      
      performanceMonitor.endTiming('Warmup: Topics');
    } catch (error) {
      console.warn('Failed to warmup topics:', error);
    }
  }

  // Warm up chat list
  private async warmupChats() {
    try {
      performanceMonitor.startTiming('Warmup: Chats');
      
      await queryClient.prefetchQuery(
        ['my-chats'],
        chat.getChats,
        {
          staleTime: 1000 * 60 * 3, // 3 minutes
          cacheTime: 1000 * 60 * 60, // 1 hour
        }
      );
      
      performanceMonitor.endTiming('Warmup: Chats');
    } catch (error) {
      console.warn('Failed to warmup chats:', error);
    }
  }

  // Warm up conversations
  private async warmupConvos() {
    try {
      performanceMonitor.startTiming('Warmup: Convos');

      await queryClient.prefetchQuery(
        ['convos'],
        topic.getConvos,
        {
          staleTime: 1000 * 60 * 5, // 5 minutes
          cacheTime: 1000 * 60 * 30, // 30 minutes
        }
      );

      performanceMonitor.endTiming('Warmup: Convos');
    } catch (error) {
      console.warn('Failed to warmup convos:', error);
    }
  }

  // Warm up user's groups
  private async warmupMyGroups() {
    try {
      performanceMonitor.startTiming('Warmup: My Groups');

      await queryClient.prefetchQuery(
        ['my-groups'],
        group.getMyGroups,
        {
          staleTime: 1000 * 60 * 5, // 5 minutes
          cacheTime: 1000 * 60 * 60, // 1 hour
        }
      );

      performanceMonitor.endTiming('Warmup: My Groups');
    } catch (error) {
      console.warn('Failed to warmup my groups:', error);
    }
  }

  // Warm up suggested groups
  private async warmupSuggestedGroups() {
    try {
      performanceMonitor.startTiming('Warmup: Suggested Groups');

      await queryClient.prefetchQuery(
        ['suggested-groups'],
        group.getSuggestedGroups,
        {
          staleTime: 1000 * 60 * 15, // 15 minutes
          cacheTime: 1000 * 60 * 60 * 2, // 2 hours
        }
      );

      performanceMonitor.endTiming('Warmup: Suggested Groups');
    } catch (error) {
      console.warn('Failed to warmup suggested groups:', error);
    }
  }

  // Warm up recent topic details (prefetch first few topics)
  private async warmupRecentTopicDetails() {
    try {
      performanceMonitor.startTiming('Warmup: Recent Topic Details');
      
      // Get cached topics list
      const cachedTopics = queryClient.getQueryData(['topics']) as any[];
      
      if (cachedTopics && cachedTopics.length > 0) {
        // Prefetch details for first 3 topics
        const topicsToWarmup = cachedTopics.slice(0, 3);
        
        await Promise.all(
          topicsToWarmup.map(async (topicItem) => {
            try {
              await queryClient.prefetchQuery(
                ['topic', topicItem._id],
                () => topic.getSingleTopic(topicItem._id),
                {
                  staleTime: 1000 * 60 * 10, // 10 minutes
                  cacheTime: 1000 * 60 * 60, // 1 hour
                }
              );
            } catch (error) {
              // Ignore individual topic failures
            }
          })
        );
      }
      
      performanceMonitor.endTiming('Warmup: Recent Topic Details');
    } catch (error) {
      console.warn('Failed to warmup recent topic details:', error);
    }
  }

  // Execute warmup functions with configuration
  private async warmupWithConfig(
    warmupItems: Array<{ fn: () => Promise<void>; config: WarmupConfig }>
  ) {
    const criticalItems = warmupItems.filter(item => item.config.priority === 'critical');
    const importantItems = warmupItems.filter(item => item.config.priority === 'important');
    const niceToHaveItems = warmupItems.filter(item => item.config.priority === 'nice-to-have');

    // Execute critical items in parallel
    if (criticalItems.length > 0) {
      await Promise.allSettled(criticalItems.map(item => item.fn()));
    }

    // Execute important items in parallel with slight delay
    if (importantItems.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 500));
      await Promise.allSettled(importantItems.map(item => item.fn()));
    }

    // Execute nice-to-have items in sequence to avoid overwhelming
    for (const item of niceToHaveItems) {
      try {
        await item.fn();
        // Small delay between nice-to-have items
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        // Continue with next item even if one fails
      }
    }
  }

  // Warm up data based on user behavior patterns
  async warmupBasedOnUserBehavior(userPreferences: {
    frequentlyViewedTopics?: string[];
    favoriteUsers?: string[];
    recentSearches?: string[];
  }) {
    const { frequentlyViewedTopics = [], recentSearches = [] } = userPreferences;

    // Prefetch frequently viewed topics
    if (frequentlyViewedTopics.length > 0) {
      setTimeout(async () => {
        for (const topicId of frequentlyViewedTopics.slice(0, 5)) {
          try {
            await queryClient.prefetchQuery(
              ['topic', topicId],
              () => topic.getSingleTopic(topicId),
              {
                staleTime: 1000 * 60 * 15, // 15 minutes for frequently viewed
                cacheTime: 1000 * 60 * 120, // 2 hours cache
              }
            );
          } catch (error) {
            // Ignore individual failures
          }
        }
      }, 3000);
    }

    // Prefetch recent search results
    if (recentSearches.length > 0) {
      setTimeout(async () => {
        for (const searchTerm of recentSearches.slice(0, 3)) {
          try {
            await queryClient.prefetchQuery(
              ['search', { search: searchTerm, searchType: 'topics' }],
              () => topic.search({ search: searchTerm, searchType: 'topics' }),
              {
                staleTime: 1000 * 60 * 10, // 10 minutes
                cacheTime: 1000 * 60 * 30, // 30 minutes
              }
            );
          } catch (error) {
            // Ignore individual failures
          }
        }
      }, 4000);
    }
  }

  // Clear all cached data (useful for logout or data refresh)
  async clearAllCache() {
    await queryClient.clear();
    console.log('🧹 All cache cleared');
  }

  // Get cache statistics
  getCacheStats() {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const stats = {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.state.fetchStatus === 'fetching').length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
    };

    return stats;
  }
}

// Singleton instance
export const cacheWarmingService = new CacheWarmingService();

// Convenience functions
export const warmupCriticalData = () => cacheWarmingService.warmupCriticalData();

export const warmupBasedOnUserBehavior = (userPreferences: Parameters<typeof cacheWarmingService.warmupBasedOnUserBehavior>[0]) =>
  cacheWarmingService.warmupBasedOnUserBehavior(userPreferences);

export const clearAllCache = () => cacheWarmingService.clearAllCache();

export const getCacheStats = () => cacheWarmingService.getCacheStats();
