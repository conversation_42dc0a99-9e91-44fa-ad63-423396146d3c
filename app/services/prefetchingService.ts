import { queryClient } from 'app/redux/user/hooks';
import { topic } from 'app/api/topic';
import { chat } from 'app/api/chat';
import { group } from 'app/api/group';
import { performanceMonitor } from 'app/utils/performanceMonitor';

interface PrefetchConfig {
  staleTime?: number;
  cacheTime?: number;
  priority?: 'high' | 'medium' | 'low';
}

class PrefetchingService {
  private prefetchQueue: Array<() => Promise<void>> = [];
  private isProcessing = false;
  private maxConcurrentPrefetches = 3;
  private activePrefetches = 0;

  // Prefetch topic details
  async prefetchTopic(topicId: string, config: PrefetchConfig = {}) {
    const { staleTime = 1000 * 60 * 10, cacheTime = 1000 * 60 * 60, priority = 'medium' } = config;
    
    const prefetchFn = async () => {
      try {
        performanceMonitor.startTiming(`Prefetch Topic: ${topicId}`);
        
        await queryClient.prefetchQuery(
          ['topic', topicId],
          () => topic.getSingleTopic(topicId),
          { staleTime, cacheTime }
        );
        
        performanceMonitor.endTiming(`Prefetch Topic: ${topicId}`);
      } catch (error) {
        console.warn(`Failed to prefetch topic ${topicId}:`, error);
      }
    };

    this.addToPrefetchQueue(prefetchFn, priority);
  }

  // Prefetch topic comments
  async prefetchTopicComments(topicId: string, config: PrefetchConfig = {}) {
    const { staleTime = 1000 * 60 * 5, cacheTime = 1000 * 60 * 30, priority = 'low' } = config;
    
    const prefetchFn = async () => {
      try {
        performanceMonitor.startTiming(`Prefetch Comments: ${topicId}`);
        
        await queryClient.prefetchQuery(
          ['topic-comments', topicId],
          () => topic.getTopicComments(topicId),
          { staleTime, cacheTime }
        );
        
        performanceMonitor.endTiming(`Prefetch Comments: ${topicId}`);
      } catch (error) {
        console.warn(`Failed to prefetch comments for topic ${topicId}:`, error);
      }
    };

    this.addToPrefetchQueue(prefetchFn, priority);
  }

  // Prefetch user's topics
  async prefetchUserTopics(userInfo: any, config: PrefetchConfig = {}) {
    const { staleTime = 1000 * 60 * 5, cacheTime = 1000 * 60 * 30, priority = 'medium' } = config;
    
    const prefetchFn = async () => {
      try {
        performanceMonitor.startTiming(`Prefetch User Topics: ${userInfo._id}`);
        
        await queryClient.prefetchQuery(
          ['userTopics', userInfo],
          () => topic.fetchTopic(userInfo),
          { staleTime, cacheTime }
        );
        
        performanceMonitor.endTiming(`Prefetch User Topics: ${userInfo._id}`);
      } catch (error) {
        console.warn(`Failed to prefetch user topics:`, error);
      }
    };

    this.addToPrefetchQueue(prefetchFn, priority);
  }

  // Prefetch chat list
  async prefetchChats(config: PrefetchConfig = {}) {
    const { staleTime = 1000 * 60 * 3, cacheTime = 1000 * 60 * 60, priority = 'high' } = config;
    
    const prefetchFn = async () => {
      try {
        performanceMonitor.startTiming('Prefetch Chats');
        
        await queryClient.prefetchQuery(
          ['my-chats'],
          chat.getChats,
          { staleTime, cacheTime }
        );
        
        performanceMonitor.endTiming('Prefetch Chats');
      } catch (error) {
        console.warn('Failed to prefetch chats:', error);
      }
    };

    this.addToPrefetchQueue(prefetchFn, priority);
  }

  // Prefetch group details
  async prefetchGroup(groupId: string, config: PrefetchConfig = {}) {
    const { staleTime = 1000 * 60 * 10, cacheTime = 1000 * 60 * 60, priority = 'medium' } = config;

    const prefetchFn = async () => {
      try {
        performanceMonitor.startTiming(`Prefetch Group: ${groupId}`);

        await queryClient.prefetchQuery(
          ['group', { id: groupId }],
          () => group.getGroup(groupId),
          { staleTime, cacheTime }
        );

        performanceMonitor.endTiming(`Prefetch Group: ${groupId}`);
      } catch (error) {
        console.warn(`Failed to prefetch group ${groupId}:`, error);
      }
    };

    this.addToPrefetchQueue(prefetchFn, priority);
  }

  // Prefetch user groups
  async prefetchUserGroups(userInfo: any, config: PrefetchConfig = {}) {
    const { staleTime = 1000 * 60 * 10, cacheTime = 1000 * 60 * 60, priority = 'low' } = config;

    const prefetchFn = async () => {
      try {
        performanceMonitor.startTiming(`Prefetch User Groups: ${userInfo._id}`);

        await queryClient.prefetchQuery(
          ['userGroups', userInfo],
          () => group.fetchUserGroups(userInfo),
          { staleTime, cacheTime }
        );

        performanceMonitor.endTiming(`Prefetch User Groups: ${userInfo._id}`);
      } catch (error) {
        console.warn(`Failed to prefetch user groups:`, error);
      }
    };

    this.addToPrefetchQueue(prefetchFn, priority);
  }

  // Intelligent prefetching based on user behavior
  async intelligentPrefetch(context: {
    currentScreen: string;
    visibleItems?: any[];
    userInteractions?: string[];
  }) {
    const { currentScreen, visibleItems = [], userInteractions = [] } = context;

    switch (currentScreen) {
      case 'topics':
        // Prefetch topic details for visible items
        visibleItems.slice(0, 3).forEach((item, index) => {
          if (item._id) {
            // Higher priority for first visible item
            const priority = index === 0 ? 'high' : 'medium';
            this.prefetchTopic(item._id, { priority });
            
            // Prefetch comments for first item only
            if (index === 0) {
              this.prefetchTopicComments(item._id, { priority: 'low' });
            }
          }
        });
        break;

      case 'chats':
        // Prefetch chat list if not already cached
        this.prefetchChats({ priority: 'high' });
        break;

      case 'groups':
        // Prefetch group details for visible items
        visibleItems.slice(0, 3).forEach((item, index) => {
          if (item._id) {
            const priority = index === 0 ? 'high' : 'medium';
            this.prefetchGroup(item._id, { priority });
          }
        });
        break;

      case 'profile':
        // Prefetch user's own topics and groups
        if (userInteractions.includes('viewOwnTopics')) {
          // This would need user info passed in
          // this.prefetchUserTopics(userInfo, { priority: 'medium' });
          // this.prefetchUserGroups(userInfo, { priority: 'low' });
        }
        break;
    }
  }

  // Add prefetch function to queue with priority
  private addToPrefetchQueue(prefetchFn: () => Promise<void>, priority: 'high' | 'medium' | 'low') {
    // Insert based on priority
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    const insertIndex = this.prefetchQueue.findIndex((_, index) => {
      // This is a simplified priority insertion - in reality you'd want to track priority
      return false; // For now, just append
    });

    if (insertIndex === -1) {
      this.prefetchQueue.push(prefetchFn);
    } else {
      this.prefetchQueue.splice(insertIndex, 0, prefetchFn);
    }

    this.processPrefetchQueue();
  }

  // Process the prefetch queue
  private async processPrefetchQueue() {
    if (this.isProcessing || this.activePrefetches >= this.maxConcurrentPrefetches) {
      return;
    }

    this.isProcessing = true;

    while (this.prefetchQueue.length > 0 && this.activePrefetches < this.maxConcurrentPrefetches) {
      const prefetchFn = this.prefetchQueue.shift();
      if (prefetchFn) {
        this.activePrefetches++;
        
        prefetchFn()
          .finally(() => {
            this.activePrefetches--;
            // Continue processing queue
            if (this.prefetchQueue.length > 0) {
              setTimeout(() => this.processPrefetchQueue(), 100);
            }
          });
      }
    }

    this.isProcessing = false;
  }

  // Clear prefetch queue
  clearQueue() {
    this.prefetchQueue = [];
  }

  // Get queue status
  getQueueStatus() {
    return {
      queueLength: this.prefetchQueue.length,
      activePrefetches: this.activePrefetches,
      isProcessing: this.isProcessing,
    };
  }
}

// Singleton instance
export const prefetchingService = new PrefetchingService();

// Convenience functions
export const prefetchTopic = (topicId: string, config?: PrefetchConfig) =>
  prefetchingService.prefetchTopic(topicId, config);

export const prefetchTopicComments = (topicId: string, config?: PrefetchConfig) =>
  prefetchingService.prefetchTopicComments(topicId, config);

export const prefetchUserTopics = (userInfo: any, config?: PrefetchConfig) =>
  prefetchingService.prefetchUserTopics(userInfo, config);

export const prefetchChats = (config?: PrefetchConfig) =>
  prefetchingService.prefetchChats(config);

export const intelligentPrefetch = (context: Parameters<typeof prefetchingService.intelligentPrefetch>[0]) =>
  prefetchingService.intelligentPrefetch(context);

export const prefetchGroup = (groupId: string, config?: PrefetchConfig) =>
  prefetchingService.prefetchGroup(groupId, config);

export const prefetchUserGroups = (userInfo: any, config?: PrefetchConfig) =>
  prefetchingService.prefetchUserGroups(userInfo, config);
