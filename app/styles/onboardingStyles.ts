import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { createOptimizedThemedStyles } from 'app/theme';

// Common onboarding styles that can be shared across all onboarding screens
export const createOnboardingStyles = createOptimizedThemedStyles((theme, fs) => ({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: rv(10),
  },
  keyboardContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: wp('5'),
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: wp('5'),
    paddingBottom: rv(20),
  },

  // Header styles
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: rv(10),
    marginBottom: rv(12),
  },
  headerTextContainer: {
    flex: 1,
    paddingRight: rv(10),
  },
  headerText: {
    color: theme.colors.textSecondary,
    fontSize: fs("XXXL"),
    fontFamily: 'medium',
  },
  headerImageContainer: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },

  // Content styles
  contentContainer: {
    flex: 1,
  },
  descriptionContainer: {
    marginBottom: rv(20),
  },
  descriptionText: {
    color: theme.colors.textSecondary,
    fontSize: fs("MD"),
    fontFamily: 'medium',
    lineHeight: fs("MD") * 1.4,
  },

  // Field styles
  fieldContainer: {
    marginBottom: rv(20),
  },
  fieldLabel: {
    color: theme.colors.text,
    fontSize: fs("BASE"),
    fontFamily: 'medium',
    marginBottom: rv(8),
  },
  inputField: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: rv(8),
    padding: rv(12),
    backgroundColor: theme.colors.inputBackground,
    color: theme.colors.text,
    fontSize: fs("BASE"),
    fontFamily: 'regular',
  },

  // Button styles
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: rv(40),
    paddingTop: rv(20),
  },
  buttonWrapper: {
    flex: 1,
    alignItems: 'flex-end',
  },
  nextButton: {
    width: wp('30%'),
    maxWidth: rv(118),
    borderRadius: rv(12),
  },
  backButton: {
    padding: rv(10),
  },
  backButtonText: {
    color: theme.colors.text,
    fontSize: fs("MD"),
    fontFamily: 'medium',
  },

  // Specific component styles
  dropdownTrigger: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: rv(12),
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: rv(8),
    backgroundColor: theme.colors.inputBackground,
    marginBottom: rv(8),
  },
  dropdownText: {
    fontFamily: 'medium',
    fontSize: fs("BASE"),
    color: theme.colors.text,
    flex: 1,
  },
  dropdownArrow: {
    fontSize: fs("LG"),
    color: theme.colors.textSecondary,
    marginLeft: rv(5),
  },

  // Section styles
  sectionContainer: {
    marginBottom: rv(25),
  },
  sectionLabel: {
    color: theme.colors.text,
    fontSize: fs("BASE"),
    fontFamily: 'medium',
    marginBottom: rv(12),
  },

  // Utility styles
  spacer: {
    height: rv(20),
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullWidth: {
    width: '100%',
  },
}));

// Common layout patterns for onboarding screens
export const OnboardingLayouts = {
  // Standard layout with header, description, fields, and buttons
  standard: {
    headerWithImage: true,
    descriptionSection: true,
    fieldsSection: true,
    navigationButtons: true,
  },
  
  // Simple layout without back button (for first screen)
  welcome: {
    headerWithImage: true,
    descriptionSection: true,
    fieldsSection: true,
    navigationButtons: 'next-only',
  },
  
  // Final layout with different button arrangement (for last screen)
  final: {
    headerWithImage: true,
    descriptionSection: true,
    fieldsSection: true,
    navigationButtons: 'finish',
  },
};

// Common field configurations
export const OnboardingFields = {
  text: {
    autoCapitalize: 'words',
    autoCorrect: false,
  },
  email: {
    autoCapitalize: 'none',
    autoCorrect: false,
    keyboardType: 'email-address',
  },
  multiline: {
    multiline: true,
    numberOfLines: 4,
    textAlignVertical: 'top',
  },
};
