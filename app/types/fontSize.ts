/**
 * Font size types and interfaces
 * Defines the font size scaling system types
 */

export type FontSizeScale = 'small' | 'normal' | 'large' | 'extra-large';

export interface FontSizeContextType {
  fontSizeScale: FontSizeScale;
  fontSizeMultiplier: number;
  setFontSizeScale: (scale: FontSizeScale) => void;
  scaleFontSize: (baseSize: number) => number;
  isLoading: boolean;
}

export interface FontSizeConfig {
  multipliers: Record<FontSizeScale, number>;
  labels: Record<FontSizeScale, string>;
  defaultScale: FontSizeScale;
  storageKey: string;
}
