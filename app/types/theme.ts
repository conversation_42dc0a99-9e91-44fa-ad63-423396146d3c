export type ThemeMode = 'light' | 'dark' | 'system';

export interface ThemeColors {
  // Primary brand colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  
  // Secondary colors
  secondary: string;
  secondaryLight: string;
  
  // Background colors
  background: string;
  surface: string;
  surfaceVariant: string;
  
  // Text colors
  text: string;
  textSecondary: string;
  textDisabled: string;
  textPlaceholder: string;
  
  // Interactive colors
  accent: string;
  error: string;
  warning: string;
  success: string;
  info: string;
  
  // Border and divider colors
  border: string;
  borderLight: string;
  divider: string;
  
  // Component-specific colors
  cardBackground: string;
  inputBackground: string;
  inputBorder: string;
  buttonPrimary: string;
  buttonSecondary: string;
  buttonTertiary: string;
  buttonQuaternary: string;
  buttonQuinary: string;
  
  // Navigation colors
  tabBarBackground: string;
  tabBarActive: string;
  tabBarInactive: string;
  headerBackground: string;
  headerText: string;
  
  // Status and feedback colors
  statusBarStyle: 'light-content' | 'dark-content';
  overlayBackground: string;
  shadowColor: string;
  
  // Game and interactive elements
  gameBackground: string;
  wheelColors: string[];
  spinWheelPrimary: string;
  spinWheelSecondary: string;

  // Chat bubble colors
  chatBubbleMy: string;
  chatBubbleOther: string;
  chatTextMy: string;
  chatTextOther: string;
}

export interface ThemeSpacing {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

export interface ThemeBorderRadius {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  round: number;
}

export interface ThemeShadows {
  small: {
    shadowColor: string;
    shadowOffset: { width: number; height: number };
    shadowOpacity: number;
    shadowRadius: number;
    elevation: number;
  };
  medium: {
    shadowColor: string;
    shadowOffset: { width: number; height: number };
    shadowOpacity: number;
    shadowRadius: number;
    elevation: number;
  };
  large: {
    shadowColor: string;
    shadowOffset: { width: number; height: number };
    shadowOpacity: number;
    shadowRadius: number;
    elevation: number;
  };
}

export interface Theme {
  mode: ThemeMode;
  colors: ThemeColors;
  spacing: ThemeSpacing;
  borderRadius: ThemeBorderRadius;
  shadows: ThemeShadows;
}

export interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  toggleTheme: () => void;
  setTheme: (mode: ThemeMode) => void;
  isLoading: boolean;
}
