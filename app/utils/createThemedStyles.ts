import { StyleSheet } from 'react-native';
import { Theme } from 'app/types/theme';
import { rvf } from 'app/utils/scalableResponsiveValue';

/**
 * Utility function to create themed styles
 * This function takes a style creator function that receives the theme
 * and returns a StyleSheet object
 * 
 * @param styleCreator - Function that takes theme and returns style object
 * @returns Function that takes theme and returns StyleSheet
 */
export const createThemedStyles = <T extends Record<string, any>>(
  styleCreator: (theme: Theme) => T
) => {
  return (theme: Theme) => StyleSheet.create(styleCreator(theme));
};

/**
 * Helper function to create responsive themed styles
 * Includes responsive value function for consistency
 */
export const createResponsiveThemedStyles = <T extends Record<string, any>>(
  styleCreator: (theme: Theme, rv: (value: number) => number) => T,
  responsiveValue: (value: number) => number
) => {
  return (theme: Theme) => StyleSheet.create(styleCreator(theme, responsiveValue));
};

/**
 * Utility to merge theme colors with custom overrides
 * Useful for component-specific color variations
 */
export const mergeThemeColors = (theme: Theme, overrides: Partial<Theme['colors']>) => {
  return {
    ...theme,
    colors: {
      ...theme.colors,
      ...overrides,
    },
  };
};

/**
 * Helper to get platform-specific theme values
 */
export const getPlatformThemedValue = <T>(
  theme: Theme,
  iosValue: T,
  androidValue: T,
  defaultValue: T = iosValue
): T => {
  const Platform = require('react-native').Platform;
  
  if (Platform.OS === 'ios') {
    return iosValue;
  } else if (Platform.OS === 'android') {
    return androidValue;
  }
  
  return defaultValue;
};

/**
 * Helper to create conditional styles based on theme mode
 */
export const createConditionalStyles = <T extends Record<string, any>>(
  lightStyles: T,
  darkStyles: T
) => {
  return (theme: Theme) => {
    const styles = theme.mode === 'dark' ? darkStyles : lightStyles;
    return StyleSheet.create(styles);
  };
};

/**
 * Enhanced createThemedStyles that provides both theme and scalable font size function
 * Use this when you want font sizes to automatically scale with user preference
 *
 * Example:
 * const createStyles = createScalableThemedStyles((theme, rvf) => ({
 *   title: {
 *     fontSize: rvf(18), // Will scale based on user font size setting
 *     color: theme.colors.text
 *   }
 * }));
 */
export const createScalableThemedStyles = <T extends Record<string, any>>(
  styleCreator: (theme: Theme, rvf: (size: number) => number) => T
) => {
  return (theme: Theme) => StyleSheet.create(styleCreator(theme, rvf));
};

/**
 * OPTIMIZED: Enhanced createThemedStyles with optimized font size function
 * Provides theme and high-performance font size function using pre-computed values
 *
 * This is significantly more performant than createScalableThemedStyles
 *
 * Example:
 * const createStyles = createOptimizedThemedStyles((theme, fs) => ({
 *   title: {
 *     fontSize: fs('LG'), // Pre-computed font size - much faster!
 *     color: theme.colors.text
 *   },
 *   body: {
 *     fontSize: fs('BASE'),
 *     color: theme.colors.textSecondary
 *   }
 * }));
 */
export const createOptimizedThemedStyles = <T extends Record<string, any>>(
  styleCreator: (theme: Theme, fs: (sizeKey: import('app/constants/theme').FontSizeKey) => number) => T
) => {
  return (theme: Theme, fs: (sizeKey: import('app/constants/theme').FontSizeKey) => number) =>
    StyleSheet.create(styleCreator(theme, fs));
};

export default createThemedStyles;
