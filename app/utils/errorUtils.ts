/**
 * Utility functions for handling and formatting error messages and stack traces
 */

/**
 * Truncates a string to a maximum length and adds a truncation indicator
 * @param str - The string to truncate
 * @param maxLength - Maximum allowed length (default: 5000)
 * @returns Truncated string with indicator if truncated
 */
export function truncateString(str: string, maxLength: number = 5000): string {
  if (!str || typeof str !== 'string') return str;
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength) + '... [TRUNCATED]';
}

/**
 * Truncates stack traces intelligently by keeping first and last lines
 * @param stack - The stack trace string to truncate
 * @param maxLength - Maximum allowed length (default: 8000)
 * @returns Truncated stack trace with middle section indicator
 */
export function truncateStackTrace(stack: string, maxLength: number = 8000): string {
  if (!stack || typeof stack !== 'string') return stack;
  if (stack.length <= maxLength) return stack;
  
  const lines = stack.split('\n');
  if (lines.length <= 10) {
    // If few lines, just truncate the string
    return truncateString(stack, maxLength);
  }
  
  // Keep first 5 and last 5 lines, truncate middle
  const firstLines = lines.slice(0, 5).join('\n');
  const lastLines = lines.slice(-5).join('\n');
  const truncatedMiddle = `\n... [${lines.length - 10} lines truncated] ...\n`;
  
  const result = firstLines + truncatedMiddle + lastLines;
  
  // If still too long, apply string truncation
  return result.length > maxLength ? truncateString(result, maxLength) : result;
}

/**
 * Prepares error payload with truncated message and stack trace
 * @param error - Error object with message and stack properties
 * @param messageMaxLength - Maximum length for error message (default: 2000)
 * @param stackMaxLength - Maximum length for stack trace (default: 8000)
 * @returns Object with truncated message and stack
 */
export function prepareErrorPayload(
  error: any, 
  messageMaxLength: number = 2000, 
  stackMaxLength: number = 8000
): { message: string; stack: string } {
  return {
    message: truncateString(error.message || 'Unknown error', messageMaxLength),
    stack: truncateStackTrace(error.stack || 'No stack trace', stackMaxLength),
  };
}

/**
 * Prepares crash report payload with truncated fields
 * @param error - Error object
 * @param platform - Platform string ('iOS' or 'Android')
 * @param appVersion - App version string
 * @param deviceModel - Device model string
 * @param osVersion - OS version string
 * @param messageMaxLength - Maximum length for error message (default: 2000)
 * @param stackMaxLength - Maximum length for stack trace (default: 8000)
 * @returns Crash report object with truncated fields
 */
export function prepareCrashPayload(
  error: any,
  platform: string,
  appVersion: string,
  deviceModel: string,
  osVersion: string,
  messageMaxLength: number = 2000,
  stackMaxLength: number = 8000
): {
  platform: string;
  errorMessage: string;
  stackTrace: string;
  appVersion: string;
  deviceModel: string;
  osVersion: string;
} {
  return {
    platform,
    errorMessage: truncateString(error.message || 'Unknown error', messageMaxLength),
    stackTrace: truncateStackTrace(error.stack || 'No stack trace', stackMaxLength),
    appVersion,
    deviceModel,
    osVersion,
  };
}
