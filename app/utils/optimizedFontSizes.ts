import { responsiveValue as rv } from 'app/providers/responsive-value';
import { FONT_SIZES, FontSizeKey } from 'app/constants/theme';

// Font size scale options
export type FontSizeScale = 'small' | 'normal' | 'large' | 'extra-large';

// Font size multipliers for each scale
export const FONT_SIZE_MULTIPLIERS: Record<FontSizeScale, number> = {
  'small': 0.85,
  'normal': 1.0,
  'large': 1.15,
  'extra-large': 1.3,
};

// Pre-computed font size cache for optimal performance
// This eliminates runtime calculations and improves performance significantly
class FontSizeCache {
  private cache: Map<string, number> = new Map();
  private currentScale: FontSizeScale = 'normal';

  // Pre-compute all font sizes for a given scale
  private computeAllSizes(scale: FontSizeScale): void {
    const multiplier = FONT_SIZE_MULTIPLIERS[scale];
    
    Object.entries(FONT_SIZES).forEach(([key, baseSize]) => {
      const cacheKey = `${scale}-${key}`;
      const responsiveSize = rv(baseSize);
      const scaledSize = Math.round(responsiveSize * multiplier);
      this.cache.set(cacheKey, scaledSize);
    });
  }

  // Initialize cache with all scales
  public initialize(): void {
    Object.keys(FONT_SIZE_MULTIPLIERS).forEach(scale => {
      this.computeAllSizes(scale as FontSizeScale);
    });
  }

  // Get font size - O(1) lookup instead of O(n) calculation
  public getSize(sizeKey: FontSizeKey, scale: FontSizeScale = this.currentScale): number {
    const cacheKey = `${scale}-${sizeKey}`;
    const cachedSize = this.cache.get(cacheKey);
    
    if (cachedSize === undefined) {
      // Fallback calculation if not in cache (shouldn't happen after initialization)
      const baseSize = FONT_SIZES[sizeKey];
      const multiplier = FONT_SIZE_MULTIPLIERS[scale];
      return Math.round(rv(baseSize) * multiplier);
    }
    
    return cachedSize;
  }

  // Update current scale
  public setScale(scale: FontSizeScale): void {
    this.currentScale = scale;
  }

  // Get current scale
  public getScale(): FontSizeScale {
    return this.currentScale;
  }

  // Clear cache (useful for testing or memory management)
  public clearCache(): void {
    this.cache.clear();
  }

  // Get cache size (for debugging)
  public getCacheSize(): number {
    return this.cache.size;
  }
}

// Global font size cache instance
export const fontSizeCache = new FontSizeCache();

// Initialize cache on module load
fontSizeCache.initialize();

/**
 * Optimized font size function - replaces rvf()
 * Uses pre-computed values for maximum performance
 * 
 * @param sizeKey - Font size key from FONT_SIZES constant
 * @param scale - Optional font size scale (uses current scale if not provided)
 * @returns Pre-computed responsive font size
 * 
 * Example:
 * fontSize: fs('BASE') // Gets base font size with current user scale
 * fontSize: fs('LG', 'large') // Gets large font size with large scale
 */
export const fs = (sizeKey: FontSizeKey, scale?: FontSizeScale): number => {
  return fontSizeCache.getSize(sizeKey, scale);
};

/**
 * Get multiple font sizes at once
 * Useful for creating style objects with multiple font sizes
 */
export const getFontSizes = (
  sizeKeys: FontSizeKey[], 
  scale?: FontSizeScale
): Record<FontSizeKey, number> => {
  const result = {} as Record<FontSizeKey, number>;
  sizeKeys.forEach(key => {
    result[key] = fontSizeCache.getSize(key, scale);
  });
  return result;
};

/**
 * Create a font size object for all sizes
 * Useful for theme objects or style constants
 */
export const createFontSizeObject = (scale?: FontSizeScale): Record<FontSizeKey, number> => {
  const sizeKeys = Object.keys(FONT_SIZES) as FontSizeKey[];
  return getFontSizes(sizeKeys, scale);
};

/**
 * Update global font size scale
 * This will affect all subsequent fs() calls
 */
export const setGlobalFontScale = (scale: FontSizeScale): void => {
  fontSizeCache.setScale(scale);
};

/**
 * Get current global font scale
 */
export const getGlobalFontScale = (): FontSizeScale => {
  return fontSizeCache.getScale();
};

// Export font size constants for direct access
export { FONT_SIZES } from 'app/constants/theme';
export type { FontSizeKey } from 'app/constants/theme';
