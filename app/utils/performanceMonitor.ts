// Fallback performance implementation for React Native
const getPerformanceNow = (): number => {
  if (typeof performance !== 'undefined' && performance.now) {
    return performance.now();
  }
  // Fallback to Date.now() if performance.now() is not available
  return Date.now();
};

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = __DEV__; // Only enable in development

  // Start timing a performance metric
  startTiming(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const startTime = getPerformanceNow();
    this.metrics.set(name, {
      name,
      startTime,
      metadata,
    });
  }

  // End timing and calculate duration
  endTiming(name: string): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" was not started`);
      return null;
    }

    const endTime = getPerformanceNow();
    const duration = endTime - metric.startTime;

    // Update the metric
    metric.endTime = endTime;
    metric.duration = duration;

    // Log the result
    this.logMetric(metric);

    // Clean up
    this.metrics.delete(name);

    return duration;
  }

  // Measure a function execution time
  measure<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    if (!this.isEnabled) return fn();

    this.startTiming(name, metadata);
    try {
      const result = fn();
      this.endTiming(name);
      return result;
    } catch (error) {
      this.endTiming(name);
      throw error;
    }
  }

  // Measure async function execution time
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    if (!this.isEnabled) return fn();

    this.startTiming(name, metadata);
    try {
      const result = await fn();
      this.endTiming(name);
      return result;
    } catch (error) {
      this.endTiming(name);
      throw error;
    }
  }

  // Log performance metric
  private logMetric(metric: PerformanceMetric): void {
    const { name, duration, metadata } = metric;
    
    // Color code based on performance
    let color = '🟢'; // Green for good performance
    if (duration! > 1000) color = '🔴'; // Red for slow
    else if (duration! > 500) color = '🟡'; // Yellow for moderate

    console.log(
      `${color} Performance: ${name} took ${duration?.toFixed(2)}ms`,
      metadata ? metadata : ''
    );

    // Log warning for slow operations
    if (duration! > 1000) {
      console.warn(`⚠️ Slow operation detected: ${name} took ${duration?.toFixed(2)}ms`);
    }
  }

  // Get all current metrics (for debugging)
  getCurrentMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  // Clear all metrics
  clear(): void {
    this.metrics.clear();
  }

  // Enable/disable monitoring
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Convenience functions
export const startTiming = (name: string, metadata?: Record<string, any>) =>
  performanceMonitor.startTiming(name, metadata);

export const endTiming = (name: string) => performanceMonitor.endTiming(name);

export const measure = <T>(name: string, fn: () => T, metadata?: Record<string, any>) =>
  performanceMonitor.measure(name, fn, metadata);

export const measureAsync = <T>(
  name: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
) => performanceMonitor.measureAsync(name, fn, metadata);

// React Query performance wrapper
export const measureQuery = (queryKey: string, queryFn: () => any) => {
  return () => measureAsync(`Query: ${queryKey}`, queryFn);
};

// Navigation performance tracking
export const trackNavigation = (screenName: string) => {
  startTiming(`Navigation to ${screenName}`);
  
  // Return a function to end timing when screen is ready
  return () => endTiming(`Navigation to ${screenName}`);
};

// API call performance tracking
export const trackApiCall = (endpoint: string, method: string = 'GET') => {
  const metricName = `API: ${method} ${endpoint}`;
  startTiming(metricName, { endpoint, method });
  
  return () => endTiming(metricName);
};
