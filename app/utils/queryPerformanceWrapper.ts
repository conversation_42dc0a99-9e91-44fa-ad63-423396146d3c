import { useQuery, useMutation, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { performanceMonitor } from './performanceMonitor';

// Enhanced useQuery with performance monitoring
export function usePerformantQuery<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(
  queryKey: any[],
  queryFn: () => Promise<TQueryFnData>,
  options?: Omit<UseQueryOptions<TQueryFnData, TError, TData>, 'queryKey' | 'queryFn'>
) {
  const queryKeyString = Array.isArray(queryKey) ? queryKey.join('-') : String(queryKey);
  
  const wrappedQueryFn = async () => {
    const metricName = `Query: ${queryKeyString}`;
    performanceMonitor.startTiming(metricName, { queryKey });
    
    try {
      const result = await queryFn();
      performanceMonitor.endTiming(metricName);
      return result;
    } catch (error) {
      performanceMonitor.endTiming(metricName);
      throw error;
    }
  };

  return useQuery(queryKey, wrappedQueryFn, {
    ...options,
    // Add performance-optimized defaults if not specified
    staleTime: options?.staleTime ?? 1000 * 60 * 5, // 5 minutes default
    cacheTime: options?.cacheTime ?? 1000 * 60 * 30, // 30 minutes default
    refetchOnWindowFocus: options?.refetchOnWindowFocus ?? false,
    refetchOnMount: options?.refetchOnMount ?? true,
  });
}

// Enhanced useMutation with performance monitoring
export function usePerformantMutation<TData = unknown, TError = unknown, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: UseMutationOptions<TData, TError, TVariables>
) {
  const wrappedMutationFn = async (variables: TVariables) => {
    const metricName = `Mutation: ${mutationFn.name || 'anonymous'}`;
    performanceMonitor.startTiming(metricName, { variables });
    
    try {
      const result = await mutationFn(variables);
      performanceMonitor.endTiming(metricName);
      return result;
    } catch (error) {
      performanceMonitor.endTiming(metricName);
      throw error;
    }
  };

  return useMutation(wrappedMutationFn, options);
}

// Query performance analytics
export class QueryPerformanceAnalytics {
  private static queryTimes: Map<string, number[]> = new Map();

  static recordQueryTime(queryKey: string, duration: number): void {
    if (!this.queryTimes.has(queryKey)) {
      this.queryTimes.set(queryKey, []);
    }
    
    const times = this.queryTimes.get(queryKey)!;
    times.push(duration);
    
    // Keep only last 10 measurements to avoid memory bloat
    if (times.length > 10) {
      times.shift();
    }
  }

  static getAverageQueryTime(queryKey: string): number | null {
    const times = this.queryTimes.get(queryKey);
    if (!times || times.length === 0) return null;
    
    const sum = times.reduce((acc, time) => acc + time, 0);
    return sum / times.length;
  }

  static getQueryStats(queryKey: string): {
    average: number;
    min: number;
    max: number;
    count: number;
  } | null {
    const times = this.queryTimes.get(queryKey);
    if (!times || times.length === 0) return null;
    
    return {
      average: times.reduce((acc, time) => acc + time, 0) / times.length,
      min: Math.min(...times),
      max: Math.max(...times),
      count: times.length,
    };
  }

  static getAllStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [queryKey, times] of this.queryTimes.entries()) {
      if (times.length > 0) {
        stats[queryKey] = this.getQueryStats(queryKey);
      }
    }
    
    return stats;
  }

  static logPerformanceReport(): void {
    const stats = this.getAllStats();
    
    console.group('📊 Query Performance Report');
    
    Object.entries(stats).forEach(([queryKey, stat]) => {
      const { average, min, max, count } = stat;
      const color = average < 500 ? '🟢' : average < 1000 ? '🟡' : '🔴';
      
      console.log(
        `${color} ${queryKey}: avg ${average.toFixed(2)}ms (${min.toFixed(2)}-${max.toFixed(2)}ms, ${count} calls)`
      );
    });
    
    console.groupEnd();
  }

  static clear(): void {
    this.queryTimes.clear();
  }
}

// Hook to get query performance stats
export function useQueryPerformanceStats(queryKey: string) {
  return QueryPerformanceAnalytics.getQueryStats(queryKey);
}

// Development helper to log performance periodically
if (__DEV__) {
  // Log performance report every 2 minutes in development
  setInterval(() => {
    QueryPerformanceAnalytics.logPerformanceReport();
  }, 2 * 60 * 1000);
}
