import { responsiveValue as rv } from 'app/providers/responsive-value';

/**
 * Creates a scalable responsive value function that applies font size scaling
 * This allows you to use rv() values that automatically scale with user font size preference
 */

// Global font size multiplier - will be updated by FontSizeProvider
let globalFontSizeMultiplier = 1.0;

/**
 * Set the global font size multiplier
 * Called by FontSizeProvider when font size changes
 */
export const setGlobalFontSizeMultiplier = (multiplier: number) => {
  globalFontSizeMultiplier = multiplier;
};

/**
 * Get the current global font size multiplier
 */
export const getGlobalFontSizeMultiplier = () => globalFontSizeMultiplier;

/**
 * Scalable responsive value for font sizes
 * Use this instead of rv() for font sizes that should scale with user preference
 * 
 * Example:
 * fontSize: rvf(14) // Will scale from rv(10) to rv(16) based on user setting
 */
export const rvf = (size: number): number => {
  const baseResponsiveSize = rv(size);
  return Math.round(baseResponsiveSize * globalFontSizeMultiplier);
};

/**
 * Create a themed styles function that automatically applies font size scaling
 * Use this in your createThemedStyles calls
 */
export const createScalableThemedStyles = <T extends Record<string, any>>(
  styleCreator: (theme: any, rvf: (size: number) => number) => T
) => {
  return (theme: any) => {
    return styleCreator(theme, rvf);
  };
};

/**
 * Hook to get scalable responsive value function
 * Use this in components that need to create dynamic styles
 */
export const useScalableRV = () => {
  return rvf;
};
