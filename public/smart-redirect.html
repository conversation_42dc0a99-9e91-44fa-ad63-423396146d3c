<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Opening Connectify...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            text-align: center;
        }
        .container {
            max-width: 400px;
            padding: 40px 20px;
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .message {
            font-size: 18px;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .fallback {
            margin-top: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            border: 2px solid rgba(255,255,255,0.3);
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📱 Connectify</div>
        <div class="spinner"></div>
        <div class="message">Opening the app...</div>
        <div class="fallback" id="fallback" style="display: none;">
            <p>Having trouble? Try these options:</p>
            <a href="#" id="appLink" class="btn">Open App</a>
            <a href="https://pennytot.app" class="btn">Visit Website</a>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const appUrl = urlParams.get('app_url');
        const path = window.location.pathname.replace('/smart-redirect', '');
        
        // Detect mobile device
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        
        // Function to attempt app opening
        function openApp() {
            if (appUrl) {
                console.log('Attempting to open app with URL:', appUrl);
                
                if (isMobile) {
                    // Try to open the app
                    window.location.href = appUrl;
                    
                    // Show fallback after a delay
                    setTimeout(() => {
                        document.getElementById('fallback').style.display = 'block';
                        document.querySelector('.message').textContent = 'App not installed?';
                        document.getElementById('appLink').href = appUrl;
                    }, 2000);
                } else {
                    // Desktop - show options immediately
                    document.querySelector('.spinner').style.display = 'none';
                    document.querySelector('.message').textContent = 'Choose how to continue:';
                    document.getElementById('fallback').style.display = 'block';
                    document.getElementById('appLink').href = appUrl;
                }
            } else {
                // No app URL provided, redirect to website
                window.location.href = 'https://pennytot.app' + path;
            }
        }

        // Start the redirect process
        openApp();

        // Handle page visibility change (when user returns from app)
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // User returned to browser, show fallback options
                setTimeout(() => {
                    document.getElementById('fallback').style.display = 'block';
                    document.querySelector('.message').textContent = 'Continue in browser?';
                }, 500);
            }
        });
    </script>
</body>
</html>
