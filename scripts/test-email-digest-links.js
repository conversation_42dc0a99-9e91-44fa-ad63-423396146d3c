/**
 * Test script for email digest deep linking
 * Run with: node scripts/test-email-digest-links.js
 */

require('dotenv').config();

// Test environment variables
function testEnvironmentVariables() {
  console.log('🔧 Checking Environment Variables:\n');
  
  const requiredVars = [
    'BASE_URL',
    'WEB_BASE_URL', 
    'APP_BASE_URL'
  ];

  const optionalVars = [
    'EMAIL_HOST',
    'EMAIL_USERNAME',
    'FROM_EMAIL'
  ];

  console.log('Required variables:');
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    const status = value ? '✅' : '❌';
    console.log(`   ${status} ${varName}: ${value || 'NOT SET'}`);
  });

  console.log('\nOptional variables:');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    const status = value ? '✅' : '⚠️';
    console.log(`   ${status} ${varName}: ${value ? 'SET' : 'NOT SET'}`);
  });

  console.log('');
}

// Test URL generation manually (without requiring digestService)
function testUrlGeneration() {
  console.log('🧪 Testing URL Generation\n');

  const baseWebUrl = process.env.WEB_BASE_URL || 'https://pennytot.app';
  const appScheme = 'connectify://';
  
  // Test smart link generation
  function generateSmartLink(path) {
    const appSchemeUrl = `${appScheme}${path.startsWith('/') ? path.slice(1) : path}`;
    const tracking = 'utm_source=email_digest&utm_medium=email&utm_campaign=weekly_digest';
    return `${baseWebUrl}/smart-redirect${path}?${tracking}&app_url=${encodeURIComponent(appSchemeUrl)}`;
  }

  // Test app link generation
  function generateAppLink(path) {
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    const tracking = 'utm_source=email_digest&utm_medium=email&utm_campaign=weekly_digest';
    return `${appScheme}${cleanPath}?${tracking}`;
  }

  const testPaths = [
    '/topic/507f1f77bcf86cd799439011',
    '/chat/507f1f77bcf86cd799439012', 
    '/group/507f1f77bcf86cd799439013',
    '/quiz/507f1f77bcf86cd799439014',
    '/home'
  ];

  testPaths.forEach(path => {
    const pathType = path.split('/')[1];
    console.log(`📝 Testing ${pathType} links:`);
    console.log(`   Smart:     ${generateSmartLink(path)}`);
    console.log(`   App:       ${generateAppLink(path)}`);
    console.log('');
  });
}

// Main execution
async function main() {
  console.log('🚀 Email Digest Deep Linking Test Suite\n');
  
  testEnvironmentVariables();
  testUrlGeneration();
  
  console.log('\n🔗 Manual Testing URLs:');
  console.log('Test these URLs in different browsers and devices:');
  console.log('');
  console.log('Smart Redirect (Mobile):');
  console.log(`${process.env.WEB_BASE_URL || 'https://pennytot.app'}/smart-redirect/home?app_url=connectify://home`);
  console.log('');
  console.log('Smart Redirect (Desktop):');
  console.log(`${process.env.WEB_BASE_URL || 'https://pennytot.app'}/smart-redirect/topic/123`);
  console.log('');
  console.log('Direct App Link (Mobile only):');
  console.log('connectify://topic/123');
  console.log('');
  console.log('✅ Test completed! Check the URLs above in browsers and mobile devices.');
}

if (require.main === module) {
  main().catch(console.error);
}
