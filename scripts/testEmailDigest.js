#!/usr/bin/env node

/**
 * Test script for email digest functionality
 * Usage: node scripts/testEmailDigest.js [userId]
 */

const { sendDigestToUser } = require('../controllers/emailDigest');
const digestService = require('../services/digestService');
const { userModel } = require('../models');

async function testEmailDigest(userId) {
  try {
    console.log('🧪 Testing Email Digest Functionality');
    console.log('=====================================');
    
    if (!userId) {
      console.log('❌ Please provide a userId as argument');
      console.log('Usage: node scripts/testEmailDigest.js [userId]');
      process.exit(1);
    }
    
    // Check if user exists
    const user = await userModel.findById(userId);
    if (!user) {
      console.log(`❌ User with ID ${userId} not found`);
      process.exit(1);
    }
    
    console.log(`👤 Testing for user: ${user.first_name} ${user.last_name} (${user.email})`);
    console.log('');
    
    // Test link generation
    console.log('🔗 Testing Link Generation:');
    console.log('----------------------------');
    
    const testLinks = {
      topic: digestService.generateSmartLink('/topic/507f1f77bcf86cd799439011', {
        utm_content: 'test_topic'
      }),
      group: digestService.generateSmartLink('/group/507f1f77bcf86cd799439012', {
        utm_content: 'test_group'
      }),
      chat: digestService.generateSmartLink('/chat/507f1f77bcf86cd799439013', {
        utm_content: 'test_chat'
      }),
      appLink: digestService.generateAppLink('home', {
        utm_content: 'main_app'
      }),
      unsubscribe: digestService.generateSmartLink('/settings/email-digest', {
        utm_content: 'unsubscribe'
      })
    };
    
    Object.entries(testLinks).forEach(([type, link]) => {
      console.log(`${type}: ${link}`);
    });
    
    console.log('');
    
    // Test digest data generation
    console.log('📊 Testing Digest Data Generation:');
    console.log('----------------------------------');
    
    try {
      const digestData = await digestService.generateDigestData(userId, {
        includeFollowingPosts: true,
        includePopularPosts: true,
        includeGroupActivity: true,
        includeTrendingTopics: true,
        maxPostsPerSection: 3
      });
      
      console.log('✅ Digest data generated successfully');
      console.log('Sections:');
      Object.entries(digestData.sections).forEach(([section, data]) => {
        const count = Array.isArray(data) ? data.length : 0;
        console.log(`  - ${section}: ${count} items`);
      });
      
      console.log('');
      console.log('Links in digest:');
      console.log(`  - App Link: ${digestData.appLink}`);
      console.log(`  - Unsubscribe Link: ${digestData.unsubscribeLink}`);
      
    } catch (error) {
      console.log('❌ Error generating digest data:', error.message);
    }
    
    console.log('');
    
    // Test email sending (optional)
    const shouldSendEmail = process.argv.includes('--send-email');
    
    if (shouldSendEmail) {
      console.log('📧 Testing Email Sending:');
      console.log('-------------------------');
      
      const result = await sendDigestToUser(userId, 'test');
      
      if (result.success) {
        console.log('✅ Email sent successfully');
        console.log(`Message ID: ${result.messageId}`);
        console.log('Content Summary:', result.contentSummary);
      } else {
        console.log('❌ Email sending failed:', result.reason || result.error);
      }
    } else {
      console.log('📧 Email Sending: Skipped (use --send-email flag to test)');
    }
    
    console.log('');
    console.log('🎉 Test completed!');
    
    // Environment check
    console.log('');
    console.log('🔧 Environment Configuration:');
    console.log('-----------------------------');
    console.log(`BASE_URL: ${process.env.BASE_URL || 'Not set'}`);
    console.log(`WEB_BASE_URL: ${process.env.WEB_BASE_URL || 'Not set'}`);
    console.log(`APP_BASE_URL: ${process.env.APP_BASE_URL || 'Not set'}`);
    console.log(`EMAIL_HOST: ${process.env.EMAIL_HOST || 'Not set'}`);
    console.log(`FROM_EMAIL: ${process.env.FROM_EMAIL || 'Not set'}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
const userId = process.argv[2];
testEmailDigest(userId).then(() => {
  process.exit(0);
}).catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
