// Example Express.js route for your website
app.get('/smart-redirect/*', (req, res) => {
  const path = req.path.replace('/smart-redirect', '');
  const appUrl = req.query.app_url;
  const tracking = req.query;
  
  // Detect mobile device
  const userAgent = req.headers['user-agent'] || '';
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  
  if (isMobile && appUrl) {
    // Mobile: Try to redirect to app, fallback to website
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Opening Connectify...</title>
      </head>
      <body>
        <script>
          // Try to open app
          window.location.href = '${appUrl}';
          
          // Fallback to website after 2 seconds
          setTimeout(() => {
            window.location.href = 'https://pennytot.app${path}';
          }, 2000);
        </script>
        <p>Opening Connectify app...</p>
        <p>If the app doesn't open, <a href="https://pennytot.app${path}">click here</a></p>
      </body>
      </html>
    `);
  } else {
    // Desktop: Redirect to website
    res.redirect(`https://pennytot.app${path}`);
  }
});
